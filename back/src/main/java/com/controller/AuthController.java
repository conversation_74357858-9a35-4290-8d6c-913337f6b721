package com.controller;

import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.annotation.IgnoreAuth;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.entity.UserEntity;
import com.service.TokenService;
import com.service.UserService;
import com.utils.MD5Util;
import com.utils.R;

/**
 * 统一认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private TokenService tokenService;

    /**
     * 统一登录接口
     */
    @IgnoreAuth
    @PostMapping("/login")
    public R login(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        String password = params.get("password");
        String role = params.get("role");

        if (username == null || password == null) {
            return R.error("用户名和密码不能为空");
        }

        // 查找用户
        EntityWrapper<UserEntity> wrapper = new EntityWrapper<UserEntity>();
        wrapper.eq("username", username);

        // 如果指定了角色，则按角色过滤
        if (role != null && !role.isEmpty()) {
            if ("管理员".equals(role)) {
                wrapper.eq("role", "管理员");
            } else {
                // 对于普通用户，我们需要查询yonghu表，但现在先简化处理
                return R.error("普通用户登录暂时不支持，请使用管理员账户");
            }
        }

        UserEntity user = userService.selectOne(wrapper);

        if (user == null || !MD5Util.md5(user.getPassword()).equals(password)) {
            return R.error("用户名或密码错误");
        }

        // 暂时移除状态检查，因为数据库表中没有status字段

        // 生成token
        String token = tokenService.generateToken(user.getId(), username, "users", user.getRole());

        return R.ok().put("token", token).put("user", user);
    }

    /**
     * 用户注册
     */
    @IgnoreAuth
    @PostMapping("/register")
    public R register(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        String password = params.get("password");
        String role = params.get("role");

        if (username == null || password == null) {
            return R.error("用户名和密码不能为空");
        }

        // 检查用户名是否已存在
        if (userService.selectOne(new EntityWrapper<UserEntity>().eq("username", username)) != null) {
            return R.error("用户名已存在");
        }

        // 创建新用户
        UserEntity user = new UserEntity();
        user.setUsername(username);
        user.setPassword(password); // 注册时前端已经发送MD5加密的密码，直接存储
        user.setRole(role != null ? role : "管理员"); // 默认为管理员，与现有数据库结构一致
        user.setAddtime(new Date());

        userService.insert(user);
        return R.ok("注册成功");
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public R logout(HttpServletRequest request) {
        request.getSession().invalidate();
        return R.ok("退出成功");
    }

    /**
     * 密码重置
     */
    @IgnoreAuth
    @PostMapping("/reset-password")
    public R resetPassword(@RequestBody Map<String, String> params) {
        String username = params.get("username");

        if (username == null) {
            return R.error("用户名不能为空");
        }

        UserEntity user = userService.selectOne(new EntityWrapper<UserEntity>().eq("username", username));
        if (user == null) {
            return R.error("用户不存在");
        }

        user.setPassword("123456");
        userService.updateById(user);
        return R.ok("密码已重置为：123456");
    }
}
