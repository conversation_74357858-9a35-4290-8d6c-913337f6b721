[{"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx": "2", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts": "3", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx": "6", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx": "7", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx": "8", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts": "9", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts": "10", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx": "11", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx": "12", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx": "13", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx": "14", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx": "15", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx": "16", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx": "17", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx": "18", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx": "19", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx": "20", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx": "21", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts": "22", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts": "23", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts": "24", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts": "25", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts": "26", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts": "27", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/AdminRegister.tsx": "28", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/PermissionWrapper.tsx": "29", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ActionFeedback.tsx": "30"}, {"size": 657, "mtime": 1750308789615, "results": "31", "hashOfConfig": "32"}, {"size": 1128, "mtime": 1750311762111, "results": "33", "hashOfConfig": "32"}, {"size": 6105, "mtime": 1750308759431, "results": "34", "hashOfConfig": "32"}, {"size": 2134, "mtime": 1750309987604, "results": "35", "hashOfConfig": "32"}, {"size": 9258, "mtime": 1750316293085, "results": "36", "hashOfConfig": "32"}, {"size": 724, "mtime": 1750309965887, "results": "37", "hashOfConfig": "32"}, {"size": 1056, "mtime": 1750308619882, "results": "38", "hashOfConfig": "32"}, {"size": 10563, "mtime": 1750316355613, "results": "39", "hashOfConfig": "32"}, {"size": 2425, "mtime": 1750314068259, "results": "40", "hashOfConfig": "32"}, {"size": 2936, "mtime": 1750314598264, "results": "41", "hashOfConfig": "32"}, {"size": 19184, "mtime": 1750319088233, "results": "42", "hashOfConfig": "32"}, {"size": 14595, "mtime": 1750319664727, "results": "43", "hashOfConfig": "32"}, {"size": 19069, "mtime": 1750319926567, "results": "44", "hashOfConfig": "32"}, {"size": 12102, "mtime": 1750320227968, "results": "45", "hashOfConfig": "32"}, {"size": 321, "mtime": 1750304751954, "results": "46", "hashOfConfig": "32"}, {"size": 329, "mtime": 1750304741855, "results": "47", "hashOfConfig": "32"}, {"size": 5057, "mtime": 1750310251679, "results": "48", "hashOfConfig": "32"}, {"size": 19210, "mtime": 1750320112992, "results": "49", "hashOfConfig": "32"}, {"size": 12621, "mtime": 1750320266157, "results": "50", "hashOfConfig": "32"}, {"size": 13578, "mtime": 1750318060736, "results": "51", "hashOfConfig": "32"}, {"size": 18609, "mtime": 1750320180167, "results": "52", "hashOfConfig": "32"}, {"size": 926, "mtime": 1750310069298, "results": "53", "hashOfConfig": "32"}, {"size": 1524, "mtime": 1750310090505, "results": "54", "hashOfConfig": "32"}, {"size": 1496, "mtime": 1750310154341, "results": "55", "hashOfConfig": "32"}, {"size": 1310, "mtime": 1750310172532, "results": "56", "hashOfConfig": "32"}, {"size": 1353, "mtime": 1750310192648, "results": "57", "hashOfConfig": "32"}, {"size": 1123, "mtime": 1750310211438, "results": "58", "hashOfConfig": "32"}, {"size": 5937, "mtime": 1750313524552, "results": "59", "hashOfConfig": "32"}, {"size": 4740, "mtime": 1750319464930, "results": "60", "hashOfConfig": "32"}, {"size": 8134, "mtime": 1750316813400, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tyov95", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx", ["152", "153"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx", ["154", "155", "156", "157", "158"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx", ["159", "160", "161", "162", "163", "164", "165", "166", "167", "168"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx", ["169"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx", ["170"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx", ["171", "172", "173", "174", "175", "176", "177", "178", "179"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx", ["180"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx", ["181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx", ["194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/AdminRegister.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/PermissionWrapper.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ActionFeedback.tsx", [], [], {"ruleId": "205", "severity": 1, "message": "206", "line": 1, "column": 27, "nodeType": "207", "messageId": "208", "endLine": 1, "endColumn": 36}, {"ruleId": "205", "severity": 1, "message": "209", "line": 10, "column": 9, "nodeType": "207", "messageId": "208", "endLine": 10, "endColumn": 14}, {"ruleId": "205", "severity": 1, "message": "210", "line": 21, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 21, "endColumn": 10}, {"ruleId": "205", "severity": 1, "message": "211", "line": 49, "column": 10, "nodeType": "207", "messageId": "208", "endLine": 49, "endColumn": 20}, {"ruleId": "205", "severity": 1, "message": "212", "line": 50, "column": 10, "nodeType": "207", "messageId": "208", "endLine": 50, "endColumn": 22}, {"ruleId": "205", "severity": 1, "message": "213", "line": 51, "column": 22, "nodeType": "207", "messageId": "208", "endLine": 51, "endColumn": 35}, {"ruleId": "214", "severity": 1, "message": "215", "line": 67, "column": 6, "nodeType": "216", "endLine": 67, "endColumn": 47, "suggestions": "217"}, {"ruleId": "205", "severity": 1, "message": "218", "line": 21, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 21, "endColumn": 8}, {"ruleId": "205", "severity": 1, "message": "210", "line": 22, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 22, "endColumn": 10}, {"ruleId": "205", "severity": 1, "message": "219", "line": 23, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 23, "endColumn": 9}, {"ruleId": "205", "severity": 1, "message": "220", "line": 24, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 24, "endColumn": 11}, {"ruleId": "205", "severity": 1, "message": "221", "line": 36, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 36, "endColumn": 17}, {"ruleId": "205", "severity": 1, "message": "222", "line": 43, "column": 8, "nodeType": "207", "messageId": "208", "endLine": 43, "endColumn": 25}, {"ruleId": "205", "severity": 1, "message": "223", "line": 44, "column": 8, "nodeType": "207", "messageId": "208", "endLine": 44, "endColumn": 22}, {"ruleId": "205", "severity": 1, "message": "224", "line": 47, "column": 19, "nodeType": "207", "messageId": "208", "endLine": 47, "endColumn": 25}, {"ruleId": "205", "severity": 1, "message": "213", "line": 58, "column": 22, "nodeType": "207", "messageId": "208", "endLine": 58, "endColumn": 35}, {"ruleId": "214", "severity": 1, "message": "225", "line": 80, "column": 6, "nodeType": "216", "endLine": 80, "endColumn": 47, "suggestions": "226"}, {"ruleId": "214", "severity": 1, "message": "227", "line": 54, "column": 6, "nodeType": "216", "endLine": 54, "endColumn": 47, "suggestions": "228"}, {"ruleId": "214", "severity": 1, "message": "229", "line": 31, "column": 6, "nodeType": "216", "endLine": 31, "endColumn": 47, "suggestions": "230"}, {"ruleId": "205", "severity": 1, "message": "218", "line": 21, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 21, "endColumn": 8}, {"ruleId": "205", "severity": 1, "message": "231", "line": 22, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 22, "endColumn": 9}, {"ruleId": "205", "severity": 1, "message": "220", "line": 23, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 23, "endColumn": 11}, {"ruleId": "205", "severity": 1, "message": "210", "line": 24, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 24, "endColumn": 10}, {"ruleId": "205", "severity": 1, "message": "232", "line": 35, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 35, "endColumn": 28}, {"ruleId": "205", "severity": 1, "message": "233", "line": 40, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 40, "endColumn": 19}, {"ruleId": "205", "severity": 1, "message": "222", "line": 48, "column": 8, "nodeType": "207", "messageId": "208", "endLine": 48, "endColumn": 25}, {"ruleId": "205", "severity": 1, "message": "224", "line": 52, "column": 9, "nodeType": "207", "messageId": "208", "endLine": 52, "endColumn": 15}, {"ruleId": "214", "severity": 1, "message": "234", "line": 87, "column": 6, "nodeType": "216", "endLine": 87, "endColumn": 47, "suggestions": "235"}, {"ruleId": "214", "severity": 1, "message": "236", "line": 63, "column": 6, "nodeType": "216", "endLine": 63, "endColumn": 47, "suggestions": "237"}, {"ruleId": "205", "severity": 1, "message": "238", "line": 10, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 10, "endColumn": 10}, {"ruleId": "205", "severity": 1, "message": "239", "line": 18, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 18, "endColumn": 13}, {"ruleId": "205", "severity": 1, "message": "220", "line": 20, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 20, "endColumn": 11}, {"ruleId": "205", "severity": 1, "message": "240", "line": 28, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 28, "endColumn": 23}, {"ruleId": "205", "severity": 1, "message": "241", "line": 29, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 29, "endColumn": 22}, {"ruleId": "205", "severity": 1, "message": "242", "line": 32, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 32, "endColumn": 15}, {"ruleId": "205", "severity": 1, "message": "243", "line": 33, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 33, "endColumn": 15}, {"ruleId": "205", "severity": 1, "message": "244", "line": 34, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 34, "endColumn": 19}, {"ruleId": "205", "severity": 1, "message": "245", "line": 36, "column": 8, "nodeType": "207", "messageId": "208", "endLine": 36, "endColumn": 13}, {"ruleId": "205", "severity": 1, "message": "246", "line": 41, "column": 22, "nodeType": "207", "messageId": "208", "endLine": 41, "endColumn": 31}, {"ruleId": "205", "severity": 1, "message": "247", "line": 42, "column": 9, "nodeType": "207", "messageId": "208", "endLine": 42, "endColumn": 15}, {"ruleId": "205", "severity": 1, "message": "248", "line": 111, "column": 11, "nodeType": "207", "messageId": "208", "endLine": 111, "endColumn": 24}, {"ruleId": "214", "severity": 1, "message": "249", "line": 115, "column": 6, "nodeType": "216", "endLine": 115, "endColumn": 8, "suggestions": "250"}, {"ruleId": "205", "severity": 1, "message": "231", "line": 21, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 21, "endColumn": 9}, {"ruleId": "205", "severity": 1, "message": "251", "line": 22, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 22, "endColumn": 11}, {"ruleId": "205", "severity": 1, "message": "210", "line": 24, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 24, "endColumn": 10}, {"ruleId": "205", "severity": 1, "message": "243", "line": 33, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 33, "endColumn": 15}, {"ruleId": "205", "severity": 1, "message": "252", "line": 40, "column": 3, "nodeType": "207", "messageId": "208", "endLine": 40, "endColumn": 15}, {"ruleId": "205", "severity": 1, "message": "222", "line": 46, "column": 8, "nodeType": "207", "messageId": "208", "endLine": 46, "endColumn": 25}, {"ruleId": "205", "severity": 1, "message": "253", "line": 46, "column": 29, "nodeType": "207", "messageId": "208", "endLine": 46, "endColumn": 40}, {"ruleId": "205", "severity": 1, "message": "254", "line": 51, "column": 16, "nodeType": "207", "messageId": "208", "endLine": 51, "endColumn": 20}, {"ruleId": "205", "severity": 1, "message": "255", "line": 52, "column": 9, "nodeType": "207", "messageId": "208", "endLine": 52, "endColumn": 16}, {"ruleId": "205", "severity": 1, "message": "248", "line": 79, "column": 11, "nodeType": "207", "messageId": "208", "endLine": 79, "endColumn": 24}, {"ruleId": "214", "severity": 1, "message": "256", "line": 83, "column": 6, "nodeType": "216", "endLine": 83, "endColumn": 47, "suggestions": "257"}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Title' is assigned a value but never used.", "'Divider' is defined but never used.", "'searchText' is assigned a value but never used.", "'selectedRole' is assigned a value but never used.", "'setStatistics' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["258"], "'Badge' is defined but never used.", "'Upload' is defined but never used.", "'Progress' is defined but never used.", "'UploadOutlined' is defined but never used.", "'PermissionWrapper' is defined but never used.", "'ActionFeedback' is defined but never used.", "'Search' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["259"], "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["260"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["261"], "'Avatar' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'CalendarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchBorrows'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["262"], "React Hook useEffect has a missing dependency: 'fetchFines'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["263"], "'message' is defined but never used.", "'TimePicker' is defined but never used.", "'SecurityScanOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'UserOutlined' is defined but never used.", "'BellOutlined' is defined but never used.", "'DatabaseOutlined' is defined but never used.", "'dayjs' is defined but never used.", "'Paragraph' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'hasPermission' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchConfig'. Either include it or remove the dependency array.", ["264"], "'Timeline' is defined but never used.", "'SendOutlined' is defined but never used.", "'PERMISSIONS' is defined but never used.", "'Text' is assigned a value but never used.", "'TabPane' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["265"], {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, {"desc": "270", "fix": "271"}, {"desc": "272", "fix": "273"}, {"desc": "274", "fix": "275"}, {"desc": "276", "fix": "277"}, {"desc": "278", "fix": "279"}, {"desc": "280", "fix": "281"}, "Update the dependencies array to be: [fetchUsers, pagination.pageSize]", {"range": "282", "text": "283"}, "Update the dependencies array to be: [fetchBooks, pagination.pageSize]", {"range": "284", "text": "285"}, "Update the dependencies array to be: [fetchReturns, pagination.pageSize]", {"range": "286", "text": "287"}, "Update the dependencies array to be: [fetchCategories, pagination.pageSize]", {"range": "288", "text": "289"}, "Update the dependencies array to be: [fetchBorrows, pagination.pageSize]", {"range": "290", "text": "291"}, "Update the dependencies array to be: [fetchFines, pagination.pageSize]", {"range": "292", "text": "293"}, "Update the dependencies array to be: [fetchConfig]", {"range": "294", "text": "295"}, "Update the dependencies array to be: [fetchMessages, pagination.pageSize]", {"range": "296", "text": "297"}, [1518, 1559], "[fetchUsers, pagination.pageSize]", [1932, 1973], "[fetchBooks, pagination.pageSize]", [1307, 1348], "[fetchReturns, pagination.pageSize]", [847, 888], "[fetchCategories, pagination.pageSize]", [2074, 2115], "[fetchBorrows, pagination.pageSize]", [1453, 1494], "[fetchFines, pagination.pageSize]", [2399, 2401], "[fetchConfig]", [2084, 2125], "[fetchMessages, pagination.pageSize]"]