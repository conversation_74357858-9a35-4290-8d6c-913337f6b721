{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { group, max } from '@antv/vendor/d3-array';\nimport { columnOf, constant, maybeColumnOf, visualColumn } from './utils/helper';\n/**\n * Group marks by channels into groups and stacking their enterDelay\n * to make marks show up groups by groups.\n * It will update enterDelay channel for each mark by its enterDuration and group.\n * @todo Support orderBy.\n * @todo Sort among groups(e.g. reverse).\n * @todo Stack enter in groups rather than between groups?\n * @todo Auto inter this statistic for scaleInY animation in stacked interval?\n * @todo All the groups shared the enterDuration?\n */\nexport const StackEnter = options => {\n  const {\n    groupBy = ['x'],\n    reducer = (I, V) => V[I[0]],\n    orderBy = null,\n    reverse = false,\n    duration\n  } = options;\n  return (I, mark) => {\n    const {\n      encode\n    } = mark;\n    // Extract group information by each specified channel,\n    // and skip if all values of channels are empty.\n    const by = Array.isArray(groupBy) ? groupBy : [groupBy];\n    const groupEntries = by.map(k => [k, columnOf(encode, k)[0]]);\n    if (groupEntries.length === 0) return [I, mark];\n    // Nest group index and flatten them in right order among timeline.\n    // [[1, 2, 3, 4, 5, 6]] ->\n    // [[1, 2, 3], [4, 5, 6]] ->\n    // [[1], [2], [3], [4], [5], [6]]\n    let groups = [I];\n    for (const [, V] of groupEntries) {\n      const newGroups = [];\n      for (const I of groups) {\n        const G = Array.from(group(I, i => V[i]).values());\n        // @todo sort by x.\n        newGroups.push(...G);\n      }\n      groups = newGroups;\n    }\n    // const {color} = encode;\n    if (orderBy) {\n      const [V] = columnOf(encode, orderBy);\n      if (V) groups.sort((I, J) => reducer(I, V) - reducer(J, V));\n      if (reverse) groups.reverse();\n    }\n    // Stack delay for each group.\n    const t = (duration || 3000) / groups.length;\n    const [ED] = duration ? [constant(I, t)] // If specified duration, generate enter duration for each.\n    : maybeColumnOf(encode, 'enterDuration', constant(I, t));\n    const [EDL] = maybeColumnOf(encode, 'enterDelay', constant(I, 0));\n    const newEnterDelay = new Array(I.length);\n    for (let i = 0, pd = 0; i < groups.length; i++) {\n      const I = groups[i];\n      const maxDuration = max(I, i => +ED[i]);\n      for (const j of I) newEnterDelay[j] = +EDL[j] + pd;\n      pd += maxDuration;\n    }\n    return [I, deepMix({}, mark, {\n      encode: {\n        enterDuration: visualColumn(ED),\n        enterDelay: visualColumn(newEnterDelay)\n      }\n    })];\n  };\n};\nStackEnter.props = {};", "map": {"version": 3, "names": ["deepMix", "group", "max", "columnOf", "constant", "maybeColumnOf", "visualColumn", "StackEnter", "options", "groupBy", "reducer", "I", "V", "orderBy", "reverse", "duration", "mark", "encode", "by", "Array", "isArray", "groupEntries", "map", "k", "length", "groups", "newGroups", "G", "from", "i", "values", "push", "sort", "J", "t", "ED", "EDL", "newEnterDelay", "pd", "maxDuration", "j", "enterDuration", "enterDelay", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/stackEnter.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { group, max } from '@antv/vendor/d3-array';\nimport { TransformComponent as TC } from '../runtime';\nimport { StackEnterTransform } from '../spec';\nimport {\n  columnOf,\n  constant,\n  maybeColumnOf,\n  visualColumn,\n} from './utils/helper';\n\nexport type StackEnterOptions = Omit<StackEnterTransform, 'type'>;\n\n/**\n * Group marks by channels into groups and stacking their enterDelay\n * to make marks show up groups by groups.\n * It will update enterDelay channel for each mark by its enterDuration and group.\n * @todo Support orderBy.\n * @todo Sort among groups(e.g. reverse).\n * @todo Stack enter in groups rather than between groups?\n * @todo Auto inter this statistic for scaleInY animation in stacked interval?\n * @todo All the groups shared the enterDuration?\n */\nexport const StackEnter: TC<StackEnterOptions> = (options) => {\n  const {\n    groupBy = ['x'],\n    reducer = (I, V) => V[I[0]],\n    orderBy = null,\n    reverse = false,\n    duration,\n  } = options;\n  return (I, mark) => {\n    const { encode } = mark;\n\n    // Extract group information by each specified channel,\n    // and skip if all values of channels are empty.\n    const by = Array.isArray(groupBy) ? groupBy : [groupBy];\n    const groupEntries = by.map((k) => [k, columnOf(encode, k)[0]]);\n    if (groupEntries.length === 0) return [I, mark];\n\n    // Nest group index and flatten them in right order among timeline.\n    // [[1, 2, 3, 4, 5, 6]] ->\n    // [[1, 2, 3], [4, 5, 6]] ->\n    // [[1], [2], [3], [4], [5], [6]]\n    let groups = [I];\n    for (const [, V] of groupEntries) {\n      const newGroups = [];\n      for (const I of groups) {\n        const G = Array.from(group(I, (i) => V[i]).values());\n        // @todo sort by x.\n        newGroups.push(...G);\n      }\n      groups = newGroups;\n    }\n\n    // const {color} = encode;\n    if (orderBy) {\n      const [V] = columnOf(encode, orderBy);\n      if (V) groups.sort((I, J) => reducer(I, V) - reducer(J, V));\n      if (reverse) groups.reverse();\n    }\n\n    // Stack delay for each group.\n    const t = (duration || 3000) / groups.length;\n    const [ED] = duration\n      ? [constant(I, t)] // If specified duration, generate enter duration for each.\n      : maybeColumnOf(encode, 'enterDuration', constant(I, t));\n    const [EDL] = maybeColumnOf(encode, 'enterDelay', constant(I, 0));\n    const newEnterDelay = new Array(I.length);\n    for (let i = 0, pd = 0; i < groups.length; i++) {\n      const I = groups[i];\n      const maxDuration = max(I, (i) => +ED[i]);\n      for (const j of I) newEnterDelay[j] = +EDL[j] + pd;\n      pd += maxDuration;\n    }\n\n    return [\n      I,\n      deepMix({}, mark, {\n        encode: {\n          enterDuration: visualColumn(ED),\n          enterDelay: visualColumn(newEnterDelay),\n        },\n      }),\n    ];\n  };\n};\n\nStackEnter.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,KAAK,EAAEC,GAAG,QAAQ,uBAAuB;AAGlD,SACEC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,YAAY,QACP,gBAAgB;AAIvB;;;;;;;;;;AAUA,OAAO,MAAMC,UAAU,GAA2BC,OAAO,IAAI;EAC3D,MAAM;IACJC,OAAO,GAAG,CAAC,GAAG,CAAC;IACfC,OAAO,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3BE,OAAO,GAAG,IAAI;IACdC,OAAO,GAAG,KAAK;IACfC;EAAQ,CACT,GAAGP,OAAO;EACX,OAAO,CAACG,CAAC,EAAEK,IAAI,KAAI;IACjB,MAAM;MAAEC;IAAM,CAAE,GAAGD,IAAI;IAEvB;IACA;IACA,MAAME,EAAE,GAAGC,KAAK,CAACC,OAAO,CAACX,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;IACvD,MAAMY,YAAY,GAAGH,EAAE,CAACI,GAAG,CAAEC,CAAC,IAAK,CAACA,CAAC,EAAEpB,QAAQ,CAACc,MAAM,EAAEM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAIF,YAAY,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,CAACb,CAAC,EAAEK,IAAI,CAAC;IAE/C;IACA;IACA;IACA;IACA,IAAIS,MAAM,GAAG,CAACd,CAAC,CAAC;IAChB,KAAK,MAAM,GAAGC,CAAC,CAAC,IAAIS,YAAY,EAAE;MAChC,MAAMK,SAAS,GAAG,EAAE;MACpB,KAAK,MAAMf,CAAC,IAAIc,MAAM,EAAE;QACtB,MAAME,CAAC,GAAGR,KAAK,CAACS,IAAI,CAAC3B,KAAK,CAACU,CAAC,EAAGkB,CAAC,IAAKjB,CAAC,CAACiB,CAAC,CAAC,CAAC,CAACC,MAAM,EAAE,CAAC;QACpD;QACAJ,SAAS,CAACK,IAAI,CAAC,GAAGJ,CAAC,CAAC;;MAEtBF,MAAM,GAAGC,SAAS;;IAGpB;IACA,IAAIb,OAAO,EAAE;MACX,MAAM,CAACD,CAAC,CAAC,GAAGT,QAAQ,CAACc,MAAM,EAAEJ,OAAO,CAAC;MACrC,IAAID,CAAC,EAAEa,MAAM,CAACO,IAAI,CAAC,CAACrB,CAAC,EAAEsB,CAAC,KAAKvB,OAAO,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,OAAO,CAACuB,CAAC,EAAErB,CAAC,CAAC,CAAC;MAC3D,IAAIE,OAAO,EAAEW,MAAM,CAACX,OAAO,EAAE;;IAG/B;IACA,MAAMoB,CAAC,GAAG,CAACnB,QAAQ,IAAI,IAAI,IAAIU,MAAM,CAACD,MAAM;IAC5C,MAAM,CAACW,EAAE,CAAC,GAAGpB,QAAQ,GACjB,CAACX,QAAQ,CAACO,CAAC,EAAEuB,CAAC,CAAC,CAAC,CAAC;IAAA,EACjB7B,aAAa,CAACY,MAAM,EAAE,eAAe,EAAEb,QAAQ,CAACO,CAAC,EAAEuB,CAAC,CAAC,CAAC;IAC1D,MAAM,CAACE,GAAG,CAAC,GAAG/B,aAAa,CAACY,MAAM,EAAE,YAAY,EAAEb,QAAQ,CAACO,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,MAAM0B,aAAa,GAAG,IAAIlB,KAAK,CAACR,CAAC,CAACa,MAAM,CAAC;IACzC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAES,EAAE,GAAG,CAAC,EAAET,CAAC,GAAGJ,MAAM,CAACD,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC9C,MAAMlB,CAAC,GAAGc,MAAM,CAACI,CAAC,CAAC;MACnB,MAAMU,WAAW,GAAGrC,GAAG,CAACS,CAAC,EAAGkB,CAAC,IAAK,CAACM,EAAE,CAACN,CAAC,CAAC,CAAC;MACzC,KAAK,MAAMW,CAAC,IAAI7B,CAAC,EAAE0B,aAAa,CAACG,CAAC,CAAC,GAAG,CAACJ,GAAG,CAACI,CAAC,CAAC,GAAGF,EAAE;MAClDA,EAAE,IAAIC,WAAW;;IAGnB,OAAO,CACL5B,CAAC,EACDX,OAAO,CAAC,EAAE,EAAEgB,IAAI,EAAE;MAChBC,MAAM,EAAE;QACNwB,aAAa,EAAEnC,YAAY,CAAC6B,EAAE,CAAC;QAC/BO,UAAU,EAAEpC,YAAY,CAAC+B,aAAa;;KAEzC,CAAC,CACH;EACH,CAAC;AACH,CAAC;AAED9B,UAAU,CAACoC,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}