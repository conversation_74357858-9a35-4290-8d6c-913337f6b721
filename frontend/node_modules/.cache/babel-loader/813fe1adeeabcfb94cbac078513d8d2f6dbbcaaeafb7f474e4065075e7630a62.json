{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { column, columnOf } from './utils/helper';\n/**\n * Add 3 constant encode for size channel.\n * This is useful for point geometry.\n */\nexport const MaybeIdentityX = () => {\n  return (I, mark) => {\n    const {\n      encode\n    } = mark;\n    const {\n      x1\n    } = encode;\n    if (x1) return [I, mark];\n    const [X] = columnOf(encode, 'x');\n    return [I, deepMix({}, mark, {\n      encode: {\n        x1: column([...X])\n      }\n    })];\n  };\n};\nMaybeIdentityX.props = {};", "map": {"version": 3, "names": ["deepMix", "column", "columnOf", "MaybeIdentityX", "I", "mark", "encode", "x1", "X", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/maybeIdentityX.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { TransformComponent as TC } from '../runtime';\nimport { column, columnOf } from './utils/helper';\n\nexport type MaybeIdentityXOptions = Record<string, never>;\n\n/**\n * Add 3 constant encode for size channel.\n * This is useful for point geometry.\n */\nexport const MaybeIdentityX: TC<MaybeIdentityXOptions> = () => {\n  return (I, mark) => {\n    const { encode } = mark;\n    const { x1 } = encode;\n    if (x1) return [I, mark];\n    const [X] = columnOf(encode, 'x');\n    return [I, deepMix({}, mark, { encode: { x1: column([...X]) } })];\n  };\n};\n\nMaybeIdentityX.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAIjD;;;;AAIA,OAAO,MAAMC,cAAc,GAA8BA,CAAA,KAAK;EAC5D,OAAO,CAACC,CAAC,EAAEC,IAAI,KAAI;IACjB,MAAM;MAAEC;IAAM,CAAE,GAAGD,IAAI;IACvB,MAAM;MAAEE;IAAE,CAAE,GAAGD,MAAM;IACrB,IAAIC,EAAE,EAAE,OAAO,CAACH,CAAC,EAAEC,IAAI,CAAC;IACxB,MAAM,CAACG,CAAC,CAAC,GAAGN,QAAQ,CAACI,MAAM,EAAE,GAAG,CAAC;IACjC,OAAO,CAACF,CAAC,EAAEJ,OAAO,CAAC,EAAE,EAAEK,IAAI,EAAE;MAAEC,MAAM,EAAE;QAAEC,EAAE,EAAEN,MAAM,CAAC,CAAC,GAAGO,CAAC,CAAC;MAAC;IAAE,CAAE,CAAC,CAAC;EACnE,CAAC;AACH,CAAC;AAEDL,cAAc,CAACM,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}