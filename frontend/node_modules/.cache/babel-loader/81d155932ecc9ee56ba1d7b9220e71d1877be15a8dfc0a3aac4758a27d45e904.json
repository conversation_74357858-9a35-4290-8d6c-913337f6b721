{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/spec/palette.ts"], "sourcesContent": ["import { PaletteComponent } from '../runtime';\n\nexport type Palette = Category10Palette | Category20Palette | CustomPalette;\n\nexport type PaletteTypes = 'category10' | 'category20' | PaletteComponent;\n\nexport type Category10Palette = {\n  type?: 'category10';\n};\n\nexport type Category20Palette = {\n  type?: 'category20';\n};\n\nexport type CustomPalette = {\n  type?: PaletteComponent;\n  [key: string]: any;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}