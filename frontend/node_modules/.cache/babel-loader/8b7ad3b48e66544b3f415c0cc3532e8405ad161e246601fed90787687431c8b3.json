{"ast": null, "code": "import { Group } from './group';\n/**\n * The GroupY transform group data by x channel, and aggregate.\n */\nexport const GroupY = (options = {}) => {\n  return Group(Object.assign(Object.assign({}, options), {\n    channels: ['y', 'color', 'series']\n  }));\n};\nGroupY.props = {};", "map": {"version": 3, "names": ["Group", "GroupY", "options", "Object", "assign", "channels", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/groupY.ts"], "sourcesContent": ["import { TransformComponent as TC } from '../runtime';\nimport { GroupYTransform } from '../spec';\nimport { Group } from './group';\n\nexport type GroupYOptions = Omit<GroupYTransform, 'type'>;\n\n/**\n * The GroupY transform group data by x channel, and aggregate.\n */\nexport const GroupY: TC<GroupYOptions> = (options = {}) => {\n  return Group({ ...options, channels: ['y', 'color', 'series'] });\n};\n\nGroupY.props = {};\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ,SAAS;AAI/B;;;AAGA,OAAO,MAAMC,MAAM,GAAsBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACxD,OAAOF,KAAK,CAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMF,OAAO;IAAEG,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ;EAAC,GAAG;AAClE,CAAC;AAEDJ,MAAM,CAACK,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}