{"ast": null, "code": "var isPrototype = require('./_isPrototype'),\n  nativeKeys = require('./_nativeKeys');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\nmodule.exports = baseKeys;", "map": {"version": 3, "names": ["isPrototype", "require", "nativeKeys", "objectProto", "Object", "prototype", "hasOwnProperty", "baseKeys", "object", "result", "key", "call", "push", "module", "exports"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/lodash/_baseKeys.js"], "sourcesContent": ["var isPrototype = require('./_isPrototype'),\n    nativeKeys = require('./_nativeKeys');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseKeys;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAI,CAACR,WAAW,CAACQ,MAAM,CAAC,EAAE;IACxB,OAAON,UAAU,CAACM,MAAM,CAAC;EAC3B;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,GAAG,IAAIN,MAAM,CAACI,MAAM,CAAC,EAAE;IAC9B,IAAIF,cAAc,CAACK,IAAI,CAACH,MAAM,EAAEE,GAAG,CAAC,IAAIA,GAAG,IAAI,aAAa,EAAE;MAC5DD,MAAM,CAACG,IAAI,CAACF,GAAG,CAAC;IAClB;EACF;EACA,OAAOD,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}