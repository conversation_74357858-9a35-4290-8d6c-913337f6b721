{"ast": null, "code": "var dfs = require(\"./dfs\");\nmodule.exports = postorder;\nfunction postorder(g, vs) {\n  return dfs(g, vs, \"post\");\n}", "map": {"version": 3, "names": ["dfs", "require", "module", "exports", "postorder", "g", "vs"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/graphlib/lib/alg/postorder.js"], "sourcesContent": ["var dfs = require(\"./dfs\");\n\nmodule.exports = postorder;\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, \"post\");\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE1BC,MAAM,CAACC,OAAO,GAAGC,SAAS;AAE1B,SAASA,SAASA,CAACC,CAAC,EAAEC,EAAE,EAAE;EACxB,OAAON,GAAG,CAACK,CAAC,EAAEC,EAAE,EAAE,MAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}