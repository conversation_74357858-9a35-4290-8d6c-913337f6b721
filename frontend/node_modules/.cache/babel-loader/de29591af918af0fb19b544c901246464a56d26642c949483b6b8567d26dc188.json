{"ast": null, "code": "import { useMemo } from 'react';\n/**\n * Get sticky column offset width\n */\nfunction useStickyOffsets(colWidths, flattenColumns, direction) {\n  var stickyOffsets = useMemo(function () {\n    var columnCount = flattenColumns.length;\n    var getOffsets = function getOffsets(startIndex, endIndex, offset) {\n      var offsets = [];\n      var total = 0;\n      for (var i = startIndex; i !== endIndex; i += offset) {\n        offsets.push(total);\n        if (flattenColumns[i].fixed) {\n          total += colWidths[i] || 0;\n        }\n      }\n      return offsets;\n    };\n    var startOffsets = getOffsets(0, columnCount, 1);\n    var endOffsets = getOffsets(columnCount - 1, -1, -1).reverse();\n    return direction === 'rtl' ? {\n      left: endOffsets,\n      right: startOffsets\n    } : {\n      left: startOffsets,\n      right: endOffsets\n    };\n  }, [colWidths, flattenColumns, direction]);\n  return stickyOffsets;\n}\nexport default useStickyOffsets;", "map": {"version": 3, "names": ["useMemo", "useStickyOffsets", "col<PERSON><PERSON><PERSON>", "flattenColumns", "direction", "stickyOffsets", "columnCount", "length", "getOffsets", "startIndex", "endIndex", "offset", "offsets", "total", "i", "push", "fixed", "startOffsets", "endOffsets", "reverse", "left", "right"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-table/es/hooks/useStickyOffsets.js"], "sourcesContent": ["import { useMemo } from 'react';\n/**\n * Get sticky column offset width\n */\nfunction useStickyOffsets(colWidths, flattenColumns, direction) {\n  var stickyOffsets = useMemo(function () {\n    var columnCount = flattenColumns.length;\n    var getOffsets = function getOffsets(startIndex, endIndex, offset) {\n      var offsets = [];\n      var total = 0;\n      for (var i = startIndex; i !== endIndex; i += offset) {\n        offsets.push(total);\n        if (flattenColumns[i].fixed) {\n          total += colWidths[i] || 0;\n        }\n      }\n      return offsets;\n    };\n    var startOffsets = getOffsets(0, columnCount, 1);\n    var endOffsets = getOffsets(columnCount - 1, -1, -1).reverse();\n    return direction === 'rtl' ? {\n      left: endOffsets,\n      right: startOffsets\n    } : {\n      left: startOffsets,\n      right: endOffsets\n    };\n  }, [colWidths, flattenColumns, direction]);\n  return stickyOffsets;\n}\nexport default useStickyOffsets;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAE;EAC9D,IAAIC,aAAa,GAAGL,OAAO,CAAC,YAAY;IACtC,IAAIM,WAAW,GAAGH,cAAc,CAACI,MAAM;IACvC,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAE;MACjE,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,KAAK,GAAG,CAAC;MACb,KAAK,IAAIC,CAAC,GAAGL,UAAU,EAAEK,CAAC,KAAKJ,QAAQ,EAAEI,CAAC,IAAIH,MAAM,EAAE;QACpDC,OAAO,CAACG,IAAI,CAACF,KAAK,CAAC;QACnB,IAAIV,cAAc,CAACW,CAAC,CAAC,CAACE,KAAK,EAAE;UAC3BH,KAAK,IAAIX,SAAS,CAACY,CAAC,CAAC,IAAI,CAAC;QAC5B;MACF;MACA,OAAOF,OAAO;IAChB,CAAC;IACD,IAAIK,YAAY,GAAGT,UAAU,CAAC,CAAC,EAAEF,WAAW,EAAE,CAAC,CAAC;IAChD,IAAIY,UAAU,GAAGV,UAAU,CAACF,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,CAAC;IAC9D,OAAOf,SAAS,KAAK,KAAK,GAAG;MAC3BgB,IAAI,EAAEF,UAAU;MAChBG,KAAK,EAAEJ;IACT,CAAC,GAAG;MACFG,IAAI,EAAEH,YAAY;MAClBI,KAAK,EAAEH;IACT,CAAC;EACH,CAAC,EAAE,CAAChB,SAAS,EAAEC,cAAc,EAAEC,SAAS,CAAC,CAAC;EAC1C,OAAOC,aAAa;AACtB;AACA,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}