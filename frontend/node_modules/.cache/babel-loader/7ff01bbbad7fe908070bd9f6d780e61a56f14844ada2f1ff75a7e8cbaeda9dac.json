{"ast": null, "code": "import { flow, transformOptions } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n  return flow(transformOptions)(params);\n}", "map": {"version": 3, "names": ["flow", "transformOptions", "adaptor", "params"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@ant-design/plots/es/core/plots/rose/adaptor.js"], "sourcesContent": ["import { flow, transformOptions } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n    return flow(transformOptions)(params);\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,gBAAgB,QAAQ,aAAa;AACpD;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAE;EAC5B,OAAOH,IAAI,CAACC,gBAAgB,CAAC,CAACE,MAAM,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}