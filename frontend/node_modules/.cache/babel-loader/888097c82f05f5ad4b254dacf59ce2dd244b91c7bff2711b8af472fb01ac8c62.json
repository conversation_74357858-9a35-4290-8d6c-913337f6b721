{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport isVisible from \"./isVisible\";\nfunction focusable(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (isVisible(node)) {\n    var nodeName = node.nodeName.toLowerCase();\n    var isFocusableElement =\n    // Focusable element\n    ['input', 'select', 'textarea', 'button'].includes(nodeName) ||\n    // Editable element\n    node.isContentEditable ||\n    // Anchor with href element\n    nodeName === 'a' && !!node.getAttribute('href');\n\n    // Get tabIndex\n    var tabIndexAttr = node.getAttribute('tabindex');\n    var tabIndexNum = Number(tabIndexAttr);\n\n    // Parse as number if validate\n    var tabIndex = null;\n    if (tabIndexAttr && !Number.isNaN(tabIndexNum)) {\n      tabIndex = tabIndexNum;\n    } else if (isFocusableElement && tabIndex === null) {\n      tabIndex = 0;\n    }\n\n    // Block focusable if disabled\n    if (isFocusableElement && node.disabled) {\n      tabIndex = null;\n    }\n    return tabIndex !== null && (tabIndex >= 0 || includePositive && tabIndex < 0);\n  }\n  return false;\n}\nexport function getFocusNodeList(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var res = _toConsumableArray(node.querySelectorAll('*')).filter(function (child) {\n    return focusable(child, includePositive);\n  });\n  if (focusable(node, includePositive)) {\n    res.unshift(node);\n  }\n  return res;\n}\nvar lastFocusElement = null;\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function saveLastFocusNode() {\n  lastFocusElement = document.activeElement;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function clearLastFocusNode() {\n  lastFocusElement = null;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function backLastFocusNode() {\n  if (lastFocusElement) {\n    try {\n      // 元素可能已经被移动了\n      lastFocusElement.focus();\n\n      /* eslint-disable no-empty */\n    } catch (e) {\n      // empty\n    }\n    /* eslint-enable no-empty */\n  }\n}\nexport function limitTabRange(node, e) {\n  if (e.keyCode === 9) {\n    var tabNodeList = getFocusNodeList(node);\n    var lastTabNode = tabNodeList[e.shiftKey ? 0 : tabNodeList.length - 1];\n    var leavingTab = lastTabNode === document.activeElement || node === document.activeElement;\n    if (leavingTab) {\n      var target = tabNodeList[e.shiftKey ? tabNodeList.length - 1 : 0];\n      target.focus();\n      e.preventDefault();\n    }\n  }\n}", "map": {"version": 3, "names": ["_toConsumableArray", "isVisible", "focusable", "node", "includePositive", "arguments", "length", "undefined", "nodeName", "toLowerCase", "isFocusableElement", "includes", "isContentEditable", "getAttribute", "tabIndexAttr", "tabIndexNum", "Number", "tabIndex", "isNaN", "disabled", "getFocusNodeList", "res", "querySelectorAll", "filter", "child", "unshift", "lastFocusElement", "saveLastFocusNode", "document", "activeElement", "clearLastFocusNode", "backLastFocusNode", "focus", "e", "limitTabRange", "keyCode", "tabNodeList", "lastTabNode", "shift<PERSON>ey", "leavingTab", "target", "preventDefault"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-util/es/Dom/focus.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport isVisible from \"./isVisible\";\nfunction focusable(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (isVisible(node)) {\n    var nodeName = node.nodeName.toLowerCase();\n    var isFocusableElement =\n    // Focusable element\n    ['input', 'select', 'textarea', 'button'].includes(nodeName) ||\n    // Editable element\n    node.isContentEditable ||\n    // Anchor with href element\n    nodeName === 'a' && !!node.getAttribute('href');\n\n    // Get tabIndex\n    var tabIndexAttr = node.getAttribute('tabindex');\n    var tabIndexNum = Number(tabIndexAttr);\n\n    // Parse as number if validate\n    var tabIndex = null;\n    if (tabIndexAttr && !Number.isNaN(tabIndexNum)) {\n      tabIndex = tabIndexNum;\n    } else if (isFocusableElement && tabIndex === null) {\n      tabIndex = 0;\n    }\n\n    // Block focusable if disabled\n    if (isFocusableElement && node.disabled) {\n      tabIndex = null;\n    }\n    return tabIndex !== null && (tabIndex >= 0 || includePositive && tabIndex < 0);\n  }\n  return false;\n}\nexport function getFocusNodeList(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var res = _toConsumableArray(node.querySelectorAll('*')).filter(function (child) {\n    return focusable(child, includePositive);\n  });\n  if (focusable(node, includePositive)) {\n    res.unshift(node);\n  }\n  return res;\n}\nvar lastFocusElement = null;\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function saveLastFocusNode() {\n  lastFocusElement = document.activeElement;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function clearLastFocusNode() {\n  lastFocusElement = null;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function backLastFocusNode() {\n  if (lastFocusElement) {\n    try {\n      // 元素可能已经被移动了\n      lastFocusElement.focus();\n\n      /* eslint-disable no-empty */\n    } catch (e) {\n      // empty\n    }\n    /* eslint-enable no-empty */\n  }\n}\nexport function limitTabRange(node, e) {\n  if (e.keyCode === 9) {\n    var tabNodeList = getFocusNodeList(node);\n    var lastTabNode = tabNodeList[e.shiftKey ? 0 : tabNodeList.length - 1];\n    var leavingTab = lastTabNode === document.activeElement || node === document.activeElement;\n    if (leavingTab) {\n      var target = tabNodeList[e.shiftKey ? tabNodeList.length - 1 : 0];\n      target.focus();\n      e.preventDefault();\n    }\n  }\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC/F,IAAIJ,SAAS,CAACE,IAAI,CAAC,EAAE;IACnB,IAAIK,QAAQ,GAAGL,IAAI,CAACK,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC1C,IAAIC,kBAAkB;IACtB;IACA,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACH,QAAQ,CAAC;IAC5D;IACAL,IAAI,CAACS,iBAAiB;IACtB;IACAJ,QAAQ,KAAK,GAAG,IAAI,CAAC,CAACL,IAAI,CAACU,YAAY,CAAC,MAAM,CAAC;;IAE/C;IACA,IAAIC,YAAY,GAAGX,IAAI,CAACU,YAAY,CAAC,UAAU,CAAC;IAChD,IAAIE,WAAW,GAAGC,MAAM,CAACF,YAAY,CAAC;;IAEtC;IACA,IAAIG,QAAQ,GAAG,IAAI;IACnB,IAAIH,YAAY,IAAI,CAACE,MAAM,CAACE,KAAK,CAACH,WAAW,CAAC,EAAE;MAC9CE,QAAQ,GAAGF,WAAW;IACxB,CAAC,MAAM,IAAIL,kBAAkB,IAAIO,QAAQ,KAAK,IAAI,EAAE;MAClDA,QAAQ,GAAG,CAAC;IACd;;IAEA;IACA,IAAIP,kBAAkB,IAAIP,IAAI,CAACgB,QAAQ,EAAE;MACvCF,QAAQ,GAAG,IAAI;IACjB;IACA,OAAOA,QAAQ,KAAK,IAAI,KAAKA,QAAQ,IAAI,CAAC,IAAIb,eAAe,IAAIa,QAAQ,GAAG,CAAC,CAAC;EAChF;EACA,OAAO,KAAK;AACd;AACA,OAAO,SAASG,gBAAgBA,CAACjB,IAAI,EAAE;EACrC,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC/F,IAAIgB,GAAG,GAAGrB,kBAAkB,CAACG,IAAI,CAACmB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,KAAK,EAAE;IAC/E,OAAOtB,SAAS,CAACsB,KAAK,EAAEpB,eAAe,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIF,SAAS,CAACC,IAAI,EAAEC,eAAe,CAAC,EAAE;IACpCiB,GAAG,CAACI,OAAO,CAACtB,IAAI,CAAC;EACnB;EACA,OAAOkB,GAAG;AACZ;AACA,IAAIK,gBAAgB,GAAG,IAAI;;AAE3B;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClCD,gBAAgB,GAAGE,QAAQ,CAACC,aAAa;AAC3C;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACnCJ,gBAAgB,GAAG,IAAI;AACzB;;AAEA;AACA,OAAO,SAASK,iBAAiBA,CAAA,EAAG;EAClC,IAAIL,gBAAgB,EAAE;IACpB,IAAI;MACF;MACAA,gBAAgB,CAACM,KAAK,CAAC,CAAC;;MAExB;IACF,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;IAAA;IAEF;EACF;AACF;AACA,OAAO,SAASC,aAAaA,CAAC/B,IAAI,EAAE8B,CAAC,EAAE;EACrC,IAAIA,CAAC,CAACE,OAAO,KAAK,CAAC,EAAE;IACnB,IAAIC,WAAW,GAAGhB,gBAAgB,CAACjB,IAAI,CAAC;IACxC,IAAIkC,WAAW,GAAGD,WAAW,CAACH,CAAC,CAACK,QAAQ,GAAG,CAAC,GAAGF,WAAW,CAAC9B,MAAM,GAAG,CAAC,CAAC;IACtE,IAAIiC,UAAU,GAAGF,WAAW,KAAKT,QAAQ,CAACC,aAAa,IAAI1B,IAAI,KAAKyB,QAAQ,CAACC,aAAa;IAC1F,IAAIU,UAAU,EAAE;MACd,IAAIC,MAAM,GAAGJ,WAAW,CAACH,CAAC,CAACK,QAAQ,GAAGF,WAAW,CAAC9B,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjEkC,MAAM,CAACR,KAAK,CAAC,CAAC;MACdC,CAAC,CAACQ,cAAc,CAAC,CAAC;IACpB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}