{"ast": null, "code": "const initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = function (motionCls, inKeyframes, outKeyframes, duration) {\n  let sameLevel = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [\"\\n      \".concat(sameLevelPrefix).concat(motionCls, \"-enter,\\n      \").concat(sameLevelPrefix).concat(motionCls, \"-appear\\n    \")]: Object.assign(Object.assign({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [\"\".concat(sameLevelPrefix).concat(motionCls, \"-leave\")]: Object.assign(Object.assign({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [\"\\n      \".concat(sameLevelPrefix).concat(motionCls, \"-enter\").concat(motionCls, \"-enter-active,\\n      \").concat(sameLevelPrefix).concat(motionCls, \"-appear\").concat(motionCls, \"-appear-active\\n    \")]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [\"\".concat(sameLevelPrefix).concat(motionCls, \"-leave\").concat(motionCls, \"-leave-active\")]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};", "map": {"version": 3, "names": ["initMotionCommon", "duration", "animationDuration", "animationFillMode", "initMotionCommonLeave", "initMotion", "motionCls", "inKeyframes", "outKeyframes", "sameLevel", "arguments", "length", "undefined", "sameLevelPrefix", "concat", "Object", "assign", "animationPlayState", "animationName", "pointerEvents"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/style/motion/motion.js"], "sourcesContent": ["const initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = (motionCls, inKeyframes, outKeyframes, duration, sameLevel = false) => {\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [`\n      ${sameLevelPrefix}${motionCls}-enter,\n      ${sameLevelPrefix}${motionCls}-appear\n    `]: Object.assign(Object.assign({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`${sameLevelPrefix}${motionCls}-leave`]: Object.assign(Object.assign({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`\n      ${sameLevelPrefix}${motionCls}-enter${motionCls}-enter-active,\n      ${sameLevelPrefix}${motionCls}-appear${motionCls}-appear-active\n    `]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave${motionCls}-leave-active`]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,QAAQ,KAAK;EACpCC,iBAAiB,EAAED,QAAQ;EAC3BE,iBAAiB,EAAE;AACrB,CAAC,CAAC;AACF;AACA,MAAMC,qBAAqB,GAAGH,QAAQ,KAAK;EACzCC,iBAAiB,EAAED,QAAQ;EAC3BE,iBAAiB,EAAE;AACrB,CAAC,CAAC;AACF,OAAO,MAAME,UAAU,GAAG,SAAAA,CAACC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEP,QAAQ,EAAwB;EAAA,IAAtBQ,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC1F,MAAMG,eAAe,GAAGJ,SAAS,GAAG,GAAG,GAAG,EAAE;EAC5C,OAAO;IACL,YAAAK,MAAA,CACID,eAAe,EAAAC,MAAA,CAAGR,SAAS,qBAAAQ,MAAA,CAC3BD,eAAe,EAAAC,MAAA,CAAGR,SAAS,qBAC3BS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,gBAAgB,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC/DgB,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,IAAAH,MAAA,CAAID,eAAe,EAAAC,MAAA,CAAGR,SAAS,cAAWS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,qBAAqB,CAACH,QAAQ,CAAC,CAAC,EAAE;MAC1GgB,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,YAAAH,MAAA,CACID,eAAe,EAAAC,MAAA,CAAGR,SAAS,YAAAQ,MAAA,CAASR,SAAS,4BAAAQ,MAAA,CAC7CD,eAAe,EAAAC,MAAA,CAAGR,SAAS,aAAAQ,MAAA,CAAUR,SAAS,4BAC9C;MACFY,aAAa,EAAEX,WAAW;MAC1BU,kBAAkB,EAAE;IACtB,CAAC;IACD,IAAAH,MAAA,CAAID,eAAe,EAAAC,MAAA,CAAGR,SAAS,YAAAQ,MAAA,CAASR,SAAS,qBAAkB;MACjEY,aAAa,EAAEV,YAAY;MAC3BS,kBAAkB,EAAE,SAAS;MAC7BE,aAAa,EAAE;IACjB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}