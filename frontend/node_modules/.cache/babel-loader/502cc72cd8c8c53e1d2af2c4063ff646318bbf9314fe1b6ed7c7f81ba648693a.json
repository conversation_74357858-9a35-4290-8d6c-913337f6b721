{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { updateCSS, removeCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport { isBodyOverflowing } from \"./util\";\nvar UNIQUE_ID = \"rc-util-locker-\".concat(Date.now());\nvar uuid = 0;\nexport default function useScrollLocker(lock) {\n  var mergedLock = !!lock;\n  var _React$useState = React.useState(function () {\n      uuid += 1;\n      return \"\".concat(UNIQUE_ID, \"_\").concat(uuid);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  useLayoutEffect(function () {\n    if (mergedLock) {\n      var scrollbarSize = getTargetScrollBarSize(document.body).width;\n      var isOverflow = isBodyOverflowing();\n      updateCSS(\"\\nhtml body {\\n  overflow-y: hidden;\\n  \".concat(isOverflow ? \"width: calc(100% - \".concat(scrollbarSize, \"px);\") : '', \"\\n}\"), id);\n    } else {\n      removeCSS(id);\n    }\n    return function () {\n      removeCSS(id);\n    };\n  }, [mergedLock, id]);\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "updateCSS", "removeCSS", "useLayoutEffect", "getTargetScrollBarSize", "isBodyOverflowing", "UNIQUE_ID", "concat", "Date", "now", "uuid", "useScrollLocker", "lock", "mergedLock", "_React$useState", "useState", "_React$useState2", "id", "scrollbarSize", "document", "body", "width", "isOverflow"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@rc-component/portal/es/useScrollLocker.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { updateCSS, removeCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport { isBodyOverflowing } from \"./util\";\nvar UNIQUE_ID = \"rc-util-locker-\".concat(Date.now());\nvar uuid = 0;\nexport default function useScrollLocker(lock) {\n  var mergedLock = !!lock;\n  var _React$useState = React.useState(function () {\n      uuid += 1;\n      return \"\".concat(UNIQUE_ID, \"_\").concat(uuid);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  useLayoutEffect(function () {\n    if (mergedLock) {\n      var scrollbarSize = getTargetScrollBarSize(document.body).width;\n      var isOverflow = isBodyOverflowing();\n      updateCSS(\"\\nhtml body {\\n  overflow-y: hidden;\\n  \".concat(isOverflow ? \"width: calc(100% - \".concat(scrollbarSize, \"px);\") : '', \"\\n}\"), id);\n    } else {\n      removeCSS(id);\n    }\n    return function () {\n      removeCSS(id);\n    };\n  }, [mergedLock, id]);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,SAAS,QAAQ,2BAA2B;AAChE,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,IAAIC,SAAS,GAAG,iBAAiB,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AACpD,IAAIC,IAAI,GAAG,CAAC;AACZ,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC5C,IAAIC,UAAU,GAAG,CAAC,CAACD,IAAI;EACvB,IAAIE,eAAe,GAAGd,KAAK,CAACe,QAAQ,CAAC,YAAY;MAC7CL,IAAI,IAAI,CAAC;MACT,OAAO,EAAE,CAACH,MAAM,CAACD,SAAS,EAAE,GAAG,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC;IAC/C,CAAC,CAAC;IACFM,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,EAAE,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC1Bb,eAAe,CAAC,YAAY;IAC1B,IAAIU,UAAU,EAAE;MACd,IAAIK,aAAa,GAAGd,sBAAsB,CAACe,QAAQ,CAACC,IAAI,CAAC,CAACC,KAAK;MAC/D,IAAIC,UAAU,GAAGjB,iBAAiB,CAAC,CAAC;MACpCJ,SAAS,CAAC,0CAA0C,CAACM,MAAM,CAACe,UAAU,GAAG,qBAAqB,CAACf,MAAM,CAACW,aAAa,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,EAAED,EAAE,CAAC;IAChJ,CAAC,MAAM;MACLf,SAAS,CAACe,EAAE,CAAC;IACf;IACA,OAAO,YAAY;MACjBf,SAAS,CAACe,EAAE,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACJ,UAAU,EAAEI,EAAE,CAAC,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}