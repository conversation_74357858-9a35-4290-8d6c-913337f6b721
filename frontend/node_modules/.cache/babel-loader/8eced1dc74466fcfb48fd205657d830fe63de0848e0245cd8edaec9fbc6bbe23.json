{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { deepMix } from '@antv/util';\nimport { column, columnOf, maybeColumnOf } from './utils/helper';\nimport { createGroups, normalizeComparator, applyOrder, domainOf } from './utils/order';\n/**\n * The dodge group marks into series by color or series channel,\n * and then produce new series channel for each series by specified order,\n * say to form horizontal \"columns\" by specified channels.\n */\nexport const DodgeX = (options = {}) => {\n  const {\n      groupBy = 'x',\n      reverse = false,\n      orderBy,\n      padding\n    } = options,\n    rest = __rest(options, [\"groupBy\", \"reverse\", \"orderBy\", \"padding\"]);\n  return (I, mark) => {\n    const {\n      data,\n      encode,\n      scale\n    } = mark;\n    const {\n      series: scaleSeries\n    } = scale;\n    const [Y] = columnOf(encode, 'y');\n    const [S] = maybeColumnOf(encode, 'series', 'color');\n    const domainSeries = domainOf(S, scaleSeries);\n    const newMark = deepMix({}, mark, {\n      scale: {\n        series: {\n          domain: domainSeries,\n          paddingInner: padding\n        }\n      }\n    });\n    // Create groups and apply specified order for each group.\n    const groups = createGroups(groupBy, I, mark);\n    const createComparator = normalizeComparator(orderBy);\n    if (!createComparator) {\n      return [I, deepMix(newMark, {\n        encode: {\n          series: column(S)\n        }\n      })];\n    }\n    // Sort and Update series for each mark related to series domain.\n    const comparator = createComparator(data, Y, S);\n    if (comparator) applyOrder(groups, comparator);\n    const newS = new Array(I.length);\n    for (const G of groups) {\n      if (reverse) G.reverse();\n      for (let i = 0; i < G.length; i++) {\n        newS[G[i]] = domainSeries[i];\n      }\n    }\n    return [I, deepMix(newMark, {\n      encode: {\n        series: column(orderBy ? newS : S)\n      }\n    })];\n  };\n};\nDodgeX.props = {};", "map": {"version": 3, "names": ["deepMix", "column", "columnOf", "maybeColumnOf", "createGroups", "normalizeComparator", "applyOrder", "domainOf", "DodgeX", "options", "groupBy", "reverse", "orderBy", "padding", "rest", "__rest", "I", "mark", "data", "encode", "scale", "series", "scaleSeries", "Y", "S", "domainSeries", "newMark", "domain", "paddingInner", "groups", "createComparator", "comparator", "newS", "Array", "length", "G", "i", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/dodgeX.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { TransformComponent as TC } from '../runtime';\nimport { DodgeXTransform } from '../spec';\nimport { column, columnOf, maybeColumnOf } from './utils/helper';\nimport {\n  createGroups,\n  normalizeComparator,\n  applyOrder,\n  domainOf,\n} from './utils/order';\n\nexport type DodgeXOptions = Omit<DodgeXTransform, 'type'>;\n\n/**\n * The dodge group marks into series by color or series channel,\n * and then produce new series channel for each series by specified order,\n * say to form horizontal \"columns\" by specified channels.\n */\nexport const DodgeX: TC<DodgeXOptions> = (options = {}) => {\n  const { groupBy = 'x', reverse = false, orderBy, padding, ...rest } = options;\n  return (I, mark) => {\n    const { data, encode, scale } = mark;\n    const { series: scaleSeries } = scale;\n    const [Y] = columnOf(encode, 'y');\n    const [S] = maybeColumnOf(encode, 'series', 'color');\n    const domainSeries = domainOf(S, scaleSeries);\n    const newMark = deepMix({}, mark, {\n      scale: {\n        series: {\n          domain: domainSeries,\n          paddingInner: padding,\n        },\n      },\n    });\n\n    // Create groups and apply specified order for each group.\n    const groups = createGroups(groupBy, I, mark);\n    const createComparator = normalizeComparator(orderBy);\n\n    if (!createComparator) {\n      return [I, deepMix(newMark, { encode: { series: column(S) } })];\n    }\n\n    // Sort and Update series for each mark related to series domain.\n    const comparator = createComparator(data, Y, S);\n    if (comparator) applyOrder(groups, comparator);\n    const newS = new Array(I.length);\n    for (const G of groups) {\n      if (reverse) G.reverse();\n      for (let i = 0; i < G.length; i++) {\n        newS[G[i]] = domainSeries[i];\n      }\n    }\n\n    return [\n      I,\n      deepMix(newMark, {\n        encode: {\n          series: column(orderBy ? newS : S),\n        },\n      }),\n    ];\n  };\n};\n\nDodgeX.props = {};\n"], "mappings": ";;;;;;;;AAAA,SAASA,OAAO,QAAQ,YAAY;AAGpC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,gBAAgB;AAChE,SACEC,YAAY,EACZC,mBAAmB,EACnBC,UAAU,EACVC,QAAQ,QACH,eAAe;AAItB;;;;;AAKA,OAAO,MAAMC,MAAM,GAAsBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACxD,MAAM;MAAEC,OAAO,GAAG,GAAG;MAAEC,OAAO,GAAG,KAAK;MAAEC,OAAO;MAAEC;IAAO,IAAcJ,OAAO;IAAhBK,IAAI,GAAAC,MAAA,CAAKN,OAAO,EAAvE,4CAA6D,CAAU;EAC7E,OAAO,CAACO,CAAC,EAAEC,IAAI,KAAI;IACjB,MAAM;MAAEC,IAAI;MAAEC,MAAM;MAAEC;IAAK,CAAE,GAAGH,IAAI;IACpC,MAAM;MAAEI,MAAM,EAAEC;IAAW,CAAE,GAAGF,KAAK;IACrC,MAAM,CAACG,CAAC,CAAC,GAAGrB,QAAQ,CAACiB,MAAM,EAAE,GAAG,CAAC;IACjC,MAAM,CAACK,CAAC,CAAC,GAAGrB,aAAa,CAACgB,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IACpD,MAAMM,YAAY,GAAGlB,QAAQ,CAACiB,CAAC,EAAEF,WAAW,CAAC;IAC7C,MAAMI,OAAO,GAAG1B,OAAO,CAAC,EAAE,EAAEiB,IAAI,EAAE;MAChCG,KAAK,EAAE;QACLC,MAAM,EAAE;UACNM,MAAM,EAAEF,YAAY;UACpBG,YAAY,EAAEf;;;KAGnB,CAAC;IAEF;IACA,MAAMgB,MAAM,GAAGzB,YAAY,CAACM,OAAO,EAAEM,CAAC,EAAEC,IAAI,CAAC;IAC7C,MAAMa,gBAAgB,GAAGzB,mBAAmB,CAACO,OAAO,CAAC;IAErD,IAAI,CAACkB,gBAAgB,EAAE;MACrB,OAAO,CAACd,CAAC,EAAEhB,OAAO,CAAC0B,OAAO,EAAE;QAAEP,MAAM,EAAE;UAAEE,MAAM,EAAEpB,MAAM,CAACuB,CAAC;QAAC;MAAE,CAAE,CAAC,CAAC;;IAGjE;IACA,MAAMO,UAAU,GAAGD,gBAAgB,CAACZ,IAAI,EAAEK,CAAC,EAAEC,CAAC,CAAC;IAC/C,IAAIO,UAAU,EAAEzB,UAAU,CAACuB,MAAM,EAAEE,UAAU,CAAC;IAC9C,MAAMC,IAAI,GAAG,IAAIC,KAAK,CAACjB,CAAC,CAACkB,MAAM,CAAC;IAChC,KAAK,MAAMC,CAAC,IAAIN,MAAM,EAAE;MACtB,IAAIlB,OAAO,EAAEwB,CAAC,CAACxB,OAAO,EAAE;MACxB,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACD,MAAM,EAAEE,CAAC,EAAE,EAAE;QACjCJ,IAAI,CAACG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAGX,YAAY,CAACW,CAAC,CAAC;;;IAIhC,OAAO,CACLpB,CAAC,EACDhB,OAAO,CAAC0B,OAAO,EAAE;MACfP,MAAM,EAAE;QACNE,MAAM,EAAEpB,MAAM,CAACW,OAAO,GAAGoB,IAAI,GAAGR,CAAC;;KAEpC,CAAC,CACH;EACH,CAAC;AACH,CAAC;AAEDhB,MAAM,CAAC6B,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}