{"ast": null, "code": "import { Graph as GraphLib } from '@antv/graphlib';\nimport { isNil, isNumber, uniq } from '@antv/util';\nimport { COMBO_KEY, ChangeType, TREE_KEY } from '../constants';\nimport { isCollapsed } from '../utils/collapsibility';\nimport { cloneElementData, isElementDataEqual, mergeElementsData } from '../utils/data';\nimport { arrayDiff } from '../utils/diff';\nimport { toG6Data, toGraphlibData } from '../utils/graphlib';\nimport { idOf, parentIdOf } from '../utils/id';\nimport { positionOf } from '../utils/position';\nimport { format, print } from '../utils/print';\nimport { dfs } from '../utils/traverse';\nimport { add } from '../utils/vector';\nexport class DataController {\n  constructor() {\n    /**\n     * <zh/> 最近一次删除的 combo 的 id\n     *\n     * <en/> The ids of the last deleted combos\n     * @remarks\n     * <zh/> 当删除 combo 后，会将其 id 从 comboIds 中移除，此时根据 Graphlib 的 changes 事件获取到的 NodeRemoved 无法区分是 combo 还是 node。\n     * 因此需要记录最近一次删除的 combo 的 id，并用于 isCombo 的判断\n     *\n     * <en/> When the combo is deleted, its id will be removed from comboIds. At this time, the NodeRemoved obtained according to the changes event of Graphlib cannot distinguish whether it is a combo or a node.\n     * Therefore, it is necessary to record the id of the last deleted combo and use it to judge isCombo\n     */\n    this.latestRemovedComboIds = new Set();\n    this.comboIds = new Set();\n    /**\n     * <zh/> 获取详细数据变更\n     *\n     * <en/> Get detailed data changes\n     */\n    this.changes = [];\n    /**\n     * <zh/> 批处理计数器\n     *\n     * <en/> Batch processing counter\n     */\n    this.batchCount = 0;\n    /**\n     * <zh/> 是否处于无痕模式\n     *\n     * <en/> Whether it is in traceless mode\n     */\n    this.isTraceless = false;\n    this.enableUpdateNodeLikeHierarchy = true;\n    this.model = new GraphLib();\n  }\n  pushChange(change) {\n    if (this.isTraceless) return;\n    const {\n      type\n    } = change;\n    if (type === ChangeType.NodeUpdated || type === ChangeType.EdgeUpdated || type === ChangeType.ComboUpdated) {\n      const {\n        value,\n        original\n      } = change;\n      this.changes.push({\n        value: cloneElementData(value),\n        original: cloneElementData(original),\n        type\n      });\n    } else {\n      this.changes.push({\n        value: cloneElementData(change.value),\n        type\n      });\n    }\n  }\n  getChanges() {\n    return this.changes;\n  }\n  clearChanges() {\n    this.changes = [];\n  }\n  batch(callback) {\n    this.batchCount++;\n    this.model.batch(callback);\n    this.batchCount--;\n  }\n  isBatching() {\n    return this.batchCount > 0;\n  }\n  /**\n   * <zh/> 执行操作而不会留下记录\n   *\n   * <en/> Perform operations without leaving records\n   * @param callback - <zh/> 回调函数 | <en/> callback function\n   * @remarks\n   * <zh/> 通常用于运行时调整元素并同步数据，避免触发数据变更导致重绘\n   *\n   * <en/> Usually used to adjust elements at runtime and synchronize data to avoid triggering data changes and causing redraws\n   */\n  silence(callback) {\n    this.isTraceless = true;\n    callback();\n    this.isTraceless = false;\n  }\n  isCombo(id) {\n    return this.comboIds.has(id) || this.latestRemovedComboIds.has(id);\n  }\n  getData() {\n    return {\n      nodes: this.getNodeData(),\n      edges: this.getEdgeData(),\n      combos: this.getComboData()\n    };\n  }\n  getNodeData(ids) {\n    return this.model.getAllNodes().reduce((acc, node) => {\n      const data = toG6Data(node);\n      if (this.isCombo(idOf(data))) return acc;\n      if (ids === undefined) acc.push(data);else ids.includes(idOf(data)) && acc.push(data);\n      return acc;\n    }, []);\n  }\n  getEdgeDatum(id) {\n    return toG6Data(this.model.getEdge(id));\n  }\n  getEdgeData(ids) {\n    return this.model.getAllEdges().reduce((acc, edge) => {\n      const data = toG6Data(edge);\n      if (ids === undefined) acc.push(data);else ids.includes(idOf(data)) && acc.push(data);\n      return acc;\n    }, []);\n  }\n  getComboData(ids) {\n    return this.model.getAllNodes().reduce((acc, combo) => {\n      const data = toG6Data(combo);\n      if (!this.isCombo(idOf(data))) return acc;\n      if (ids === undefined) acc.push(data);else ids.includes(idOf(data)) && acc.push(data);\n      return acc;\n    }, []);\n  }\n  getRootsData(hierarchyKey = TREE_KEY) {\n    return this.model.getRoots(hierarchyKey).map(toG6Data);\n  }\n  getAncestorsData(id, hierarchyKey) {\n    const {\n      model\n    } = this;\n    if (!model.hasNode(id) || !model.hasTreeStructure(hierarchyKey)) return [];\n    return model.getAncestors(id, hierarchyKey).map(toG6Data);\n  }\n  getDescendantsData(id) {\n    const root = this.getElementDataById(id);\n    const data = [];\n    dfs(root, node => {\n      if (node !== root) data.push(node);\n    }, node => this.getChildrenData(idOf(node)), 'TB');\n    return data;\n  }\n  getParentData(id, hierarchyKey) {\n    const {\n      model\n    } = this;\n    if (!hierarchyKey) {\n      print.warn('The hierarchy structure key is not specified');\n      return undefined;\n    }\n    if (!model.hasNode(id) || !model.hasTreeStructure(hierarchyKey)) return undefined;\n    const parent = model.getParent(id, hierarchyKey);\n    return parent ? toG6Data(parent) : undefined;\n  }\n  getChildrenData(id) {\n    const structureKey = this.getElementType(id) === 'node' ? TREE_KEY : COMBO_KEY;\n    const {\n      model\n    } = this;\n    if (!model.hasNode(id) || !model.hasTreeStructure(structureKey)) return [];\n    return model.getChildren(id, structureKey).map(toG6Data);\n  }\n  /**\n   * <zh/> 获取指定类型元素的数据\n   *\n   * <en/> Get the data of the specified type of element\n   * @param elementType - <zh/> 元素类型 | <en/> element type\n   * @returns <zh/> 元素数据 | <en/> element data\n   */\n  getElementsDataByType(elementType) {\n    if (elementType === 'node') return this.getNodeData();\n    if (elementType === 'edge') return this.getEdgeData();\n    if (elementType === 'combo') return this.getComboData();\n    return [];\n  }\n  /**\n   * <zh/> 根据 ID 获取元素的数据，不用关心元素的类型\n   *\n   * <en/> Get the data of the element by ID, no need to care about the type of the element\n   * @param id - <zh/> 元素 ID 数组 | <en/> element ID array\n   * @returns <zh/> 元素数据 | <en/> data of the element\n   */\n  getElementDataById(id) {\n    const type = this.getElementType(id);\n    if (type === 'edge') return this.getEdgeDatum(id);\n    return this.getNodeLikeDatum(id);\n  }\n  /**\n   * <zh/> 获取节点的数据\n   *\n   * <en/> Get node data\n   * @param id - <zh/> 节点 ID | <en/> node ID\n   * @returns <zh/> 节点数据 | <en/> node data\n   */\n  getNodeLikeDatum(id) {\n    const data = this.model.getNode(id);\n    return toG6Data(data);\n  }\n  /**\n   * <zh/> 获取所有节点和 combo 的数据\n   *\n   * <en/> Get all node and combo data\n   * @param ids - <zh/> 节点和 combo ID 数组 | <en/> node and combo ID array\n   * @returns <zh/> 节点和 combo 的数据 | <en/> node and combo data\n   */\n  getNodeLikeData(ids) {\n    return this.model.getAllNodes().reduce((acc, node) => {\n      const data = toG6Data(node);\n      if (ids) ids.includes(idOf(data)) && acc.push(data);else acc.push(data);\n      return acc;\n    }, []);\n  }\n  getElementDataByState(elementType, state) {\n    const elementData = this.getElementsDataByType(elementType);\n    return elementData.filter(datum => {\n      var _a;\n      return (_a = datum.states) === null || _a === void 0 ? void 0 : _a.includes(state);\n    });\n  }\n  getElementState(id) {\n    var _a;\n    return ((_a = this.getElementDataById(id)) === null || _a === void 0 ? void 0 : _a.states) || [];\n  }\n  hasNode(id) {\n    return this.model.hasNode(id) && !this.isCombo(id);\n  }\n  hasEdge(id) {\n    return this.model.hasEdge(id);\n  }\n  hasCombo(id) {\n    return this.model.hasNode(id) && this.isCombo(id);\n  }\n  getRelatedEdgesData(id, direction = 'both') {\n    return this.model.getRelatedEdges(id, direction).map(toG6Data);\n  }\n  getNeighborNodesData(id) {\n    return this.model.getNeighbors(id).map(toG6Data);\n  }\n  setData(data) {\n    const {\n      nodes: modifiedNodes = [],\n      edges: modifiedEdges = [],\n      combos: modifiedCombos = []\n    } = data;\n    const {\n      nodes: originalNodes,\n      edges: originalEdges,\n      combos: originalCombos\n    } = this.getData();\n    const nodeDiff = arrayDiff(originalNodes, modifiedNodes, node => idOf(node), isElementDataEqual);\n    const edgeDiff = arrayDiff(originalEdges, modifiedEdges, edge => idOf(edge), isElementDataEqual);\n    const comboDiff = arrayDiff(originalCombos, modifiedCombos, combo => idOf(combo), isElementDataEqual);\n    this.batch(() => {\n      const dataToAdd = {\n        nodes: nodeDiff.enter,\n        edges: edgeDiff.enter,\n        combos: comboDiff.enter\n      };\n      this.addData(dataToAdd);\n      this.computeZIndex(dataToAdd, 'add', true);\n      const dataToUpdate = {\n        nodes: nodeDiff.update,\n        edges: edgeDiff.update,\n        combos: comboDiff.update\n      };\n      this.updateData(dataToUpdate);\n      this.computeZIndex(dataToUpdate, 'update', true);\n      const dataToRemove = {\n        nodes: nodeDiff.exit.map(idOf),\n        edges: edgeDiff.exit.map(idOf),\n        combos: comboDiff.exit.map(idOf)\n      };\n      this.removeData(dataToRemove);\n    });\n  }\n  addData(data) {\n    const {\n      nodes,\n      edges,\n      combos\n    } = data;\n    this.batch(() => {\n      // add combo first\n      this.addComboData(combos);\n      this.addNodeData(nodes);\n      this.addEdgeData(edges);\n    });\n    this.computeZIndex(data, 'add');\n  }\n  addNodeData(nodes = []) {\n    if (!nodes.length) return;\n    this.model.addNodes(nodes.map(node => {\n      this.pushChange({\n        value: node,\n        type: ChangeType.NodeAdded\n      });\n      return toGraphlibData(node);\n    }));\n    this.updateNodeLikeHierarchy(nodes);\n    this.computeZIndex({\n      nodes\n    }, 'add');\n  }\n  addEdgeData(edges = []) {\n    if (!edges.length) return;\n    this.model.addEdges(edges.map(edge => {\n      this.pushChange({\n        value: edge,\n        type: ChangeType.EdgeAdded\n      });\n      return toGraphlibData(edge);\n    }));\n    this.computeZIndex({\n      edges\n    }, 'add');\n  }\n  addComboData(combos = []) {\n    if (!combos.length) return;\n    const {\n      model\n    } = this;\n    if (!model.hasTreeStructure(COMBO_KEY)) {\n      model.attachTreeStructure(COMBO_KEY);\n    }\n    model.addNodes(combos.map(combo => {\n      this.comboIds.add(idOf(combo));\n      this.pushChange({\n        value: combo,\n        type: ChangeType.ComboAdded\n      });\n      return toGraphlibData(combo);\n    }));\n    this.updateNodeLikeHierarchy(combos);\n    this.computeZIndex({\n      combos\n    }, 'add');\n  }\n  addChildrenData(parentId, childrenData) {\n    const parentData = this.getNodeLikeDatum(parentId);\n    const childrenId = childrenData.map(idOf);\n    this.addNodeData(childrenData);\n    this.updateNodeData([{\n      id: parentId,\n      children: [...(parentData.children || []), ...childrenId]\n    }]);\n    this.addEdgeData(childrenId.map(childId => ({\n      source: parentId,\n      target: childId\n    })));\n  }\n  /**\n   * <zh/> 计算 zIndex\n   *\n   * <en/> Calculate zIndex\n   * @param data - <zh/> 新增的数据 | <en/> newly added data\n   * @param type - <zh/> 操作类型 | <en/> operation type\n   * @param force - <zh/> 忽略批处理 | <en/> ignore batch processing\n   * @remarks\n   * <zh/> 调用该函数的情况：\n   * - 新增元素\n   * - 更新节点/组合的 combo\n   * - 更新节点的 children\n   *\n   * <en/> The situation of calling this function:\n   * - Add element\n   * - Update the combo of the node/combo\n   * - Update the children of the node\n   */\n  computeZIndex(data, type, force = false) {\n    if (!force && this.isBatching()) return;\n    this.batch(() => {\n      const {\n        nodes = [],\n        edges = [],\n        combos = []\n      } = data;\n      combos.forEach(combo => {\n        var _a, _b, _c;\n        const id = idOf(combo);\n        if (type === 'add' && isNumber((_a = combo.style) === null || _a === void 0 ? void 0 : _a.zIndex)) return;\n        if (type === 'update' && !('combo' in combo)) return;\n        const parent = this.getParentData(id, COMBO_KEY);\n        const zIndex = parent ? ((_c = (_b = parent.style) === null || _b === void 0 ? void 0 : _b.zIndex) !== null && _c !== void 0 ? _c : 0) + 1 : 0;\n        this.preventUpdateNodeLikeHierarchy(() => {\n          this.updateComboData([{\n            id,\n            style: {\n              zIndex\n            }\n          }]);\n        });\n      });\n      nodes.forEach(node => {\n        var _a, _b, _c;\n        const id = idOf(node);\n        if (type === 'add' && isNumber((_a = node.style) === null || _a === void 0 ? void 0 : _a.zIndex)) return;\n        if (type === 'update' && !('combo' in node) && !('children' in node)) return;\n        let zIndex = 0;\n        const comboParent = this.getParentData(id, COMBO_KEY);\n        if (comboParent) {\n          zIndex = (((_b = comboParent.style) === null || _b === void 0 ? void 0 : _b.zIndex) || 0) + 1;\n        } else {\n          const nodeParent = this.getParentData(id, TREE_KEY);\n          if (nodeParent) zIndex = ((_c = nodeParent === null || nodeParent === void 0 ? void 0 : nodeParent.style) === null || _c === void 0 ? void 0 : _c.zIndex) || 0;\n        }\n        this.preventUpdateNodeLikeHierarchy(() => {\n          this.updateNodeData([{\n            id,\n            style: {\n              zIndex\n            }\n          }]);\n        });\n      });\n      edges.forEach(edge => {\n        var _a, _b, _c, _d, _e;\n        if (isNumber((_a = edge.style) === null || _a === void 0 ? void 0 : _a.zIndex)) return;\n        let {\n          id,\n          source,\n          target\n        } = edge;\n        if (!id) id = idOf(edge);else {\n          const datum = this.getEdgeDatum(id);\n          source = datum.source;\n          target = datum.target;\n        }\n        if (!source || !target) return;\n        const sourceZIndex = ((_c = (_b = this.getNodeLikeDatum(source)) === null || _b === void 0 ? void 0 : _b.style) === null || _c === void 0 ? void 0 : _c.zIndex) || 0;\n        const targetZIndex = ((_e = (_d = this.getNodeLikeDatum(target)) === null || _d === void 0 ? void 0 : _d.style) === null || _e === void 0 ? void 0 : _e.zIndex) || 0;\n        this.updateEdgeData([{\n          id: idOf(edge),\n          style: {\n            zIndex: Math.max(sourceZIndex, targetZIndex) - 1\n          }\n        }]);\n      });\n    });\n  }\n  /**\n   * <zh/> 计算元素置顶后的 zIndex\n   *\n   * <en/> Calculate the zIndex after the element is placed on top\n   * @param id - <zh/> 元素 ID | <en/> ID of the element\n   * @returns <zh/> zIndex | <en/> zIndex\n   */\n  getFrontZIndex(id) {\n    var _a;\n    const elementType = this.getElementType(id);\n    const elementData = this.getElementDataById(id);\n    const data = this.getData();\n    // 排除当前元素 / Exclude the current element\n    Object.assign(data, {\n      [`${elementType}s`]: data[`${elementType}s`].filter(element => idOf(element) !== id)\n    });\n    if (elementType === 'combo') {\n      // 如果 combo 展开，则排除 combo 的子节点/combo 及内部边\n      // If the combo is expanded, exclude the child nodes/combos of the combo and the internal edges\n      if (!isCollapsed(elementData)) {\n        const ancestorIds = new Set(this.getAncestorsData(id, COMBO_KEY).map(idOf));\n        data.nodes = data.nodes.filter(element => !ancestorIds.has(idOf(element)));\n        data.combos = data.combos.filter(element => !ancestorIds.has(idOf(element)));\n        data.edges = data.edges.filter(({\n          source,\n          target\n        }) => !ancestorIds.has(source) && !ancestorIds.has(target));\n      }\n    }\n    return Math.max(((_a = elementData.style) === null || _a === void 0 ? void 0 : _a.zIndex) || 0, 0, ...Object.values(data).flat().map(datum => {\n      var _a;\n      return (((_a = datum === null || datum === void 0 ? void 0 : datum.style) === null || _a === void 0 ? void 0 : _a.zIndex) || 0) + 1;\n    }));\n  }\n  updateNodeLikeHierarchy(data) {\n    if (!this.enableUpdateNodeLikeHierarchy) return;\n    const {\n      model\n    } = this;\n    data.forEach(datum => {\n      const id = idOf(datum);\n      const parent = parentIdOf(datum);\n      if (parent !== undefined) {\n        if (!model.hasTreeStructure(COMBO_KEY)) model.attachTreeStructure(COMBO_KEY);\n        // 解除原父节点的子节点关系，更新原父节点及其祖先的数据\n        // Remove the child relationship of the original parent node, update the data of the original parent node and its ancestors\n        if (parent === null) {\n          this.refreshComboData(id);\n        }\n        this.setParent(id, parentIdOf(datum), COMBO_KEY);\n      }\n      const children = datum.children || [];\n      if (children.length) {\n        if (!model.hasTreeStructure(TREE_KEY)) model.attachTreeStructure(TREE_KEY);\n        const _children = children.filter(child => model.hasNode(child));\n        _children.forEach(child => this.setParent(child, id, TREE_KEY));\n        if (_children.length !== children.length) {\n          // 从数据中移除不存在的子节点\n          // Remove non-existent child nodes from the data\n          this.updateNodeData([{\n            id,\n            children: _children\n          }]);\n        }\n      }\n    });\n  }\n  /**\n   * <zh/> 执行变更时不要更新节点层次结构\n   *\n   * <en/> Do not update the node hierarchy when executing changes\n   * @param callback - <zh/> 变更函数 | <en/> change function\n   */\n  preventUpdateNodeLikeHierarchy(callback) {\n    this.enableUpdateNodeLikeHierarchy = false;\n    callback();\n    this.enableUpdateNodeLikeHierarchy = true;\n  }\n  updateData(data) {\n    const {\n      nodes,\n      edges,\n      combos\n    } = data;\n    this.batch(() => {\n      this.updateNodeData(nodes);\n      this.updateComboData(combos);\n      this.updateEdgeData(edges);\n    });\n    this.computeZIndex(data, 'update');\n  }\n  updateNodeData(nodes = []) {\n    if (!nodes.length) return;\n    const {\n      model\n    } = this;\n    this.batch(() => {\n      const modifiedNodes = [];\n      nodes.forEach(modifiedNode => {\n        const id = idOf(modifiedNode);\n        const originalNode = toG6Data(model.getNode(id));\n        if (isElementDataEqual(originalNode, modifiedNode)) return;\n        const value = mergeElementsData(originalNode, modifiedNode);\n        this.pushChange({\n          value,\n          original: originalNode,\n          type: ChangeType.NodeUpdated\n        });\n        model.mergeNodeData(id, value);\n        modifiedNodes.push(value);\n      });\n      this.updateNodeLikeHierarchy(modifiedNodes);\n    });\n    this.computeZIndex({\n      nodes\n    }, 'update');\n  }\n  /**\n   * <zh/> 将所有数据提交到变更记录中以进行重绘\n   *\n   * <en/> Submit all data to the change record for redrawing\n   */\n  refreshData() {\n    const {\n      nodes,\n      edges,\n      combos\n    } = this.getData();\n    nodes.forEach(node => {\n      this.pushChange({\n        value: node,\n        original: node,\n        type: ChangeType.NodeUpdated\n      });\n    });\n    edges.forEach(edge => {\n      this.pushChange({\n        value: edge,\n        original: edge,\n        type: ChangeType.EdgeUpdated\n      });\n    });\n    combos.forEach(combo => {\n      this.pushChange({\n        value: combo,\n        original: combo,\n        type: ChangeType.ComboUpdated\n      });\n    });\n  }\n  syncNodeLikeDatum(datum) {\n    const {\n      model\n    } = this;\n    const id = idOf(datum);\n    if (!model.hasNode(id)) return;\n    const original = toG6Data(model.getNode(id));\n    const value = mergeElementsData(original, datum);\n    model.mergeNodeData(id, value);\n  }\n  syncEdgeDatum(datum) {\n    const {\n      model\n    } = this;\n    const id = idOf(datum);\n    if (!model.hasEdge(id)) return;\n    const original = toG6Data(model.getEdge(id));\n    const value = mergeElementsData(original, datum);\n    model.mergeEdgeData(id, value);\n  }\n  updateEdgeData(edges = []) {\n    if (!edges.length) return;\n    const {\n      model\n    } = this;\n    this.batch(() => {\n      edges.forEach(modifiedEdge => {\n        const id = idOf(modifiedEdge);\n        const originalEdge = toG6Data(model.getEdge(id));\n        if (isElementDataEqual(originalEdge, modifiedEdge)) return;\n        if (modifiedEdge.source && originalEdge.source !== modifiedEdge.source) {\n          model.updateEdgeSource(id, modifiedEdge.source);\n        }\n        if (modifiedEdge.target && originalEdge.target !== modifiedEdge.target) {\n          model.updateEdgeTarget(id, modifiedEdge.target);\n        }\n        const updatedData = mergeElementsData(originalEdge, modifiedEdge);\n        this.pushChange({\n          value: updatedData,\n          original: originalEdge,\n          type: ChangeType.EdgeUpdated\n        });\n        model.mergeEdgeData(id, updatedData);\n      });\n    });\n    this.computeZIndex({\n      edges\n    }, 'update');\n  }\n  updateComboData(combos = []) {\n    if (!combos.length) return;\n    const {\n      model\n    } = this;\n    model.batch(() => {\n      const modifiedCombos = [];\n      combos.forEach(modifiedCombo => {\n        const id = idOf(modifiedCombo);\n        const originalCombo = toG6Data(model.getNode(id));\n        if (isElementDataEqual(originalCombo, modifiedCombo)) return;\n        const value = mergeElementsData(originalCombo, modifiedCombo);\n        this.pushChange({\n          value,\n          original: originalCombo,\n          type: ChangeType.ComboUpdated\n        });\n        model.mergeNodeData(id, value);\n        modifiedCombos.push(value);\n      });\n      this.updateNodeLikeHierarchy(modifiedCombos);\n    });\n    this.computeZIndex({\n      combos\n    }, 'update');\n  }\n  /**\n   * <zh/> 设置节点的父节点\n   *\n   * <en/> Set the parent node of the node\n   * @param id - <zh/> 节点 ID | <en/> node ID\n   * @param parent - <zh/> 父节点 ID | <en/> parent node ID\n   * @param hierarchyKey - <zh/> 层次结构类型 | <en/> hierarchy type\n   * @param update - <zh/> 添加新/旧父节点数据更新记录 | <en/> add new/old parent node data update record\n   */\n  setParent(id, parent, hierarchyKey, update = true) {\n    if (id === parent) return;\n    const elementData = this.getNodeLikeDatum(id);\n    const originalParentId = parentIdOf(elementData);\n    if (originalParentId !== parent && hierarchyKey === COMBO_KEY) {\n      const modifiedDatum = {\n        id,\n        combo: parent\n      };\n      if (this.isCombo(id)) this.syncNodeLikeDatum(modifiedDatum);else this.syncNodeLikeDatum(modifiedDatum);\n    }\n    this.model.setParent(id, parent, hierarchyKey);\n    if (update && hierarchyKey === COMBO_KEY) {\n      uniq([originalParentId, parent]).forEach(pId => {\n        if (pId !== undefined) this.refreshComboData(pId);\n      });\n    }\n  }\n  /**\n   * <zh/> 刷新 combo 数据\n   *\n   * <en/> Refresh combo data\n   * @param id - <zh/> combo ID | <en/> combo ID\n   * @remarks\n   * <zh/> 不会更改数据，但会触发数据变更事件\n   *\n   * <en/> Will not change the data, but will trigger data change events\n   */\n  refreshComboData(id) {\n    const combo = this.getComboData([id])[0];\n    const ancestors = this.getAncestorsData(id, COMBO_KEY);\n    if (combo) this.pushChange({\n      value: combo,\n      original: combo,\n      type: ChangeType.ComboUpdated\n    });\n    ancestors.forEach(value => {\n      this.pushChange({\n        value: value,\n        original: value,\n        type: ChangeType.ComboUpdated\n      });\n    });\n  }\n  getElementPosition(id) {\n    const datum = this.getElementDataById(id);\n    return positionOf(datum);\n  }\n  translateNodeLikeBy(id, offset) {\n    if (this.isCombo(id)) this.translateComboBy(id, offset);else this.translateNodeBy(id, offset);\n  }\n  translateNodeLikeTo(id, position) {\n    if (this.isCombo(id)) this.translateComboTo(id, position);else this.translateNodeTo(id, position);\n  }\n  translateNodeBy(id, offset) {\n    const curr = this.getElementPosition(id);\n    const position = add(curr, [...offset, 0].slice(0, 3));\n    this.translateNodeTo(id, position);\n  }\n  translateNodeTo(id, position) {\n    const [x = 0, y = 0, z = 0] = position;\n    this.preventUpdateNodeLikeHierarchy(() => {\n      this.updateNodeData([{\n        id,\n        style: {\n          x,\n          y,\n          z\n        }\n      }]);\n    });\n  }\n  translateComboBy(id, offset) {\n    const [dx = 0, dy = 0, dz = 0] = offset;\n    if ([dx, dy, dz].some(isNaN) || [dx, dy, dz].every(o => o === 0)) return;\n    const combo = this.getComboData([id])[0];\n    if (!combo) return;\n    const seenNodeLikeIds = new Set();\n    dfs(combo, succeed => {\n      const succeedID = idOf(succeed);\n      if (seenNodeLikeIds.has(succeedID)) return;\n      seenNodeLikeIds.add(succeedID);\n      const [x, y, z] = positionOf(succeed);\n      const value = mergeElementsData(succeed, {\n        style: {\n          x: x + dx,\n          y: y + dy,\n          z: z + dz\n        }\n      });\n      this.pushChange({\n        value,\n        // @ts-ignore\n        original: succeed,\n        type: this.isCombo(succeedID) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated\n      });\n      this.model.mergeNodeData(succeedID, value);\n    }, node => this.getChildrenData(idOf(node)), 'BT');\n  }\n  translateComboTo(id, position) {\n    var _a;\n    if (position.some(isNaN)) return;\n    const [tx = 0, ty = 0, tz = 0] = position;\n    const combo = (_a = this.getComboData([id])) === null || _a === void 0 ? void 0 : _a[0];\n    if (!combo) return;\n    const [comboX, comboY, comboZ] = positionOf(combo);\n    const dx = tx - comboX;\n    const dy = ty - comboY;\n    const dz = tz - comboZ;\n    dfs(combo, succeed => {\n      const succeedId = idOf(succeed);\n      const [x, y, z] = positionOf(succeed);\n      const value = mergeElementsData(succeed, {\n        style: {\n          x: x + dx,\n          y: y + dy,\n          z: z + dz\n        }\n      });\n      this.pushChange({\n        value,\n        // @ts-ignore\n        original: succeed,\n        type: this.isCombo(succeedId) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated\n      });\n      this.model.mergeNodeData(succeedId, value);\n    }, node => this.getChildrenData(idOf(node)), 'BT');\n  }\n  removeData(data) {\n    const {\n      nodes,\n      edges,\n      combos\n    } = data;\n    this.batch(() => {\n      // remove edges first\n      this.removeEdgeData(edges);\n      this.removeNodeData(nodes);\n      this.removeComboData(combos);\n      this.latestRemovedComboIds = new Set(combos);\n    });\n  }\n  removeNodeData(ids = []) {\n    if (!ids.length) return;\n    this.batch(() => {\n      ids.forEach(id => {\n        // 移除关联边、子节点\n        // remove related edges and child nodes\n        this.removeEdgeData(this.getRelatedEdgesData(id).map(idOf));\n        // TODO 树图情况下移除子节点\n        this.pushChange({\n          value: this.getNodeData([id])[0],\n          type: ChangeType.NodeRemoved\n        });\n        this.removeNodeLikeHierarchy(id);\n      });\n      this.model.removeNodes(ids);\n    });\n  }\n  removeEdgeData(ids = []) {\n    if (!ids.length) return;\n    ids.forEach(id => this.pushChange({\n      value: this.getEdgeData([id])[0],\n      type: ChangeType.EdgeRemoved\n    }));\n    this.model.removeEdges(ids);\n  }\n  removeComboData(ids = []) {\n    if (!ids.length) return;\n    this.batch(() => {\n      ids.forEach(id => {\n        this.pushChange({\n          value: this.getComboData([id])[0],\n          type: ChangeType.ComboRemoved\n        });\n        this.removeNodeLikeHierarchy(id);\n        this.comboIds.delete(id);\n      });\n      this.model.removeNodes(ids);\n    });\n  }\n  /**\n   * <zh/> 移除节点层次结构，将其子节点移动到父节点的 children 列表中\n   *\n   * <en/> Remove the node hierarchy and move its child nodes to the parent node's children list\n   * @param id - <zh/> 待处理的节点 | <en/> node to be processed\n   */\n  removeNodeLikeHierarchy(id) {\n    if (this.model.hasTreeStructure(COMBO_KEY)) {\n      const grandParent = parentIdOf(this.getNodeLikeDatum(id));\n      // 从父节点的 children 列表中移除\n      // remove from its parent's children list\n      // 调用 graphlib.setParent，不需要更新数据\n      this.setParent(id, undefined, COMBO_KEY, false);\n      // 将子节点移动到父节点的 children 列表中\n      // move the children to the grandparent's children list\n      this.model.getChildren(id, COMBO_KEY).forEach(child => {\n        const childData = toG6Data(child);\n        const childId = idOf(childData);\n        this.setParent(idOf(childData), grandParent, COMBO_KEY, false);\n        const value = mergeElementsData(childData, {\n          id: idOf(childData),\n          combo: grandParent\n        });\n        this.pushChange({\n          value,\n          original: childData,\n          type: this.isCombo(childId) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated\n        });\n        this.model.mergeNodeData(idOf(childData), value);\n      });\n      if (!isNil(grandParent)) this.refreshComboData(grandParent);\n    }\n  }\n  /**\n   * <zh/> 获取元素的类型\n   *\n   * <en/> Get the type of the element\n   * @param id - <zh/> 元素 ID | <en/> ID of the element\n   * @returns <zh/> 元素类型 | <en/> type of the element\n   */\n  getElementType(id) {\n    if (this.model.hasNode(id)) {\n      if (this.isCombo(id)) return 'combo';\n      return 'node';\n    }\n    if (this.model.hasEdge(id)) return 'edge';\n    throw new Error(format(`Unknown element type of id: ${id}`));\n  }\n  destroy() {\n    const {\n      model\n    } = this;\n    const nodes = model.getAllNodes();\n    const edges = model.getAllEdges();\n    model.removeEdges(edges.map(edge => edge.id));\n    model.removeNodes(nodes.map(node => node.id));\n    // @ts-expect-error force delete\n    this.context = {};\n  }\n}", "map": {"version": 3, "names": ["Graph", "GraphLib", "isNil", "isNumber", "uniq", "COMBO_KEY", "ChangeType", "TREE_KEY", "isCollapsed", "cloneElementData", "isElementDataEqual", "mergeElementsData", "arrayDiff", "toG6Data", "toGraphlibData", "idOf", "parentIdOf", "positionOf", "format", "print", "dfs", "add", "DataController", "constructor", "latestRemovedComboIds", "Set", "comboIds", "changes", "batchCount", "isTraceless", "enableUpdateNodeLikeHierarchy", "model", "pushChange", "change", "type", "NodeUpdated", "EdgeUpdated", "ComboUpdated", "value", "original", "push", "getChanges", "clearChanges", "batch", "callback", "isBatching", "silence", "isCombo", "id", "has", "getData", "nodes", "getNodeData", "edges", "getEdgeData", "combos", "getComboData", "ids", "getAllNodes", "reduce", "acc", "node", "data", "undefined", "includes", "getEdgeDatum", "getEdge", "getAllEdges", "edge", "combo", "getRootsData", "<PERSON><PERSON><PERSON>", "getRoots", "map", "getAncestorsData", "hasNode", "hasTreeStructure", "getAncestors", "getDescendantsData", "root", "getElementDataById", "getChildrenData", "getParentData", "warn", "parent", "getParent", "structure<PERSON>ey", "getElementType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getElementsDataByType", "elementType", "getNodeLikeDatum", "getNode", "getNodeLikeData", "getElementDataByState", "state", "elementData", "filter", "datum", "_a", "states", "getElementState", "hasEdge", "hasCombo", "getRelatedEdgesData", "direction", "getRelatedEdges", "getNeighborNodesData", "getNeighbors", "setData", "modifiedNodes", "modifiedEdges", "modifiedCombos", "originalNodes", "originalEdges", "originalCombos", "nodeDiff", "edgeDiff", "comboDiff", "dataToAdd", "enter", "addData", "computeZIndex", "dataToUpdate", "update", "updateData", "dataToRemove", "exit", "removeData", "addComboData", "addNodeData", "addEdgeData", "length", "addNodes", "NodeAdded", "updateNodeLikeHierarchy", "addEdges", "EdgeAdded", "attachTreeStructure", "ComboAdded", "addChildrenData", "parentId", "childrenData", "parentData", "childrenId", "updateNodeData", "children", "childId", "source", "target", "force", "for<PERSON>ach", "style", "zIndex", "_c", "_b", "preventUpdateNodeLikeHierarchy", "updateComboData", "comboParent", "nodeParent", "sourceZIndex", "targetZIndex", "_e", "_d", "updateEdgeData", "Math", "max", "getFrontZIndex", "Object", "assign", "element", "ancestorIds", "values", "flat", "refreshComboData", "setParent", "_children", "child", "modifiedNode", "originalNode", "mergeNodeData", "refreshData", "syncNodeLikeDatum", "syncEdgeDatum", "mergeEdgeData", "modifiedEdge", "originalEdge", "updateEdgeSource", "updateEdgeTarget", "updatedData", "modifiedCombo", "originalCombo", "originalParentId", "modifiedDatum", "pId", "ancestors", "getElementPosition", "translateNodeLikeBy", "offset", "translateComboBy", "translateNodeBy", "translateNodeLikeTo", "position", "translateComboTo", "translateNodeTo", "curr", "slice", "x", "y", "z", "dx", "dy", "dz", "some", "isNaN", "every", "o", "seenNodeLikeIds", "succeed", "succeedID", "tx", "ty", "tz", "comboX", "comboY", "comboZ", "succeedId", "removeEdgeData", "removeNodeData", "removeComboData", "NodeRemoved", "removeNodeLikeHierarchy", "removeNodes", "EdgeRemoved", "removeEdges", "ComboRemoved", "delete", "grandParent", "childData", "Error", "destroy", "context"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g6/src/runtime/data.ts"], "sourcesContent": ["import { Graph as GraphLib } from '@antv/graphlib';\nimport { isNil, isNumber, uniq } from '@antv/util';\nimport { COMBO_KEY, ChangeType, TREE_KEY } from '../constants';\nimport type { ComboData, EdgeData, GraphData, NodeData } from '../spec';\nimport type {\n  DataAdded,\n  DataChange,\n  DataID,\n  DataRemoved,\n  DataUpdated,\n  ElementDatum,\n  HierarchyKey,\n  ID,\n  NodeLikeData,\n  PartialEdgeData,\n  PartialGraphData,\n  PartialNodeLikeData,\n  Point,\n  State,\n} from '../types';\nimport type { EdgeDirection } from '../types/edge';\nimport type { ElementType } from '../types/element';\nimport { isCollapsed } from '../utils/collapsibility';\nimport { cloneElementData, isElementDataEqual, mergeElementsData } from '../utils/data';\nimport { arrayDiff } from '../utils/diff';\nimport { toG6Data, toGraphlibData } from '../utils/graphlib';\nimport { idOf, parentIdOf } from '../utils/id';\nimport { positionOf } from '../utils/position';\nimport { format, print } from '../utils/print';\nimport { dfs } from '../utils/traverse';\nimport { add } from '../utils/vector';\n\nexport class DataController {\n  public model: GraphLib<NodeLikeData, EdgeData>;\n\n  /**\n   * <zh/> 最近一次删除的 combo 的 id\n   *\n   * <en/> The ids of the last deleted combos\n   * @remarks\n   * <zh/> 当删除 combo 后，会将其 id 从 comboIds 中移除，此时根据 Graphlib 的 changes 事件获取到的 NodeRemoved 无法区分是 combo 还是 node。\n   * 因此需要记录最近一次删除的 combo 的 id，并用于 isCombo 的判断\n   *\n   * <en/> When the combo is deleted, its id will be removed from comboIds. At this time, the NodeRemoved obtained according to the changes event of Graphlib cannot distinguish whether it is a combo or a node.\n   * Therefore, it is necessary to record the id of the last deleted combo and use it to judge isCombo\n   */\n  protected latestRemovedComboIds = new Set<ID>();\n\n  protected comboIds = new Set<ID>();\n\n  /**\n   * <zh/> 获取详细数据变更\n   *\n   * <en/> Get detailed data changes\n   */\n  private changes: DataChange[] = [];\n\n  /**\n   * <zh/> 批处理计数器\n   *\n   * <en/> Batch processing counter\n   */\n  private batchCount = 0;\n\n  /**\n   * <zh/> 是否处于无痕模式\n   *\n   * <en/> Whether it is in traceless mode\n   */\n  private isTraceless = false;\n\n  constructor() {\n    this.model = new GraphLib();\n  }\n\n  private pushChange(change: DataChange) {\n    if (this.isTraceless) return;\n    const { type } = change;\n\n    if (type === ChangeType.NodeUpdated || type === ChangeType.EdgeUpdated || type === ChangeType.ComboUpdated) {\n      const { value, original } = change;\n      this.changes.push({ value: cloneElementData(value), original: cloneElementData(original), type } as DataUpdated);\n    } else {\n      this.changes.push({ value: cloneElementData(change.value), type } as DataAdded | DataRemoved);\n    }\n  }\n\n  public getChanges(): DataChange[] {\n    return this.changes;\n  }\n\n  public clearChanges() {\n    this.changes = [];\n  }\n\n  public batch(callback: () => void) {\n    this.batchCount++;\n    this.model.batch(callback);\n    this.batchCount--;\n  }\n\n  protected isBatching() {\n    return this.batchCount > 0;\n  }\n\n  /**\n   * <zh/> 执行操作而不会留下记录\n   *\n   * <en/> Perform operations without leaving records\n   * @param callback - <zh/> 回调函数 | <en/> callback function\n   * @remarks\n   * <zh/> 通常用于运行时调整元素并同步数据，避免触发数据变更导致重绘\n   *\n   * <en/> Usually used to adjust elements at runtime and synchronize data to avoid triggering data changes and causing redraws\n   */\n  public silence(callback: () => void) {\n    this.isTraceless = true;\n    callback();\n    this.isTraceless = false;\n  }\n\n  public isCombo(id: ID) {\n    return this.comboIds.has(id) || this.latestRemovedComboIds.has(id);\n  }\n\n  public getData() {\n    return {\n      nodes: this.getNodeData(),\n      edges: this.getEdgeData(),\n      combos: this.getComboData(),\n    };\n  }\n\n  public getNodeData(ids?: ID[]) {\n    return this.model.getAllNodes().reduce((acc, node) => {\n      const data = toG6Data(node);\n      if (this.isCombo(idOf(data))) return acc;\n      if (ids === undefined) acc.push(data);\n      else ids.includes(idOf(data)) && acc.push(data);\n      return acc;\n    }, [] as NodeData[]);\n  }\n\n  public getEdgeDatum(id: ID) {\n    return toG6Data(this.model.getEdge(id));\n  }\n\n  public getEdgeData(ids?: ID[]) {\n    return this.model.getAllEdges().reduce((acc, edge) => {\n      const data = toG6Data(edge);\n      if (ids === undefined) acc.push(data);\n      else ids.includes(idOf(data)) && acc.push(data);\n      return acc;\n    }, [] as EdgeData[]);\n  }\n\n  public getComboData(ids?: ID[]) {\n    return this.model.getAllNodes().reduce((acc, combo) => {\n      const data = toG6Data(combo);\n      if (!this.isCombo(idOf(data))) return acc;\n\n      if (ids === undefined) acc.push(data as ComboData);\n      else ids.includes(idOf(data)) && acc.push(data as ComboData);\n      return acc;\n    }, [] as ComboData[]);\n  }\n\n  public getRootsData(hierarchyKey: HierarchyKey = TREE_KEY) {\n    return this.model.getRoots(hierarchyKey).map(toG6Data);\n  }\n\n  public getAncestorsData(id: ID, hierarchyKey: HierarchyKey): NodeLikeData[] {\n    const { model } = this;\n    if (!model.hasNode(id) || !model.hasTreeStructure(hierarchyKey)) return [];\n    return model.getAncestors(id, hierarchyKey).map(toG6Data);\n  }\n\n  public getDescendantsData(id: ID): NodeLikeData[] {\n    const root = this.getElementDataById(id) as NodeLikeData;\n    const data: NodeLikeData[] = [];\n    dfs(\n      root,\n      (node) => {\n        if (node !== root) data.push(node);\n      },\n      (node) => this.getChildrenData(idOf(node)),\n      'TB',\n    );\n    return data;\n  }\n\n  public getParentData(id: ID, hierarchyKey: HierarchyKey): NodeLikeData | undefined {\n    const { model } = this;\n    if (!hierarchyKey) {\n      print.warn('The hierarchy structure key is not specified');\n      return undefined;\n    }\n    if (!model.hasNode(id) || !model.hasTreeStructure(hierarchyKey)) return undefined;\n    const parent = model.getParent(id, hierarchyKey);\n    return parent ? toG6Data(parent) : undefined;\n  }\n\n  public getChildrenData(id: ID): NodeLikeData[] {\n    const structureKey = this.getElementType(id) === 'node' ? TREE_KEY : COMBO_KEY;\n    const { model } = this;\n    if (!model.hasNode(id) || !model.hasTreeStructure(structureKey)) return [];\n    return model.getChildren(id, structureKey).map(toG6Data);\n  }\n\n  /**\n   * <zh/> 获取指定类型元素的数据\n   *\n   * <en/> Get the data of the specified type of element\n   * @param elementType - <zh/> 元素类型 | <en/> element type\n   * @returns <zh/> 元素数据 | <en/> element data\n   */\n  public getElementsDataByType(elementType: ElementType) {\n    if (elementType === 'node') return this.getNodeData();\n    if (elementType === 'edge') return this.getEdgeData();\n    if (elementType === 'combo') return this.getComboData();\n    return [];\n  }\n\n  /**\n   * <zh/> 根据 ID 获取元素的数据，不用关心元素的类型\n   *\n   * <en/> Get the data of the element by ID, no need to care about the type of the element\n   * @param id - <zh/> 元素 ID 数组 | <en/> element ID array\n   * @returns <zh/> 元素数据 | <en/> data of the element\n   */\n  public getElementDataById(id: ID): ElementDatum {\n    const type = this.getElementType(id);\n    if (type === 'edge') return this.getEdgeDatum(id);\n    return this.getNodeLikeDatum(id);\n  }\n\n  /**\n   * <zh/> 获取节点的数据\n   *\n   * <en/> Get node data\n   * @param id - <zh/> 节点 ID | <en/> node ID\n   * @returns <zh/> 节点数据 | <en/> node data\n   */\n  public getNodeLikeDatum(id: ID) {\n    const data = this.model.getNode(id);\n    return toG6Data(data);\n  }\n\n  /**\n   * <zh/> 获取所有节点和 combo 的数据\n   *\n   * <en/> Get all node and combo data\n   * @param ids - <zh/> 节点和 combo ID 数组 | <en/> node and combo ID array\n   * @returns <zh/> 节点和 combo 的数据 | <en/> node and combo data\n   */\n  public getNodeLikeData(ids?: ID[]) {\n    return this.model.getAllNodes().reduce((acc, node) => {\n      const data = toG6Data(node);\n      if (ids) ids.includes(idOf(data)) && acc.push(data);\n      else acc.push(data);\n      return acc;\n    }, [] as NodeLikeData[]);\n  }\n\n  public getElementDataByState(elementType: ElementType, state: string) {\n    const elementData = this.getElementsDataByType(elementType);\n    return elementData.filter((datum) => datum.states?.includes(state));\n  }\n\n  public getElementState(id: ID): State[] {\n    return this.getElementDataById(id)?.states || [];\n  }\n\n  public hasNode(id: ID) {\n    return this.model.hasNode(id) && !this.isCombo(id);\n  }\n\n  public hasEdge(id: ID) {\n    return this.model.hasEdge(id);\n  }\n\n  public hasCombo(id: ID) {\n    return this.model.hasNode(id) && this.isCombo(id);\n  }\n\n  public getRelatedEdgesData(id: ID, direction: EdgeDirection = 'both') {\n    return this.model.getRelatedEdges(id, direction).map(toG6Data) as EdgeData[];\n  }\n\n  public getNeighborNodesData(id: ID) {\n    return this.model.getNeighbors(id).map(toG6Data);\n  }\n\n  public setData(data: GraphData) {\n    const { nodes: modifiedNodes = [], edges: modifiedEdges = [], combos: modifiedCombos = [] } = data;\n    const { nodes: originalNodes, edges: originalEdges, combos: originalCombos } = this.getData();\n\n    const nodeDiff = arrayDiff(originalNodes, modifiedNodes, (node) => idOf(node), isElementDataEqual);\n    const edgeDiff = arrayDiff(originalEdges, modifiedEdges, (edge) => idOf(edge), isElementDataEqual);\n    const comboDiff = arrayDiff(originalCombos, modifiedCombos, (combo) => idOf(combo), isElementDataEqual);\n\n    this.batch(() => {\n      const dataToAdd = {\n        nodes: nodeDiff.enter,\n        edges: edgeDiff.enter,\n        combos: comboDiff.enter,\n      };\n      this.addData(dataToAdd);\n      this.computeZIndex(dataToAdd, 'add', true);\n\n      const dataToUpdate = {\n        nodes: nodeDiff.update,\n        edges: edgeDiff.update,\n        combos: comboDiff.update,\n      };\n      this.updateData(dataToUpdate);\n      this.computeZIndex(dataToUpdate, 'update', true);\n\n      const dataToRemove = {\n        nodes: nodeDiff.exit.map(idOf),\n        edges: edgeDiff.exit.map(idOf),\n        combos: comboDiff.exit.map(idOf),\n      };\n      this.removeData(dataToRemove);\n    });\n  }\n\n  public addData(data: GraphData) {\n    const { nodes, edges, combos } = data;\n    this.batch(() => {\n      // add combo first\n      this.addComboData(combos);\n      this.addNodeData(nodes);\n      this.addEdgeData(edges);\n    });\n    this.computeZIndex(data, 'add');\n  }\n\n  public addNodeData(nodes: NodeData[] = []) {\n    if (!nodes.length) return;\n    this.model.addNodes(\n      nodes.map((node) => {\n        this.pushChange({ value: node, type: ChangeType.NodeAdded });\n        return toGraphlibData(node);\n      }),\n    );\n    this.updateNodeLikeHierarchy(nodes);\n\n    this.computeZIndex({ nodes }, 'add');\n  }\n\n  public addEdgeData(edges: EdgeData[] = []) {\n    if (!edges.length) return;\n    this.model.addEdges(\n      edges.map((edge) => {\n        this.pushChange({ value: edge, type: ChangeType.EdgeAdded });\n        return toGraphlibData(edge);\n      }),\n    );\n\n    this.computeZIndex({ edges }, 'add');\n  }\n\n  public addComboData(combos: ComboData[] = []) {\n    if (!combos.length) return;\n    const { model } = this;\n\n    if (!model.hasTreeStructure(COMBO_KEY)) {\n      model.attachTreeStructure(COMBO_KEY);\n    }\n\n    model.addNodes(\n      combos.map((combo) => {\n        this.comboIds.add(idOf(combo));\n        this.pushChange({ value: combo, type: ChangeType.ComboAdded });\n        return toGraphlibData(combo);\n      }),\n    );\n\n    this.updateNodeLikeHierarchy(combos);\n\n    this.computeZIndex({ combos }, 'add');\n  }\n\n  public addChildrenData(parentId: ID, childrenData: NodeData[]) {\n    const parentData = this.getNodeLikeDatum(parentId) as NodeData;\n    const childrenId = childrenData.map(idOf);\n    this.addNodeData(childrenData);\n    this.updateNodeData([{ id: parentId, children: [...(parentData.children || []), ...childrenId] }]);\n    this.addEdgeData(childrenId.map((childId) => ({ source: parentId, target: childId })));\n  }\n\n  /**\n   * <zh/> 计算 zIndex\n   *\n   * <en/> Calculate zIndex\n   * @param data - <zh/> 新增的数据 | <en/> newly added data\n   * @param type - <zh/> 操作类型 | <en/> operation type\n   * @param force - <zh/> 忽略批处理 | <en/> ignore batch processing\n   * @remarks\n   * <zh/> 调用该函数的情况：\n   * - 新增元素\n   * - 更新节点/组合的 combo\n   * - 更新节点的 children\n   *\n   * <en/> The situation of calling this function:\n   * - Add element\n   * - Update the combo of the node/combo\n   * - Update the children of the node\n   */\n  protected computeZIndex(data: PartialGraphData, type: 'add' | 'update', force = false) {\n    if (!force && this.isBatching()) return;\n    this.batch(() => {\n      const { nodes = [], edges = [], combos = [] } = data;\n\n      combos.forEach((combo) => {\n        const id = idOf(combo);\n        if (type === 'add' && isNumber(combo.style?.zIndex)) return;\n        if (type === 'update' && !('combo' in combo)) return;\n\n        const parent = this.getParentData(id, COMBO_KEY);\n        const zIndex = parent ? (parent.style?.zIndex ?? 0) + 1 : 0;\n\n        this.preventUpdateNodeLikeHierarchy(() => {\n          this.updateComboData([{ id, style: { zIndex } }]);\n        });\n      });\n\n      nodes.forEach((node) => {\n        const id = idOf(node);\n        if (type === 'add' && isNumber(node.style?.zIndex)) return;\n        if (type === 'update' && !('combo' in node) && !('children' in node)) return;\n\n        let zIndex = 0;\n\n        const comboParent = this.getParentData(id, COMBO_KEY);\n        if (comboParent) {\n          zIndex = (comboParent.style?.zIndex || 0) + 1;\n        } else {\n          const nodeParent = this.getParentData(id, TREE_KEY);\n          if (nodeParent) zIndex = nodeParent?.style?.zIndex || 0;\n        }\n\n        this.preventUpdateNodeLikeHierarchy(() => {\n          this.updateNodeData([{ id, style: { zIndex } }]);\n        });\n      });\n\n      edges.forEach((edge) => {\n        if (isNumber(edge.style?.zIndex)) return;\n\n        let { id, source, target } = edge;\n        if (!id) id = idOf(edge);\n        else {\n          const datum = this.getEdgeDatum(id);\n          source = datum.source;\n          target = datum.target;\n        }\n\n        if (!source || !target) return;\n\n        const sourceZIndex = this.getNodeLikeDatum(source)?.style?.zIndex || 0;\n        const targetZIndex = this.getNodeLikeDatum(target)?.style?.zIndex || 0;\n\n        this.updateEdgeData([{ id: idOf(edge), style: { zIndex: Math.max(sourceZIndex, targetZIndex) - 1 } }]);\n      });\n    });\n  }\n\n  /**\n   * <zh/> 计算元素置顶后的 zIndex\n   *\n   * <en/> Calculate the zIndex after the element is placed on top\n   * @param id - <zh/> 元素 ID | <en/> ID of the element\n   * @returns <zh/> zIndex | <en/> zIndex\n   */\n  public getFrontZIndex(id: ID) {\n    const elementType = this.getElementType(id);\n    const elementData = this.getElementDataById(id);\n    const data = this.getData();\n\n    // 排除当前元素 / Exclude the current element\n    Object.assign(data, {\n      [`${elementType}s`]: data[`${elementType}s`].filter((element) => idOf(element) !== id),\n    });\n\n    if (elementType === 'combo') {\n      // 如果 combo 展开，则排除 combo 的子节点/combo 及内部边\n      // If the combo is expanded, exclude the child nodes/combos of the combo and the internal edges\n      if (!isCollapsed(elementData as ComboData)) {\n        const ancestorIds = new Set(this.getAncestorsData(id, COMBO_KEY).map(idOf));\n        data.nodes = data.nodes.filter((element) => !ancestorIds.has(idOf(element)));\n        data.combos = data.combos.filter((element) => !ancestorIds.has(idOf(element)));\n        data.edges = data.edges.filter(({ source, target }) => !ancestorIds.has(source) && !ancestorIds.has(target));\n      }\n    }\n\n    return Math.max(\n      elementData.style?.zIndex || 0,\n      0,\n      ...Object.values(data)\n        .flat()\n        .map((datum) => (datum?.style?.zIndex || 0) + 1),\n    );\n  }\n\n  protected updateNodeLikeHierarchy(data: NodeLikeData[]) {\n    if (!this.enableUpdateNodeLikeHierarchy) return;\n    const { model } = this;\n\n    data.forEach((datum) => {\n      const id = idOf(datum);\n      const parent = parentIdOf(datum);\n\n      if (parent !== undefined) {\n        if (!model.hasTreeStructure(COMBO_KEY)) model.attachTreeStructure(COMBO_KEY);\n\n        // 解除原父节点的子节点关系，更新原父节点及其祖先的数据\n        // Remove the child relationship of the original parent node, update the data of the original parent node and its ancestors\n        if (parent === null) {\n          this.refreshComboData(id);\n        }\n\n        this.setParent(id, parentIdOf(datum), COMBO_KEY);\n      }\n\n      const children = (datum as NodeData).children || [];\n      if (children.length) {\n        if (!model.hasTreeStructure(TREE_KEY)) model.attachTreeStructure(TREE_KEY);\n        const _children = children.filter((child) => model.hasNode(child));\n        _children.forEach((child) => this.setParent(child, id, TREE_KEY));\n        if (_children.length !== children.length) {\n          // 从数据中移除不存在的子节点\n          // Remove non-existent child nodes from the data\n          this.updateNodeData([{ id, children: _children }]);\n        }\n      }\n    });\n  }\n\n  private enableUpdateNodeLikeHierarchy = true;\n\n  /**\n   * <zh/> 执行变更时不要更新节点层次结构\n   *\n   * <en/> Do not update the node hierarchy when executing changes\n   * @param callback - <zh/> 变更函数 | <en/> change function\n   */\n  public preventUpdateNodeLikeHierarchy(callback: () => void) {\n    this.enableUpdateNodeLikeHierarchy = false;\n    callback();\n    this.enableUpdateNodeLikeHierarchy = true;\n  }\n\n  public updateData(data: PartialGraphData) {\n    const { nodes, edges, combos } = data;\n    this.batch(() => {\n      this.updateNodeData(nodes);\n      this.updateComboData(combos);\n      this.updateEdgeData(edges);\n    });\n    this.computeZIndex(data, 'update');\n  }\n\n  public updateNodeData(nodes: PartialNodeLikeData<NodeData>[] = []) {\n    if (!nodes.length) return;\n    const { model } = this;\n    this.batch(() => {\n      const modifiedNodes: NodeData[] = [];\n      nodes.forEach((modifiedNode) => {\n        const id = idOf(modifiedNode);\n        const originalNode = toG6Data(model.getNode(id));\n        if (isElementDataEqual(originalNode, modifiedNode)) return;\n\n        const value = mergeElementsData(originalNode, modifiedNode);\n        this.pushChange({ value, original: originalNode, type: ChangeType.NodeUpdated });\n        model.mergeNodeData(id, value);\n        modifiedNodes.push(value);\n      });\n\n      this.updateNodeLikeHierarchy(modifiedNodes);\n    });\n\n    this.computeZIndex({ nodes }, 'update');\n  }\n\n  /**\n   * <zh/> 将所有数据提交到变更记录中以进行重绘\n   *\n   * <en/> Submit all data to the change record for redrawing\n   */\n  public refreshData() {\n    const { nodes, edges, combos } = this.getData();\n    nodes.forEach((node) => {\n      this.pushChange({ value: node, original: node, type: ChangeType.NodeUpdated });\n    });\n    edges.forEach((edge) => {\n      this.pushChange({ value: edge, original: edge, type: ChangeType.EdgeUpdated });\n    });\n    combos.forEach((combo) => {\n      this.pushChange({ value: combo, original: combo, type: ChangeType.ComboUpdated });\n    });\n  }\n\n  public syncNodeLikeDatum(datum: PartialNodeLikeData<NodeData>) {\n    const { model } = this;\n\n    const id = idOf(datum);\n    if (!model.hasNode(id)) return;\n    const original = toG6Data(model.getNode(id));\n    const value = mergeElementsData(original, datum);\n    model.mergeNodeData(id, value);\n  }\n\n  public syncEdgeDatum(datum: PartialEdgeData<EdgeData>) {\n    const { model } = this;\n\n    const id = idOf(datum);\n    if (!model.hasEdge(id)) return;\n    const original = toG6Data(model.getEdge(id));\n    const value = mergeElementsData(original, datum);\n    model.mergeEdgeData(id, value);\n  }\n\n  public updateEdgeData(edges: PartialEdgeData<EdgeData>[] = []) {\n    if (!edges.length) return;\n    const { model } = this;\n    this.batch(() => {\n      edges.forEach((modifiedEdge) => {\n        const id = idOf(modifiedEdge);\n        const originalEdge = toG6Data(model.getEdge(id));\n        if (isElementDataEqual(originalEdge, modifiedEdge)) return;\n\n        if (modifiedEdge.source && originalEdge.source !== modifiedEdge.source) {\n          model.updateEdgeSource(id, modifiedEdge.source);\n        }\n        if (modifiedEdge.target && originalEdge.target !== modifiedEdge.target) {\n          model.updateEdgeTarget(id, modifiedEdge.target);\n        }\n        const updatedData = mergeElementsData(originalEdge, modifiedEdge);\n        this.pushChange({ value: updatedData, original: originalEdge, type: ChangeType.EdgeUpdated });\n        model.mergeEdgeData(id, updatedData);\n      });\n    });\n\n    this.computeZIndex({ edges }, 'update');\n  }\n\n  public updateComboData(combos: PartialNodeLikeData<ComboData>[] = []) {\n    if (!combos.length) return;\n    const { model } = this;\n    model.batch(() => {\n      const modifiedCombos: ComboData[] = [];\n      combos.forEach((modifiedCombo) => {\n        const id = idOf(modifiedCombo);\n        const originalCombo = toG6Data(model.getNode(id)) as ComboData;\n        if (isElementDataEqual(originalCombo, modifiedCombo)) return;\n\n        const value = mergeElementsData(originalCombo, modifiedCombo);\n        this.pushChange({ value, original: originalCombo, type: ChangeType.ComboUpdated });\n        model.mergeNodeData(id, value);\n        modifiedCombos.push(value);\n      });\n\n      this.updateNodeLikeHierarchy(modifiedCombos);\n    });\n\n    this.computeZIndex({ combos }, 'update');\n  }\n\n  /**\n   * <zh/> 设置节点的父节点\n   *\n   * <en/> Set the parent node of the node\n   * @param id - <zh/> 节点 ID | <en/> node ID\n   * @param parent - <zh/> 父节点 ID | <en/> parent node ID\n   * @param hierarchyKey - <zh/> 层次结构类型 | <en/> hierarchy type\n   * @param update - <zh/> 添加新/旧父节点数据更新记录 | <en/> add new/old parent node data update record\n   */\n  public setParent(id: ID, parent: ID | undefined | null, hierarchyKey: HierarchyKey, update: boolean = true) {\n    if (id === parent) return;\n    const elementData = this.getNodeLikeDatum(id);\n    const originalParentId = parentIdOf(elementData);\n\n    if (originalParentId !== parent && hierarchyKey === COMBO_KEY) {\n      const modifiedDatum = { id, combo: parent };\n      if (this.isCombo(id)) this.syncNodeLikeDatum(modifiedDatum);\n      else this.syncNodeLikeDatum(modifiedDatum);\n    }\n\n    this.model.setParent(id, parent, hierarchyKey);\n\n    if (update && hierarchyKey === COMBO_KEY) {\n      uniq([originalParentId, parent]).forEach((pId) => {\n        if (pId !== undefined) this.refreshComboData(pId);\n      });\n    }\n  }\n\n  /**\n   * <zh/> 刷新 combo 数据\n   *\n   * <en/> Refresh combo data\n   * @param id - <zh/> combo ID | <en/> combo ID\n   * @remarks\n   * <zh/> 不会更改数据，但会触发数据变更事件\n   *\n   * <en/> Will not change the data, but will trigger data change events\n   */\n  public refreshComboData(id: ID) {\n    const combo = this.getComboData([id])[0];\n    const ancestors = this.getAncestorsData(id, COMBO_KEY) as ComboData[];\n\n    if (combo) this.pushChange({ value: combo, original: combo, type: ChangeType.ComboUpdated });\n\n    ancestors.forEach((value) => {\n      this.pushChange({ value: value, original: value, type: ChangeType.ComboUpdated });\n    });\n  }\n\n  public getElementPosition(id: ID): Point {\n    const datum = this.getElementDataById(id) as NodeLikeData;\n    return positionOf(datum);\n  }\n\n  public translateNodeLikeBy(id: ID, offset: Point) {\n    if (this.isCombo(id)) this.translateComboBy(id, offset);\n    else this.translateNodeBy(id, offset);\n  }\n\n  public translateNodeLikeTo(id: ID, position: Point) {\n    if (this.isCombo(id)) this.translateComboTo(id, position);\n    else this.translateNodeTo(id, position);\n  }\n\n  public translateNodeBy(id: ID, offset: Point) {\n    const curr = this.getElementPosition(id);\n    const position = add(curr, [...offset, 0].slice(0, 3) as Point);\n    this.translateNodeTo(id, position);\n  }\n\n  public translateNodeTo(id: ID, position: Point) {\n    const [x = 0, y = 0, z = 0] = position;\n    this.preventUpdateNodeLikeHierarchy(() => {\n      this.updateNodeData([{ id, style: { x, y, z } }]);\n    });\n  }\n\n  public translateComboBy(id: ID, offset: Point) {\n    const [dx = 0, dy = 0, dz = 0] = offset;\n    if ([dx, dy, dz].some(isNaN) || [dx, dy, dz].every((o) => o === 0)) return;\n    const combo = this.getComboData([id])[0];\n    if (!combo) return;\n    const seenNodeLikeIds = new Set<ID>();\n    dfs<NodeLikeData>(\n      combo,\n      (succeed) => {\n        const succeedID = idOf(succeed);\n        if (seenNodeLikeIds.has(succeedID)) return;\n        seenNodeLikeIds.add(succeedID);\n        const [x, y, z] = positionOf(succeed);\n        const value = mergeElementsData(succeed, {\n          style: { x: x + dx, y: y + dy, z: z + dz },\n        });\n        this.pushChange({\n          value,\n          // @ts-ignore\n          original: succeed,\n          type: this.isCombo(succeedID) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated,\n        });\n\n        this.model.mergeNodeData(succeedID, value);\n      },\n      (node) => this.getChildrenData(idOf(node)),\n      'BT',\n    );\n  }\n\n  public translateComboTo(id: ID, position: Point) {\n    if (position.some(isNaN)) return;\n    const [tx = 0, ty = 0, tz = 0] = position;\n    const combo = this.getComboData([id])?.[0];\n    if (!combo) return;\n\n    const [comboX, comboY, comboZ] = positionOf(combo);\n    const dx = tx - comboX;\n    const dy = ty - comboY;\n    const dz = tz - comboZ;\n\n    dfs<NodeLikeData>(\n      combo,\n      (succeed) => {\n        const succeedId = idOf(succeed);\n        const [x, y, z] = positionOf(succeed);\n        const value = mergeElementsData(succeed, {\n          style: { x: x + dx, y: y + dy, z: z + dz },\n        });\n        this.pushChange({\n          value,\n          // @ts-ignore\n          original: succeed,\n          type: this.isCombo(succeedId) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated,\n        });\n        this.model.mergeNodeData(succeedId, value);\n      },\n      (node) => this.getChildrenData(idOf(node)),\n      'BT',\n    );\n  }\n\n  public removeData(data: DataID) {\n    const { nodes, edges, combos } = data;\n    this.batch(() => {\n      // remove edges first\n      this.removeEdgeData(edges);\n      this.removeNodeData(nodes);\n      this.removeComboData(combos);\n\n      this.latestRemovedComboIds = new Set(combos);\n    });\n  }\n\n  public removeNodeData(ids: ID[] = []) {\n    if (!ids.length) return;\n    this.batch(() => {\n      ids.forEach((id) => {\n        // 移除关联边、子节点\n        // remove related edges and child nodes\n        this.removeEdgeData(this.getRelatedEdgesData(id).map(idOf));\n        // TODO 树图情况下移除子节点\n\n        this.pushChange({ value: this.getNodeData([id])[0], type: ChangeType.NodeRemoved });\n        this.removeNodeLikeHierarchy(id);\n      });\n      this.model.removeNodes(ids);\n    });\n  }\n\n  public removeEdgeData(ids: ID[] = []) {\n    if (!ids.length) return;\n    ids.forEach((id) => this.pushChange({ value: this.getEdgeData([id])[0], type: ChangeType.EdgeRemoved }));\n    this.model.removeEdges(ids);\n  }\n\n  public removeComboData(ids: ID[] = []) {\n    if (!ids.length) return;\n    this.batch(() => {\n      ids.forEach((id) => {\n        this.pushChange({ value: this.getComboData([id])[0], type: ChangeType.ComboRemoved });\n        this.removeNodeLikeHierarchy(id);\n        this.comboIds.delete(id);\n      });\n      this.model.removeNodes(ids);\n    });\n  }\n\n  /**\n   * <zh/> 移除节点层次结构，将其子节点移动到父节点的 children 列表中\n   *\n   * <en/> Remove the node hierarchy and move its child nodes to the parent node's children list\n   * @param id - <zh/> 待处理的节点 | <en/> node to be processed\n   */\n  protected removeNodeLikeHierarchy(id: ID) {\n    if (this.model.hasTreeStructure(COMBO_KEY)) {\n      const grandParent = parentIdOf(this.getNodeLikeDatum(id));\n\n      // 从父节点的 children 列表中移除\n      // remove from its parent's children list\n      // 调用 graphlib.setParent，不需要更新数据\n      this.setParent(id, undefined, COMBO_KEY, false);\n      // 将子节点移动到父节点的 children 列表中\n      // move the children to the grandparent's children list\n\n      this.model.getChildren(id, COMBO_KEY).forEach((child) => {\n        const childData = toG6Data(child);\n        const childId = idOf(childData);\n        this.setParent(idOf(childData), grandParent, COMBO_KEY, false);\n        const value = mergeElementsData(childData, {\n          id: idOf(childData),\n          combo: grandParent,\n        });\n        this.pushChange({\n          value,\n          original: childData,\n          type: this.isCombo(childId) ? ChangeType.ComboUpdated : ChangeType.NodeUpdated,\n        });\n        this.model.mergeNodeData(idOf(childData), value);\n      });\n\n      if (!isNil(grandParent)) this.refreshComboData(grandParent);\n    }\n  }\n\n  /**\n   * <zh/> 获取元素的类型\n   *\n   * <en/> Get the type of the element\n   * @param id - <zh/> 元素 ID | <en/> ID of the element\n   * @returns <zh/> 元素类型 | <en/> type of the element\n   */\n  public getElementType(id: ID): ElementType {\n    if (this.model.hasNode(id)) {\n      if (this.isCombo(id)) return 'combo';\n      return 'node';\n    }\n\n    if (this.model.hasEdge(id)) return 'edge';\n\n    throw new Error(format(`Unknown element type of id: ${id}`));\n  }\n\n  public destroy() {\n    const { model } = this;\n    const nodes = model.getAllNodes();\n    const edges = model.getAllEdges();\n\n    model.removeEdges(edges.map((edge) => edge.id));\n    model.removeNodes(nodes.map((node) => node.id));\n\n    // @ts-expect-error force delete\n    this.context = {};\n  }\n}\n"], "mappings": "AAAA,SAASA,KAAK,IAAIC,QAAQ,QAAQ,gBAAgB;AAClD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,YAAY;AAClD,SAASC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,cAAc;AAoB9D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,eAAe;AACvF,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,mBAAmB;AAC5D,SAASC,IAAI,EAAEC,UAAU,QAAQ,aAAa;AAC9C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AAC9C,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,GAAG,QAAQ,iBAAiB;AAErC,OAAM,MAAOC,cAAc;EAuCzBC,YAAA;IApCA;;;;;;;;;;;IAWU,KAAAC,qBAAqB,GAAG,IAAIC,GAAG,EAAM;IAErC,KAAAC,QAAQ,GAAG,IAAID,GAAG,EAAM;IAElC;;;;;IAKQ,KAAAE,OAAO,GAAiB,EAAE;IAElC;;;;;IAKQ,KAAAC,UAAU,GAAG,CAAC;IAEtB;;;;;IAKQ,KAAAC,WAAW,GAAG,KAAK;IAudnB,KAAAC,6BAA6B,GAAG,IAAI;IApd1C,IAAI,CAACC,KAAK,GAAG,IAAI9B,QAAQ,EAAE;EAC7B;EAEQ+B,UAAUA,CAACC,MAAkB;IACnC,IAAI,IAAI,CAACJ,WAAW,EAAE;IACtB,MAAM;MAAEK;IAAI,CAAE,GAAGD,MAAM;IAEvB,IAAIC,IAAI,KAAK5B,UAAU,CAAC6B,WAAW,IAAID,IAAI,KAAK5B,UAAU,CAAC8B,WAAW,IAAIF,IAAI,KAAK5B,UAAU,CAAC+B,YAAY,EAAE;MAC1G,MAAM;QAAEC,KAAK;QAAEC;MAAQ,CAAE,GAAGN,MAAM;MAClC,IAAI,CAACN,OAAO,CAACa,IAAI,CAAC;QAAEF,KAAK,EAAE7B,gBAAgB,CAAC6B,KAAK,CAAC;QAAEC,QAAQ,EAAE9B,gBAAgB,CAAC8B,QAAQ,CAAC;QAAEL;MAAI,CAAiB,CAAC;IAClH,CAAC,MAAM;MACL,IAAI,CAACP,OAAO,CAACa,IAAI,CAAC;QAAEF,KAAK,EAAE7B,gBAAgB,CAACwB,MAAM,CAACK,KAAK,CAAC;QAAEJ;MAAI,CAA6B,CAAC;IAC/F;EACF;EAEOO,UAAUA,CAAA;IACf,OAAO,IAAI,CAACd,OAAO;EACrB;EAEOe,YAAYA,CAAA;IACjB,IAAI,CAACf,OAAO,GAAG,EAAE;EACnB;EAEOgB,KAAKA,CAACC,QAAoB;IAC/B,IAAI,CAAChB,UAAU,EAAE;IACjB,IAAI,CAACG,KAAK,CAACY,KAAK,CAACC,QAAQ,CAAC;IAC1B,IAAI,CAAChB,UAAU,EAAE;EACnB;EAEUiB,UAAUA,CAAA;IAClB,OAAO,IAAI,CAACjB,UAAU,GAAG,CAAC;EAC5B;EAEA;;;;;;;;;;EAUOkB,OAAOA,CAACF,QAAoB;IACjC,IAAI,CAACf,WAAW,GAAG,IAAI;IACvBe,QAAQ,EAAE;IACV,IAAI,CAACf,WAAW,GAAG,KAAK;EAC1B;EAEOkB,OAAOA,CAACC,EAAM;IACnB,OAAO,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAACD,EAAE,CAAC,IAAI,IAAI,CAACxB,qBAAqB,CAACyB,GAAG,CAACD,EAAE,CAAC;EACpE;EAEOE,OAAOA,CAAA;IACZ,OAAO;MACLC,KAAK,EAAE,IAAI,CAACC,WAAW,EAAE;MACzBC,KAAK,EAAE,IAAI,CAACC,WAAW,EAAE;MACzBC,MAAM,EAAE,IAAI,CAACC,YAAY;KAC1B;EACH;EAEOJ,WAAWA,CAACK,GAAU;IAC3B,OAAO,IAAI,CAAC1B,KAAK,CAAC2B,WAAW,EAAE,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAI;MACnD,MAAMC,IAAI,GAAGjD,QAAQ,CAACgD,IAAI,CAAC;MAC3B,IAAI,IAAI,CAACd,OAAO,CAAChC,IAAI,CAAC+C,IAAI,CAAC,CAAC,EAAE,OAAOF,GAAG;MACxC,IAAIH,GAAG,KAAKM,SAAS,EAAEH,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC,CAAC,KACjCL,GAAG,CAACO,QAAQ,CAACjD,IAAI,CAAC+C,IAAI,CAAC,CAAC,IAAIF,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC;MAC/C,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAgB,CAAC;EACtB;EAEOK,YAAYA,CAACjB,EAAM;IACxB,OAAOnC,QAAQ,CAAC,IAAI,CAACkB,KAAK,CAACmC,OAAO,CAAClB,EAAE,CAAC,CAAC;EACzC;EAEOM,WAAWA,CAACG,GAAU;IAC3B,OAAO,IAAI,CAAC1B,KAAK,CAACoC,WAAW,EAAE,CAACR,MAAM,CAAC,CAACC,GAAG,EAAEQ,IAAI,KAAI;MACnD,MAAMN,IAAI,GAAGjD,QAAQ,CAACuD,IAAI,CAAC;MAC3B,IAAIX,GAAG,KAAKM,SAAS,EAAEH,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC,CAAC,KACjCL,GAAG,CAACO,QAAQ,CAACjD,IAAI,CAAC+C,IAAI,CAAC,CAAC,IAAIF,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC;MAC/C,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAgB,CAAC;EACtB;EAEOJ,YAAYA,CAACC,GAAU;IAC5B,OAAO,IAAI,CAAC1B,KAAK,CAAC2B,WAAW,EAAE,CAACC,MAAM,CAAC,CAACC,GAAG,EAAES,KAAK,KAAI;MACpD,MAAMP,IAAI,GAAGjD,QAAQ,CAACwD,KAAK,CAAC;MAC5B,IAAI,CAAC,IAAI,CAACtB,OAAO,CAAChC,IAAI,CAAC+C,IAAI,CAAC,CAAC,EAAE,OAAOF,GAAG;MAEzC,IAAIH,GAAG,KAAKM,SAAS,EAAEH,GAAG,CAACpB,IAAI,CAACsB,IAAiB,CAAC,CAAC,KAC9CL,GAAG,CAACO,QAAQ,CAACjD,IAAI,CAAC+C,IAAI,CAAC,CAAC,IAAIF,GAAG,CAACpB,IAAI,CAACsB,IAAiB,CAAC;MAC5D,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAiB,CAAC;EACvB;EAEOU,YAAYA,CAACC,YAAA,GAA6BhE,QAAQ;IACvD,OAAO,IAAI,CAACwB,KAAK,CAACyC,QAAQ,CAACD,YAAY,CAAC,CAACE,GAAG,CAAC5D,QAAQ,CAAC;EACxD;EAEO6D,gBAAgBA,CAAC1B,EAAM,EAAEuB,YAA0B;IACxD,MAAM;MAAExC;IAAK,CAAE,GAAG,IAAI;IACtB,IAAI,CAACA,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,IAAI,CAACjB,KAAK,CAAC6C,gBAAgB,CAACL,YAAY,CAAC,EAAE,OAAO,EAAE;IAC1E,OAAOxC,KAAK,CAAC8C,YAAY,CAAC7B,EAAE,EAAEuB,YAAY,CAAC,CAACE,GAAG,CAAC5D,QAAQ,CAAC;EAC3D;EAEOiE,kBAAkBA,CAAC9B,EAAM;IAC9B,MAAM+B,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAAChC,EAAE,CAAiB;IACxD,MAAMc,IAAI,GAAmB,EAAE;IAC/B1C,GAAG,CACD2D,IAAI,EACHlB,IAAI,IAAI;MACP,IAAIA,IAAI,KAAKkB,IAAI,EAAEjB,IAAI,CAACtB,IAAI,CAACqB,IAAI,CAAC;IACpC,CAAC,EACAA,IAAI,IAAK,IAAI,CAACoB,eAAe,CAAClE,IAAI,CAAC8C,IAAI,CAAC,CAAC,EAC1C,IAAI,CACL;IACD,OAAOC,IAAI;EACb;EAEOoB,aAAaA,CAAClC,EAAM,EAAEuB,YAA0B;IACrD,MAAM;MAAExC;IAAK,CAAE,GAAG,IAAI;IACtB,IAAI,CAACwC,YAAY,EAAE;MACjBpD,KAAK,CAACgE,IAAI,CAAC,8CAA8C,CAAC;MAC1D,OAAOpB,SAAS;IAClB;IACA,IAAI,CAAChC,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,IAAI,CAACjB,KAAK,CAAC6C,gBAAgB,CAACL,YAAY,CAAC,EAAE,OAAOR,SAAS;IACjF,MAAMqB,MAAM,GAAGrD,KAAK,CAACsD,SAAS,CAACrC,EAAE,EAAEuB,YAAY,CAAC;IAChD,OAAOa,MAAM,GAAGvE,QAAQ,CAACuE,MAAM,CAAC,GAAGrB,SAAS;EAC9C;EAEOkB,eAAeA,CAACjC,EAAM;IAC3B,MAAMsC,YAAY,GAAG,IAAI,CAACC,cAAc,CAACvC,EAAE,CAAC,KAAK,MAAM,GAAGzC,QAAQ,GAAGF,SAAS;IAC9E,MAAM;MAAE0B;IAAK,CAAE,GAAG,IAAI;IACtB,IAAI,CAACA,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,IAAI,CAACjB,KAAK,CAAC6C,gBAAgB,CAACU,YAAY,CAAC,EAAE,OAAO,EAAE;IAC1E,OAAOvD,KAAK,CAACyD,WAAW,CAACxC,EAAE,EAAEsC,YAAY,CAAC,CAACb,GAAG,CAAC5D,QAAQ,CAAC;EAC1D;EAEA;;;;;;;EAOO4E,qBAAqBA,CAACC,WAAwB;IACnD,IAAIA,WAAW,KAAK,MAAM,EAAE,OAAO,IAAI,CAACtC,WAAW,EAAE;IACrD,IAAIsC,WAAW,KAAK,MAAM,EAAE,OAAO,IAAI,CAACpC,WAAW,EAAE;IACrD,IAAIoC,WAAW,KAAK,OAAO,EAAE,OAAO,IAAI,CAAClC,YAAY,EAAE;IACvD,OAAO,EAAE;EACX;EAEA;;;;;;;EAOOwB,kBAAkBA,CAAChC,EAAM;IAC9B,MAAMd,IAAI,GAAG,IAAI,CAACqD,cAAc,CAACvC,EAAE,CAAC;IACpC,IAAId,IAAI,KAAK,MAAM,EAAE,OAAO,IAAI,CAAC+B,YAAY,CAACjB,EAAE,CAAC;IACjD,OAAO,IAAI,CAAC2C,gBAAgB,CAAC3C,EAAE,CAAC;EAClC;EAEA;;;;;;;EAOO2C,gBAAgBA,CAAC3C,EAAM;IAC5B,MAAMc,IAAI,GAAG,IAAI,CAAC/B,KAAK,CAAC6D,OAAO,CAAC5C,EAAE,CAAC;IACnC,OAAOnC,QAAQ,CAACiD,IAAI,CAAC;EACvB;EAEA;;;;;;;EAOO+B,eAAeA,CAACpC,GAAU;IAC/B,OAAO,IAAI,CAAC1B,KAAK,CAAC2B,WAAW,EAAE,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAI;MACnD,MAAMC,IAAI,GAAGjD,QAAQ,CAACgD,IAAI,CAAC;MAC3B,IAAIJ,GAAG,EAAEA,GAAG,CAACO,QAAQ,CAACjD,IAAI,CAAC+C,IAAI,CAAC,CAAC,IAAIF,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC,CAAC,KAC/CF,GAAG,CAACpB,IAAI,CAACsB,IAAI,CAAC;MACnB,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAoB,CAAC;EAC1B;EAEOkC,qBAAqBA,CAACJ,WAAwB,EAAEK,KAAa;IAClE,MAAMC,WAAW,GAAG,IAAI,CAACP,qBAAqB,CAACC,WAAW,CAAC;IAC3D,OAAOM,WAAW,CAACC,MAAM,CAAEC,KAAK,IAAI;MAAA,IAAAC,EAAA;MAAC,QAAAA,EAAA,GAAAD,KAAK,CAACE,MAAM,cAAAD,EAAA,uBAAAA,EAAA,CAAEnC,QAAQ,CAAC+B,KAAK,CAAC;IAAA,EAAC;EACrE;EAEOM,eAAeA,CAACrD,EAAM;;IAC3B,OAAO,EAAAmD,EAAA,OAAI,CAACnB,kBAAkB,CAAChC,EAAE,CAAC,cAAAmD,EAAA,uBAAAA,EAAA,CAAEC,MAAM,KAAI,EAAE;EAClD;EAEOzB,OAAOA,CAAC3B,EAAM;IACnB,OAAO,IAAI,CAACjB,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,IAAI,CAAC,IAAI,CAACD,OAAO,CAACC,EAAE,CAAC;EACpD;EAEOsD,OAAOA,CAACtD,EAAM;IACnB,OAAO,IAAI,CAACjB,KAAK,CAACuE,OAAO,CAACtD,EAAE,CAAC;EAC/B;EAEOuD,QAAQA,CAACvD,EAAM;IACpB,OAAO,IAAI,CAACjB,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,IAAI,IAAI,CAACD,OAAO,CAACC,EAAE,CAAC;EACnD;EAEOwD,mBAAmBA,CAACxD,EAAM,EAAEyD,SAAA,GAA2B,MAAM;IAClE,OAAO,IAAI,CAAC1E,KAAK,CAAC2E,eAAe,CAAC1D,EAAE,EAAEyD,SAAS,CAAC,CAAChC,GAAG,CAAC5D,QAAQ,CAAe;EAC9E;EAEO8F,oBAAoBA,CAAC3D,EAAM;IAChC,OAAO,IAAI,CAACjB,KAAK,CAAC6E,YAAY,CAAC5D,EAAE,CAAC,CAACyB,GAAG,CAAC5D,QAAQ,CAAC;EAClD;EAEOgG,OAAOA,CAAC/C,IAAe;IAC5B,MAAM;MAAEX,KAAK,EAAE2D,aAAa,GAAG,EAAE;MAAEzD,KAAK,EAAE0D,aAAa,GAAG,EAAE;MAAExD,MAAM,EAAEyD,cAAc,GAAG;IAAE,CAAE,GAAGlD,IAAI;IAClG,MAAM;MAAEX,KAAK,EAAE8D,aAAa;MAAE5D,KAAK,EAAE6D,aAAa;MAAE3D,MAAM,EAAE4D;IAAc,CAAE,GAAG,IAAI,CAACjE,OAAO,EAAE;IAE7F,MAAMkE,QAAQ,GAAGxG,SAAS,CAACqG,aAAa,EAAEH,aAAa,EAAGjD,IAAI,IAAK9C,IAAI,CAAC8C,IAAI,CAAC,EAAEnD,kBAAkB,CAAC;IAClG,MAAM2G,QAAQ,GAAGzG,SAAS,CAACsG,aAAa,EAAEH,aAAa,EAAG3C,IAAI,IAAKrD,IAAI,CAACqD,IAAI,CAAC,EAAE1D,kBAAkB,CAAC;IAClG,MAAM4G,SAAS,GAAG1G,SAAS,CAACuG,cAAc,EAAEH,cAAc,EAAG3C,KAAK,IAAKtD,IAAI,CAACsD,KAAK,CAAC,EAAE3D,kBAAkB,CAAC;IAEvG,IAAI,CAACiC,KAAK,CAAC,MAAK;MACd,MAAM4E,SAAS,GAAG;QAChBpE,KAAK,EAAEiE,QAAQ,CAACI,KAAK;QACrBnE,KAAK,EAAEgE,QAAQ,CAACG,KAAK;QACrBjE,MAAM,EAAE+D,SAAS,CAACE;OACnB;MACD,IAAI,CAACC,OAAO,CAACF,SAAS,CAAC;MACvB,IAAI,CAACG,aAAa,CAACH,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;MAE1C,MAAMI,YAAY,GAAG;QACnBxE,KAAK,EAAEiE,QAAQ,CAACQ,MAAM;QACtBvE,KAAK,EAAEgE,QAAQ,CAACO,MAAM;QACtBrE,MAAM,EAAE+D,SAAS,CAACM;OACnB;MACD,IAAI,CAACC,UAAU,CAACF,YAAY,CAAC;MAC7B,IAAI,CAACD,aAAa,CAACC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC;MAEhD,MAAMG,YAAY,GAAG;QACnB3E,KAAK,EAAEiE,QAAQ,CAACW,IAAI,CAACtD,GAAG,CAAC1D,IAAI,CAAC;QAC9BsC,KAAK,EAAEgE,QAAQ,CAACU,IAAI,CAACtD,GAAG,CAAC1D,IAAI,CAAC;QAC9BwC,MAAM,EAAE+D,SAAS,CAACS,IAAI,CAACtD,GAAG,CAAC1D,IAAI;OAChC;MACD,IAAI,CAACiH,UAAU,CAACF,YAAY,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEOL,OAAOA,CAAC3D,IAAe;IAC5B,MAAM;MAAEX,KAAK;MAAEE,KAAK;MAAEE;IAAM,CAAE,GAAGO,IAAI;IACrC,IAAI,CAACnB,KAAK,CAAC,MAAK;MACd;MACA,IAAI,CAACsF,YAAY,CAAC1E,MAAM,CAAC;MACzB,IAAI,CAAC2E,WAAW,CAAC/E,KAAK,CAAC;MACvB,IAAI,CAACgF,WAAW,CAAC9E,KAAK,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAACqE,aAAa,CAAC5D,IAAI,EAAE,KAAK,CAAC;EACjC;EAEOoE,WAAWA,CAAC/E,KAAA,GAAoB,EAAE;IACvC,IAAI,CAACA,KAAK,CAACiF,MAAM,EAAE;IACnB,IAAI,CAACrG,KAAK,CAACsG,QAAQ,CACjBlF,KAAK,CAACsB,GAAG,CAAEZ,IAAI,IAAI;MACjB,IAAI,CAAC7B,UAAU,CAAC;QAAEM,KAAK,EAAEuB,IAAI;QAAE3B,IAAI,EAAE5B,UAAU,CAACgI;MAAS,CAAE,CAAC;MAC5D,OAAOxH,cAAc,CAAC+C,IAAI,CAAC;IAC7B,CAAC,CAAC,CACH;IACD,IAAI,CAAC0E,uBAAuB,CAACpF,KAAK,CAAC;IAEnC,IAAI,CAACuE,aAAa,CAAC;MAAEvE;IAAK,CAAE,EAAE,KAAK,CAAC;EACtC;EAEOgF,WAAWA,CAAC9E,KAAA,GAAoB,EAAE;IACvC,IAAI,CAACA,KAAK,CAAC+E,MAAM,EAAE;IACnB,IAAI,CAACrG,KAAK,CAACyG,QAAQ,CACjBnF,KAAK,CAACoB,GAAG,CAAEL,IAAI,IAAI;MACjB,IAAI,CAACpC,UAAU,CAAC;QAAEM,KAAK,EAAE8B,IAAI;QAAElC,IAAI,EAAE5B,UAAU,CAACmI;MAAS,CAAE,CAAC;MAC5D,OAAO3H,cAAc,CAACsD,IAAI,CAAC;IAC7B,CAAC,CAAC,CACH;IAED,IAAI,CAACsD,aAAa,CAAC;MAAErE;IAAK,CAAE,EAAE,KAAK,CAAC;EACtC;EAEO4E,YAAYA,CAAC1E,MAAA,GAAsB,EAAE;IAC1C,IAAI,CAACA,MAAM,CAAC6E,MAAM,EAAE;IACpB,MAAM;MAAErG;IAAK,CAAE,GAAG,IAAI;IAEtB,IAAI,CAACA,KAAK,CAAC6C,gBAAgB,CAACvE,SAAS,CAAC,EAAE;MACtC0B,KAAK,CAAC2G,mBAAmB,CAACrI,SAAS,CAAC;IACtC;IAEA0B,KAAK,CAACsG,QAAQ,CACZ9E,MAAM,CAACkB,GAAG,CAAEJ,KAAK,IAAI;MACnB,IAAI,CAAC3C,QAAQ,CAACL,GAAG,CAACN,IAAI,CAACsD,KAAK,CAAC,CAAC;MAC9B,IAAI,CAACrC,UAAU,CAAC;QAAEM,KAAK,EAAE+B,KAAK;QAAEnC,IAAI,EAAE5B,UAAU,CAACqI;MAAU,CAAE,CAAC;MAC9D,OAAO7H,cAAc,CAACuD,KAAK,CAAC;IAC9B,CAAC,CAAC,CACH;IAED,IAAI,CAACkE,uBAAuB,CAAChF,MAAM,CAAC;IAEpC,IAAI,CAACmE,aAAa,CAAC;MAAEnE;IAAM,CAAE,EAAE,KAAK,CAAC;EACvC;EAEOqF,eAAeA,CAACC,QAAY,EAAEC,YAAwB;IAC3D,MAAMC,UAAU,GAAG,IAAI,CAACpD,gBAAgB,CAACkD,QAAQ,CAAa;IAC9D,MAAMG,UAAU,GAAGF,YAAY,CAACrE,GAAG,CAAC1D,IAAI,CAAC;IACzC,IAAI,CAACmH,WAAW,CAACY,YAAY,CAAC;IAC9B,IAAI,CAACG,cAAc,CAAC,CAAC;MAAEjG,EAAE,EAAE6F,QAAQ;MAAEK,QAAQ,EAAE,CAAC,IAAIH,UAAU,CAACG,QAAQ,IAAI,EAAE,CAAC,EAAE,GAAGF,UAAU;IAAC,CAAE,CAAC,CAAC;IAClG,IAAI,CAACb,WAAW,CAACa,UAAU,CAACvE,GAAG,CAAE0E,OAAO,KAAM;MAAEC,MAAM,EAAEP,QAAQ;MAAEQ,MAAM,EAAEF;IAAO,CAAE,CAAC,CAAC,CAAC;EACxF;EAEA;;;;;;;;;;;;;;;;;;EAkBUzB,aAAaA,CAAC5D,IAAsB,EAAE5B,IAAsB,EAAEoH,KAAK,GAAG,KAAK;IACnF,IAAI,CAACA,KAAK,IAAI,IAAI,CAACzG,UAAU,EAAE,EAAE;IACjC,IAAI,CAACF,KAAK,CAAC,MAAK;MACd,MAAM;QAAEQ,KAAK,GAAG,EAAE;QAAEE,KAAK,GAAG,EAAE;QAAEE,MAAM,GAAG;MAAE,CAAE,GAAGO,IAAI;MAEpDP,MAAM,CAACgG,OAAO,CAAElF,KAAK,IAAI;;QACvB,MAAMrB,EAAE,GAAGjC,IAAI,CAACsD,KAAK,CAAC;QACtB,IAAInC,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAAC,CAAAgG,EAAA,GAAA9B,KAAK,CAACmF,KAAK,cAAArD,EAAA,uBAAAA,EAAA,CAAEsD,MAAM,CAAC,EAAE;QACrD,IAAIvH,IAAI,KAAK,QAAQ,IAAI,EAAE,OAAO,IAAImC,KAAK,CAAC,EAAE;QAE9C,MAAMe,MAAM,GAAG,IAAI,CAACF,aAAa,CAAClC,EAAE,EAAE3C,SAAS,CAAC;QAChD,MAAMoJ,MAAM,GAAGrE,MAAM,GAAG,CAAC,CAAAsE,EAAA,IAAAC,EAAA,GAAAvE,MAAM,CAACoE,KAAK,cAAAG,EAAA,uBAAAA,EAAA,CAAEF,MAAM,cAAAC,EAAA,cAAAA,EAAA,GAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAE3D,IAAI,CAACE,8BAA8B,CAAC,MAAK;UACvC,IAAI,CAACC,eAAe,CAAC,CAAC;YAAE7G,EAAE;YAAEwG,KAAK,EAAE;cAAEC;YAAM;UAAE,CAAE,CAAC,CAAC;QACnD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFtG,KAAK,CAACoG,OAAO,CAAE1F,IAAI,IAAI;;QACrB,MAAMb,EAAE,GAAGjC,IAAI,CAAC8C,IAAI,CAAC;QACrB,IAAI3B,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAAC,CAAAgG,EAAA,GAAAtC,IAAI,CAAC2F,KAAK,cAAArD,EAAA,uBAAAA,EAAA,CAAEsD,MAAM,CAAC,EAAE;QACpD,IAAIvH,IAAI,KAAK,QAAQ,IAAI,EAAE,OAAO,IAAI2B,IAAI,CAAC,IAAI,EAAE,UAAU,IAAIA,IAAI,CAAC,EAAE;QAEtE,IAAI4F,MAAM,GAAG,CAAC;QAEd,MAAMK,WAAW,GAAG,IAAI,CAAC5E,aAAa,CAAClC,EAAE,EAAE3C,SAAS,CAAC;QACrD,IAAIyJ,WAAW,EAAE;UACfL,MAAM,GAAG,CAAC,EAAAE,EAAA,GAAAG,WAAW,CAACN,KAAK,cAAAG,EAAA,uBAAAA,EAAA,CAAEF,MAAM,KAAI,CAAC,IAAI,CAAC;QAC/C,CAAC,MAAM;UACL,MAAMM,UAAU,GAAG,IAAI,CAAC7E,aAAa,CAAClC,EAAE,EAAEzC,QAAQ,CAAC;UACnD,IAAIwJ,UAAU,EAAEN,MAAM,GAAG,EAAAC,EAAA,GAAAK,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEP,KAAK,cAAAE,EAAA,uBAAAA,EAAA,CAAED,MAAM,KAAI,CAAC;QACzD;QAEA,IAAI,CAACG,8BAA8B,CAAC,MAAK;UACvC,IAAI,CAACX,cAAc,CAAC,CAAC;YAAEjG,EAAE;YAAEwG,KAAK,EAAE;cAAEC;YAAM;UAAE,CAAE,CAAC,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpG,KAAK,CAACkG,OAAO,CAAEnF,IAAI,IAAI;;QACrB,IAAIjE,QAAQ,CAAC,CAAAgG,EAAA,GAAA/B,IAAI,CAACoF,KAAK,cAAArD,EAAA,uBAAAA,EAAA,CAAEsD,MAAM,CAAC,EAAE;QAElC,IAAI;UAAEzG,EAAE;UAAEoG,MAAM;UAAEC;QAAM,CAAE,GAAGjF,IAAI;QACjC,IAAI,CAACpB,EAAE,EAAEA,EAAE,GAAGjC,IAAI,CAACqD,IAAI,CAAC,CAAC,KACpB;UACH,MAAM8B,KAAK,GAAG,IAAI,CAACjC,YAAY,CAACjB,EAAE,CAAC;UACnCoG,MAAM,GAAGlD,KAAK,CAACkD,MAAM;UACrBC,MAAM,GAAGnD,KAAK,CAACmD,MAAM;QACvB;QAEA,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,EAAE;QAExB,MAAMW,YAAY,GAAG,EAAAN,EAAA,IAAAC,EAAA,OAAI,CAAChE,gBAAgB,CAACyD,MAAM,CAAC,cAAAO,EAAA,uBAAAA,EAAA,CAAEH,KAAK,cAAAE,EAAA,uBAAAA,EAAA,CAAED,MAAM,KAAI,CAAC;QACtE,MAAMQ,YAAY,GAAG,EAAAC,EAAA,IAAAC,EAAA,OAAI,CAACxE,gBAAgB,CAAC0D,MAAM,CAAC,cAAAc,EAAA,uBAAAA,EAAA,CAAEX,KAAK,cAAAU,EAAA,uBAAAA,EAAA,CAAET,MAAM,KAAI,CAAC;QAEtE,IAAI,CAACW,cAAc,CAAC,CAAC;UAAEpH,EAAE,EAAEjC,IAAI,CAACqD,IAAI,CAAC;UAAEoF,KAAK,EAAE;YAAEC,MAAM,EAAEY,IAAI,CAACC,GAAG,CAACN,YAAY,EAAEC,YAAY,CAAC,GAAG;UAAC;QAAE,CAAE,CAAC,CAAC;MACxG,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOOM,cAAcA,CAACvH,EAAM;;IAC1B,MAAM0C,WAAW,GAAG,IAAI,CAACH,cAAc,CAACvC,EAAE,CAAC;IAC3C,MAAMgD,WAAW,GAAG,IAAI,CAAChB,kBAAkB,CAAChC,EAAE,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACZ,OAAO,EAAE;IAE3B;IACAsH,MAAM,CAACC,MAAM,CAAC3G,IAAI,EAAE;MAClB,CAAC,GAAG4B,WAAW,GAAG,GAAG5B,IAAI,CAAC,GAAG4B,WAAW,GAAG,CAAC,CAACO,MAAM,CAAEyE,OAAO,IAAK3J,IAAI,CAAC2J,OAAO,CAAC,KAAK1H,EAAE;KACtF,CAAC;IAEF,IAAI0C,WAAW,KAAK,OAAO,EAAE;MAC3B;MACA;MACA,IAAI,CAAClF,WAAW,CAACwF,WAAwB,CAAC,EAAE;QAC1C,MAAM2E,WAAW,GAAG,IAAIlJ,GAAG,CAAC,IAAI,CAACiD,gBAAgB,CAAC1B,EAAE,EAAE3C,SAAS,CAAC,CAACoE,GAAG,CAAC1D,IAAI,CAAC,CAAC;QAC3E+C,IAAI,CAACX,KAAK,GAAGW,IAAI,CAACX,KAAK,CAAC8C,MAAM,CAAEyE,OAAO,IAAK,CAACC,WAAW,CAAC1H,GAAG,CAAClC,IAAI,CAAC2J,OAAO,CAAC,CAAC,CAAC;QAC5E5G,IAAI,CAACP,MAAM,GAAGO,IAAI,CAACP,MAAM,CAAC0C,MAAM,CAAEyE,OAAO,IAAK,CAACC,WAAW,CAAC1H,GAAG,CAAClC,IAAI,CAAC2J,OAAO,CAAC,CAAC,CAAC;QAC9E5G,IAAI,CAACT,KAAK,GAAGS,IAAI,CAACT,KAAK,CAAC4C,MAAM,CAAC,CAAC;UAAEmD,MAAM;UAAEC;QAAM,CAAE,KAAK,CAACsB,WAAW,CAAC1H,GAAG,CAACmG,MAAM,CAAC,IAAI,CAACuB,WAAW,CAAC1H,GAAG,CAACoG,MAAM,CAAC,CAAC;MAC9G;IACF;IAEA,OAAOgB,IAAI,CAACC,GAAG,CACb,EAAAnE,EAAA,GAAAH,WAAW,CAACwD,KAAK,cAAArD,EAAA,uBAAAA,EAAA,CAAEsD,MAAM,KAAI,CAAC,EAC9B,CAAC,EACD,GAAGe,MAAM,CAACI,MAAM,CAAC9G,IAAI,CAAC,CACnB+G,IAAI,EAAE,CACNpG,GAAG,CAAEyB,KAAK,IAAI;MAAA,IAAAC,EAAA;MAAC,QAAC,EAAAA,EAAA,GAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsD,KAAK,cAAArD,EAAA,uBAAAA,EAAA,CAAEsD,MAAM,KAAI,CAAC,IAAI,CAAC;IAAA,EAAC,CACnD;EACH;EAEUlB,uBAAuBA,CAACzE,IAAoB;IACpD,IAAI,CAAC,IAAI,CAAChC,6BAA6B,EAAE;IACzC,MAAM;MAAEC;IAAK,CAAE,GAAG,IAAI;IAEtB+B,IAAI,CAACyF,OAAO,CAAErD,KAAK,IAAI;MACrB,MAAMlD,EAAE,GAAGjC,IAAI,CAACmF,KAAK,CAAC;MACtB,MAAMd,MAAM,GAAGpE,UAAU,CAACkF,KAAK,CAAC;MAEhC,IAAId,MAAM,KAAKrB,SAAS,EAAE;QACxB,IAAI,CAAChC,KAAK,CAAC6C,gBAAgB,CAACvE,SAAS,CAAC,EAAE0B,KAAK,CAAC2G,mBAAmB,CAACrI,SAAS,CAAC;QAE5E;QACA;QACA,IAAI+E,MAAM,KAAK,IAAI,EAAE;UACnB,IAAI,CAAC0F,gBAAgB,CAAC9H,EAAE,CAAC;QAC3B;QAEA,IAAI,CAAC+H,SAAS,CAAC/H,EAAE,EAAEhC,UAAU,CAACkF,KAAK,CAAC,EAAE7F,SAAS,CAAC;MAClD;MAEA,MAAM6I,QAAQ,GAAIhD,KAAkB,CAACgD,QAAQ,IAAI,EAAE;MACnD,IAAIA,QAAQ,CAACd,MAAM,EAAE;QACnB,IAAI,CAACrG,KAAK,CAAC6C,gBAAgB,CAACrE,QAAQ,CAAC,EAAEwB,KAAK,CAAC2G,mBAAmB,CAACnI,QAAQ,CAAC;QAC1E,MAAMyK,SAAS,GAAG9B,QAAQ,CAACjD,MAAM,CAAEgF,KAAK,IAAKlJ,KAAK,CAAC4C,OAAO,CAACsG,KAAK,CAAC,CAAC;QAClED,SAAS,CAACzB,OAAO,CAAE0B,KAAK,IAAK,IAAI,CAACF,SAAS,CAACE,KAAK,EAAEjI,EAAE,EAAEzC,QAAQ,CAAC,CAAC;QACjE,IAAIyK,SAAS,CAAC5C,MAAM,KAAKc,QAAQ,CAACd,MAAM,EAAE;UACxC;UACA;UACA,IAAI,CAACa,cAAc,CAAC,CAAC;YAAEjG,EAAE;YAAEkG,QAAQ,EAAE8B;UAAS,CAAE,CAAC,CAAC;QACpD;MACF;IACF,CAAC,CAAC;EACJ;EAIA;;;;;;EAMOpB,8BAA8BA,CAAChH,QAAoB;IACxD,IAAI,CAACd,6BAA6B,GAAG,KAAK;IAC1Cc,QAAQ,EAAE;IACV,IAAI,CAACd,6BAA6B,GAAG,IAAI;EAC3C;EAEO+F,UAAUA,CAAC/D,IAAsB;IACtC,MAAM;MAAEX,KAAK;MAAEE,KAAK;MAAEE;IAAM,CAAE,GAAGO,IAAI;IACrC,IAAI,CAACnB,KAAK,CAAC,MAAK;MACd,IAAI,CAACsG,cAAc,CAAC9F,KAAK,CAAC;MAC1B,IAAI,CAAC0G,eAAe,CAACtG,MAAM,CAAC;MAC5B,IAAI,CAAC6G,cAAc,CAAC/G,KAAK,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACqE,aAAa,CAAC5D,IAAI,EAAE,QAAQ,CAAC;EACpC;EAEOmF,cAAcA,CAAC9F,KAAA,GAAyC,EAAE;IAC/D,IAAI,CAACA,KAAK,CAACiF,MAAM,EAAE;IACnB,MAAM;MAAErG;IAAK,CAAE,GAAG,IAAI;IACtB,IAAI,CAACY,KAAK,CAAC,MAAK;MACd,MAAMmE,aAAa,GAAe,EAAE;MACpC3D,KAAK,CAACoG,OAAO,CAAE2B,YAAY,IAAI;QAC7B,MAAMlI,EAAE,GAAGjC,IAAI,CAACmK,YAAY,CAAC;QAC7B,MAAMC,YAAY,GAAGtK,QAAQ,CAACkB,KAAK,CAAC6D,OAAO,CAAC5C,EAAE,CAAC,CAAC;QAChD,IAAItC,kBAAkB,CAACyK,YAAY,EAAED,YAAY,CAAC,EAAE;QAEpD,MAAM5I,KAAK,GAAG3B,iBAAiB,CAACwK,YAAY,EAAED,YAAY,CAAC;QAC3D,IAAI,CAAClJ,UAAU,CAAC;UAAEM,KAAK;UAAEC,QAAQ,EAAE4I,YAAY;UAAEjJ,IAAI,EAAE5B,UAAU,CAAC6B;QAAW,CAAE,CAAC;QAChFJ,KAAK,CAACqJ,aAAa,CAACpI,EAAE,EAAEV,KAAK,CAAC;QAC9BwE,aAAa,CAACtE,IAAI,CAACF,KAAK,CAAC;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACiG,uBAAuB,CAACzB,aAAa,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACY,aAAa,CAAC;MAAEvE;IAAK,CAAE,EAAE,QAAQ,CAAC;EACzC;EAEA;;;;;EAKOkI,WAAWA,CAAA;IAChB,MAAM;MAAElI,KAAK;MAAEE,KAAK;MAAEE;IAAM,CAAE,GAAG,IAAI,CAACL,OAAO,EAAE;IAC/CC,KAAK,CAACoG,OAAO,CAAE1F,IAAI,IAAI;MACrB,IAAI,CAAC7B,UAAU,CAAC;QAAEM,KAAK,EAAEuB,IAAI;QAAEtB,QAAQ,EAAEsB,IAAI;QAAE3B,IAAI,EAAE5B,UAAU,CAAC6B;MAAW,CAAE,CAAC;IAChF,CAAC,CAAC;IACFkB,KAAK,CAACkG,OAAO,CAAEnF,IAAI,IAAI;MACrB,IAAI,CAACpC,UAAU,CAAC;QAAEM,KAAK,EAAE8B,IAAI;QAAE7B,QAAQ,EAAE6B,IAAI;QAAElC,IAAI,EAAE5B,UAAU,CAAC8B;MAAW,CAAE,CAAC;IAChF,CAAC,CAAC;IACFmB,MAAM,CAACgG,OAAO,CAAElF,KAAK,IAAI;MACvB,IAAI,CAACrC,UAAU,CAAC;QAAEM,KAAK,EAAE+B,KAAK;QAAE9B,QAAQ,EAAE8B,KAAK;QAAEnC,IAAI,EAAE5B,UAAU,CAAC+B;MAAY,CAAE,CAAC;IACnF,CAAC,CAAC;EACJ;EAEOiJ,iBAAiBA,CAACpF,KAAoC;IAC3D,MAAM;MAAEnE;IAAK,CAAE,GAAG,IAAI;IAEtB,MAAMiB,EAAE,GAAGjC,IAAI,CAACmF,KAAK,CAAC;IACtB,IAAI,CAACnE,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,EAAE;IACxB,MAAMT,QAAQ,GAAG1B,QAAQ,CAACkB,KAAK,CAAC6D,OAAO,CAAC5C,EAAE,CAAC,CAAC;IAC5C,MAAMV,KAAK,GAAG3B,iBAAiB,CAAC4B,QAAQ,EAAE2D,KAAK,CAAC;IAChDnE,KAAK,CAACqJ,aAAa,CAACpI,EAAE,EAAEV,KAAK,CAAC;EAChC;EAEOiJ,aAAaA,CAACrF,KAAgC;IACnD,MAAM;MAAEnE;IAAK,CAAE,GAAG,IAAI;IAEtB,MAAMiB,EAAE,GAAGjC,IAAI,CAACmF,KAAK,CAAC;IACtB,IAAI,CAACnE,KAAK,CAACuE,OAAO,CAACtD,EAAE,CAAC,EAAE;IACxB,MAAMT,QAAQ,GAAG1B,QAAQ,CAACkB,KAAK,CAACmC,OAAO,CAAClB,EAAE,CAAC,CAAC;IAC5C,MAAMV,KAAK,GAAG3B,iBAAiB,CAAC4B,QAAQ,EAAE2D,KAAK,CAAC;IAChDnE,KAAK,CAACyJ,aAAa,CAACxI,EAAE,EAAEV,KAAK,CAAC;EAChC;EAEO8H,cAAcA,CAAC/G,KAAA,GAAqC,EAAE;IAC3D,IAAI,CAACA,KAAK,CAAC+E,MAAM,EAAE;IACnB,MAAM;MAAErG;IAAK,CAAE,GAAG,IAAI;IACtB,IAAI,CAACY,KAAK,CAAC,MAAK;MACdU,KAAK,CAACkG,OAAO,CAAEkC,YAAY,IAAI;QAC7B,MAAMzI,EAAE,GAAGjC,IAAI,CAAC0K,YAAY,CAAC;QAC7B,MAAMC,YAAY,GAAG7K,QAAQ,CAACkB,KAAK,CAACmC,OAAO,CAAClB,EAAE,CAAC,CAAC;QAChD,IAAItC,kBAAkB,CAACgL,YAAY,EAAED,YAAY,CAAC,EAAE;QAEpD,IAAIA,YAAY,CAACrC,MAAM,IAAIsC,YAAY,CAACtC,MAAM,KAAKqC,YAAY,CAACrC,MAAM,EAAE;UACtErH,KAAK,CAAC4J,gBAAgB,CAAC3I,EAAE,EAAEyI,YAAY,CAACrC,MAAM,CAAC;QACjD;QACA,IAAIqC,YAAY,CAACpC,MAAM,IAAIqC,YAAY,CAACrC,MAAM,KAAKoC,YAAY,CAACpC,MAAM,EAAE;UACtEtH,KAAK,CAAC6J,gBAAgB,CAAC5I,EAAE,EAAEyI,YAAY,CAACpC,MAAM,CAAC;QACjD;QACA,MAAMwC,WAAW,GAAGlL,iBAAiB,CAAC+K,YAAY,EAAED,YAAY,CAAC;QACjE,IAAI,CAACzJ,UAAU,CAAC;UAAEM,KAAK,EAAEuJ,WAAW;UAAEtJ,QAAQ,EAAEmJ,YAAY;UAAExJ,IAAI,EAAE5B,UAAU,CAAC8B;QAAW,CAAE,CAAC;QAC7FL,KAAK,CAACyJ,aAAa,CAACxI,EAAE,EAAE6I,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACnE,aAAa,CAAC;MAAErE;IAAK,CAAE,EAAE,QAAQ,CAAC;EACzC;EAEOwG,eAAeA,CAACtG,MAAA,GAA2C,EAAE;IAClE,IAAI,CAACA,MAAM,CAAC6E,MAAM,EAAE;IACpB,MAAM;MAAErG;IAAK,CAAE,GAAG,IAAI;IACtBA,KAAK,CAACY,KAAK,CAAC,MAAK;MACf,MAAMqE,cAAc,GAAgB,EAAE;MACtCzD,MAAM,CAACgG,OAAO,CAAEuC,aAAa,IAAI;QAC/B,MAAM9I,EAAE,GAAGjC,IAAI,CAAC+K,aAAa,CAAC;QAC9B,MAAMC,aAAa,GAAGlL,QAAQ,CAACkB,KAAK,CAAC6D,OAAO,CAAC5C,EAAE,CAAC,CAAc;QAC9D,IAAItC,kBAAkB,CAACqL,aAAa,EAAED,aAAa,CAAC,EAAE;QAEtD,MAAMxJ,KAAK,GAAG3B,iBAAiB,CAACoL,aAAa,EAAED,aAAa,CAAC;QAC7D,IAAI,CAAC9J,UAAU,CAAC;UAAEM,KAAK;UAAEC,QAAQ,EAAEwJ,aAAa;UAAE7J,IAAI,EAAE5B,UAAU,CAAC+B;QAAY,CAAE,CAAC;QAClFN,KAAK,CAACqJ,aAAa,CAACpI,EAAE,EAAEV,KAAK,CAAC;QAC9B0E,cAAc,CAACxE,IAAI,CAACF,KAAK,CAAC;MAC5B,CAAC,CAAC;MAEF,IAAI,CAACiG,uBAAuB,CAACvB,cAAc,CAAC;IAC9C,CAAC,CAAC;IAEF,IAAI,CAACU,aAAa,CAAC;MAAEnE;IAAM,CAAE,EAAE,QAAQ,CAAC;EAC1C;EAEA;;;;;;;;;EASOwH,SAASA,CAAC/H,EAAM,EAAEoC,MAA6B,EAAEb,YAA0B,EAAEqD,MAAA,GAAkB,IAAI;IACxG,IAAI5E,EAAE,KAAKoC,MAAM,EAAE;IACnB,MAAMY,WAAW,GAAG,IAAI,CAACL,gBAAgB,CAAC3C,EAAE,CAAC;IAC7C,MAAMgJ,gBAAgB,GAAGhL,UAAU,CAACgF,WAAW,CAAC;IAEhD,IAAIgG,gBAAgB,KAAK5G,MAAM,IAAIb,YAAY,KAAKlE,SAAS,EAAE;MAC7D,MAAM4L,aAAa,GAAG;QAAEjJ,EAAE;QAAEqB,KAAK,EAAEe;MAAM,CAAE;MAC3C,IAAI,IAAI,CAACrC,OAAO,CAACC,EAAE,CAAC,EAAE,IAAI,CAACsI,iBAAiB,CAACW,aAAa,CAAC,CAAC,KACvD,IAAI,CAACX,iBAAiB,CAACW,aAAa,CAAC;IAC5C;IAEA,IAAI,CAAClK,KAAK,CAACgJ,SAAS,CAAC/H,EAAE,EAAEoC,MAAM,EAAEb,YAAY,CAAC;IAE9C,IAAIqD,MAAM,IAAIrD,YAAY,KAAKlE,SAAS,EAAE;MACxCD,IAAI,CAAC,CAAC4L,gBAAgB,EAAE5G,MAAM,CAAC,CAAC,CAACmE,OAAO,CAAE2C,GAAG,IAAI;QAC/C,IAAIA,GAAG,KAAKnI,SAAS,EAAE,IAAI,CAAC+G,gBAAgB,CAACoB,GAAG,CAAC;MACnD,CAAC,CAAC;IACJ;EACF;EAEA;;;;;;;;;;EAUOpB,gBAAgBA,CAAC9H,EAAM;IAC5B,MAAMqB,KAAK,GAAG,IAAI,CAACb,YAAY,CAAC,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,MAAMmJ,SAAS,GAAG,IAAI,CAACzH,gBAAgB,CAAC1B,EAAE,EAAE3C,SAAS,CAAgB;IAErE,IAAIgE,KAAK,EAAE,IAAI,CAACrC,UAAU,CAAC;MAAEM,KAAK,EAAE+B,KAAK;MAAE9B,QAAQ,EAAE8B,KAAK;MAAEnC,IAAI,EAAE5B,UAAU,CAAC+B;IAAY,CAAE,CAAC;IAE5F8J,SAAS,CAAC5C,OAAO,CAAEjH,KAAK,IAAI;MAC1B,IAAI,CAACN,UAAU,CAAC;QAAEM,KAAK,EAAEA,KAAK;QAAEC,QAAQ,EAAED,KAAK;QAAEJ,IAAI,EAAE5B,UAAU,CAAC+B;MAAY,CAAE,CAAC;IACnF,CAAC,CAAC;EACJ;EAEO+J,kBAAkBA,CAACpJ,EAAM;IAC9B,MAAMkD,KAAK,GAAG,IAAI,CAAClB,kBAAkB,CAAChC,EAAE,CAAiB;IACzD,OAAO/B,UAAU,CAACiF,KAAK,CAAC;EAC1B;EAEOmG,mBAAmBA,CAACrJ,EAAM,EAAEsJ,MAAa;IAC9C,IAAI,IAAI,CAACvJ,OAAO,CAACC,EAAE,CAAC,EAAE,IAAI,CAACuJ,gBAAgB,CAACvJ,EAAE,EAAEsJ,MAAM,CAAC,CAAC,KACnD,IAAI,CAACE,eAAe,CAACxJ,EAAE,EAAEsJ,MAAM,CAAC;EACvC;EAEOG,mBAAmBA,CAACzJ,EAAM,EAAE0J,QAAe;IAChD,IAAI,IAAI,CAAC3J,OAAO,CAACC,EAAE,CAAC,EAAE,IAAI,CAAC2J,gBAAgB,CAAC3J,EAAE,EAAE0J,QAAQ,CAAC,CAAC,KACrD,IAAI,CAACE,eAAe,CAAC5J,EAAE,EAAE0J,QAAQ,CAAC;EACzC;EAEOF,eAAeA,CAACxJ,EAAM,EAAEsJ,MAAa;IAC1C,MAAMO,IAAI,GAAG,IAAI,CAACT,kBAAkB,CAACpJ,EAAE,CAAC;IACxC,MAAM0J,QAAQ,GAAGrL,GAAG,CAACwL,IAAI,EAAE,CAAC,GAAGP,MAAM,EAAE,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAU,CAAC;IAC/D,IAAI,CAACF,eAAe,CAAC5J,EAAE,EAAE0J,QAAQ,CAAC;EACpC;EAEOE,eAAeA,CAAC5J,EAAM,EAAE0J,QAAe;IAC5C,MAAM,CAACK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,GAAGP,QAAQ;IACtC,IAAI,CAAC9C,8BAA8B,CAAC,MAAK;MACvC,IAAI,CAACX,cAAc,CAAC,CAAC;QAAEjG,EAAE;QAAEwG,KAAK,EAAE;UAAEuD,CAAC;UAAEC,CAAC;UAAEC;QAAC;MAAE,CAAE,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;EAEOV,gBAAgBA,CAACvJ,EAAM,EAAEsJ,MAAa;IAC3C,MAAM,CAACY,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,GAAGd,MAAM;IACvC,IAAI,CAACY,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACG,KAAK,CAAEC,CAAC,IAAKA,CAAC,KAAK,CAAC,CAAC,EAAE;IACpE,MAAMnJ,KAAK,GAAG,IAAI,CAACb,YAAY,CAAC,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,CAACqB,KAAK,EAAE;IACZ,MAAMoJ,eAAe,GAAG,IAAIhM,GAAG,EAAM;IACrCL,GAAG,CACDiD,KAAK,EACJqJ,OAAO,IAAI;MACV,MAAMC,SAAS,GAAG5M,IAAI,CAAC2M,OAAO,CAAC;MAC/B,IAAID,eAAe,CAACxK,GAAG,CAAC0K,SAAS,CAAC,EAAE;MACpCF,eAAe,CAACpM,GAAG,CAACsM,SAAS,CAAC;MAC9B,MAAM,CAACZ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGhM,UAAU,CAACyM,OAAO,CAAC;MACrC,MAAMpL,KAAK,GAAG3B,iBAAiB,CAAC+M,OAAO,EAAE;QACvClE,KAAK,EAAE;UAAEuD,CAAC,EAAEA,CAAC,GAAGG,EAAE;UAAEF,CAAC,EAAEA,CAAC,GAAGG,EAAE;UAAEF,CAAC,EAAEA,CAAC,GAAGG;QAAE;OACzC,CAAC;MACF,IAAI,CAACpL,UAAU,CAAC;QACdM,KAAK;QACL;QACAC,QAAQ,EAAEmL,OAAO;QACjBxL,IAAI,EAAE,IAAI,CAACa,OAAO,CAAC4K,SAAS,CAAC,GAAGrN,UAAU,CAAC+B,YAAY,GAAG/B,UAAU,CAAC6B;OACtE,CAAC;MAEF,IAAI,CAACJ,KAAK,CAACqJ,aAAa,CAACuC,SAAS,EAAErL,KAAK,CAAC;IAC5C,CAAC,EACAuB,IAAI,IAAK,IAAI,CAACoB,eAAe,CAAClE,IAAI,CAAC8C,IAAI,CAAC,CAAC,EAC1C,IAAI,CACL;EACH;EAEO8I,gBAAgBA,CAAC3J,EAAM,EAAE0J,QAAe;;IAC7C,IAAIA,QAAQ,CAACW,IAAI,CAACC,KAAK,CAAC,EAAE;IAC1B,MAAM,CAACM,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,GAAGpB,QAAQ;IACzC,MAAMrI,KAAK,GAAG,CAAA8B,EAAA,OAAI,CAAC3C,YAAY,CAAC,CAACR,EAAE,CAAC,CAAC,cAAAmD,EAAA,uBAAAA,EAAA,CAAG,CAAC,CAAC;IAC1C,IAAI,CAAC9B,KAAK,EAAE;IAEZ,MAAM,CAAC0J,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,GAAGhN,UAAU,CAACoD,KAAK,CAAC;IAClD,MAAM6I,EAAE,GAAGU,EAAE,GAAGG,MAAM;IACtB,MAAMZ,EAAE,GAAGU,EAAE,GAAGG,MAAM;IACtB,MAAMZ,EAAE,GAAGU,EAAE,GAAGG,MAAM;IAEtB7M,GAAG,CACDiD,KAAK,EACJqJ,OAAO,IAAI;MACV,MAAMQ,SAAS,GAAGnN,IAAI,CAAC2M,OAAO,CAAC;MAC/B,MAAM,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGhM,UAAU,CAACyM,OAAO,CAAC;MACrC,MAAMpL,KAAK,GAAG3B,iBAAiB,CAAC+M,OAAO,EAAE;QACvClE,KAAK,EAAE;UAAEuD,CAAC,EAAEA,CAAC,GAAGG,EAAE;UAAEF,CAAC,EAAEA,CAAC,GAAGG,EAAE;UAAEF,CAAC,EAAEA,CAAC,GAAGG;QAAE;OACzC,CAAC;MACF,IAAI,CAACpL,UAAU,CAAC;QACdM,KAAK;QACL;QACAC,QAAQ,EAAEmL,OAAO;QACjBxL,IAAI,EAAE,IAAI,CAACa,OAAO,CAACmL,SAAS,CAAC,GAAG5N,UAAU,CAAC+B,YAAY,GAAG/B,UAAU,CAAC6B;OACtE,CAAC;MACF,IAAI,CAACJ,KAAK,CAACqJ,aAAa,CAAC8C,SAAS,EAAE5L,KAAK,CAAC;IAC5C,CAAC,EACAuB,IAAI,IAAK,IAAI,CAACoB,eAAe,CAAClE,IAAI,CAAC8C,IAAI,CAAC,CAAC,EAC1C,IAAI,CACL;EACH;EAEOmE,UAAUA,CAAClE,IAAY;IAC5B,MAAM;MAAEX,KAAK;MAAEE,KAAK;MAAEE;IAAM,CAAE,GAAGO,IAAI;IACrC,IAAI,CAACnB,KAAK,CAAC,MAAK;MACd;MACA,IAAI,CAACwL,cAAc,CAAC9K,KAAK,CAAC;MAC1B,IAAI,CAAC+K,cAAc,CAACjL,KAAK,CAAC;MAC1B,IAAI,CAACkL,eAAe,CAAC9K,MAAM,CAAC;MAE5B,IAAI,CAAC/B,qBAAqB,GAAG,IAAIC,GAAG,CAAC8B,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEO6K,cAAcA,CAAC3K,GAAA,GAAY,EAAE;IAClC,IAAI,CAACA,GAAG,CAAC2E,MAAM,EAAE;IACjB,IAAI,CAACzF,KAAK,CAAC,MAAK;MACdc,GAAG,CAAC8F,OAAO,CAAEvG,EAAE,IAAI;QACjB;QACA;QACA,IAAI,CAACmL,cAAc,CAAC,IAAI,CAAC3H,mBAAmB,CAACxD,EAAE,CAAC,CAACyB,GAAG,CAAC1D,IAAI,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACiB,UAAU,CAAC;UAAEM,KAAK,EAAE,IAAI,CAACc,WAAW,CAAC,CAACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAAEd,IAAI,EAAE5B,UAAU,CAACgO;QAAW,CAAE,CAAC;QACnF,IAAI,CAACC,uBAAuB,CAACvL,EAAE,CAAC;MAClC,CAAC,CAAC;MACF,IAAI,CAACjB,KAAK,CAACyM,WAAW,CAAC/K,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEO0K,cAAcA,CAAC1K,GAAA,GAAY,EAAE;IAClC,IAAI,CAACA,GAAG,CAAC2E,MAAM,EAAE;IACjB3E,GAAG,CAAC8F,OAAO,CAAEvG,EAAE,IAAK,IAAI,CAAChB,UAAU,CAAC;MAAEM,KAAK,EAAE,IAAI,CAACgB,WAAW,CAAC,CAACN,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEd,IAAI,EAAE5B,UAAU,CAACmO;IAAW,CAAE,CAAC,CAAC;IACxG,IAAI,CAAC1M,KAAK,CAAC2M,WAAW,CAACjL,GAAG,CAAC;EAC7B;EAEO4K,eAAeA,CAAC5K,GAAA,GAAY,EAAE;IACnC,IAAI,CAACA,GAAG,CAAC2E,MAAM,EAAE;IACjB,IAAI,CAACzF,KAAK,CAAC,MAAK;MACdc,GAAG,CAAC8F,OAAO,CAAEvG,EAAE,IAAI;QACjB,IAAI,CAAChB,UAAU,CAAC;UAAEM,KAAK,EAAE,IAAI,CAACkB,YAAY,CAAC,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAAEd,IAAI,EAAE5B,UAAU,CAACqO;QAAY,CAAE,CAAC;QACrF,IAAI,CAACJ,uBAAuB,CAACvL,EAAE,CAAC;QAChC,IAAI,CAACtB,QAAQ,CAACkN,MAAM,CAAC5L,EAAE,CAAC;MAC1B,CAAC,CAAC;MACF,IAAI,CAACjB,KAAK,CAACyM,WAAW,CAAC/K,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEA;;;;;;EAMU8K,uBAAuBA,CAACvL,EAAM;IACtC,IAAI,IAAI,CAACjB,KAAK,CAAC6C,gBAAgB,CAACvE,SAAS,CAAC,EAAE;MAC1C,MAAMwO,WAAW,GAAG7N,UAAU,CAAC,IAAI,CAAC2E,gBAAgB,CAAC3C,EAAE,CAAC,CAAC;MAEzD;MACA;MACA;MACA,IAAI,CAAC+H,SAAS,CAAC/H,EAAE,EAAEe,SAAS,EAAE1D,SAAS,EAAE,KAAK,CAAC;MAC/C;MACA;MAEA,IAAI,CAAC0B,KAAK,CAACyD,WAAW,CAACxC,EAAE,EAAE3C,SAAS,CAAC,CAACkJ,OAAO,CAAE0B,KAAK,IAAI;QACtD,MAAM6D,SAAS,GAAGjO,QAAQ,CAACoK,KAAK,CAAC;QACjC,MAAM9B,OAAO,GAAGpI,IAAI,CAAC+N,SAAS,CAAC;QAC/B,IAAI,CAAC/D,SAAS,CAAChK,IAAI,CAAC+N,SAAS,CAAC,EAAED,WAAW,EAAExO,SAAS,EAAE,KAAK,CAAC;QAC9D,MAAMiC,KAAK,GAAG3B,iBAAiB,CAACmO,SAAS,EAAE;UACzC9L,EAAE,EAAEjC,IAAI,CAAC+N,SAAS,CAAC;UACnBzK,KAAK,EAAEwK;SACR,CAAC;QACF,IAAI,CAAC7M,UAAU,CAAC;UACdM,KAAK;UACLC,QAAQ,EAAEuM,SAAS;UACnB5M,IAAI,EAAE,IAAI,CAACa,OAAO,CAACoG,OAAO,CAAC,GAAG7I,UAAU,CAAC+B,YAAY,GAAG/B,UAAU,CAAC6B;SACpE,CAAC;QACF,IAAI,CAACJ,KAAK,CAACqJ,aAAa,CAACrK,IAAI,CAAC+N,SAAS,CAAC,EAAExM,KAAK,CAAC;MAClD,CAAC,CAAC;MAEF,IAAI,CAACpC,KAAK,CAAC2O,WAAW,CAAC,EAAE,IAAI,CAAC/D,gBAAgB,CAAC+D,WAAW,CAAC;IAC7D;EACF;EAEA;;;;;;;EAOOtJ,cAAcA,CAACvC,EAAM;IAC1B,IAAI,IAAI,CAACjB,KAAK,CAAC4C,OAAO,CAAC3B,EAAE,CAAC,EAAE;MAC1B,IAAI,IAAI,CAACD,OAAO,CAACC,EAAE,CAAC,EAAE,OAAO,OAAO;MACpC,OAAO,MAAM;IACf;IAEA,IAAI,IAAI,CAACjB,KAAK,CAACuE,OAAO,CAACtD,EAAE,CAAC,EAAE,OAAO,MAAM;IAEzC,MAAM,IAAI+L,KAAK,CAAC7N,MAAM,CAAC,+BAA+B8B,EAAE,EAAE,CAAC,CAAC;EAC9D;EAEOgM,OAAOA,CAAA;IACZ,MAAM;MAAEjN;IAAK,CAAE,GAAG,IAAI;IACtB,MAAMoB,KAAK,GAAGpB,KAAK,CAAC2B,WAAW,EAAE;IACjC,MAAML,KAAK,GAAGtB,KAAK,CAACoC,WAAW,EAAE;IAEjCpC,KAAK,CAAC2M,WAAW,CAACrL,KAAK,CAACoB,GAAG,CAAEL,IAAI,IAAKA,IAAI,CAACpB,EAAE,CAAC,CAAC;IAC/CjB,KAAK,CAACyM,WAAW,CAACrL,KAAK,CAACsB,GAAG,CAAEZ,IAAI,IAAKA,IAAI,CAACb,EAAE,CAAC,CAAC;IAE/C;IACA,IAAI,CAACiM,OAAO,GAAG,EAAE;EACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}