{"ast": null, "code": "export var inline = false;\nexport function inlineMock(nextInline) {\n  if (typeof nextInline === 'boolean') {\n    inline = nextInline;\n  }\n  return inline;\n}", "map": {"version": 3, "names": ["inline", "inlineMock", "nextInline"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@rc-component/portal/es/mock.js"], "sourcesContent": ["export var inline = false;\nexport function inlineMock(nextInline) {\n  if (typeof nextInline === 'boolean') {\n    inline = nextInline;\n  }\n  return inline;\n}"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG,KAAK;AACzB,OAAO,SAASC,UAAUA,CAACC,UAAU,EAAE;EACrC,IAAI,OAAOA,UAAU,KAAK,SAAS,EAAE;IACnCF,MAAM,GAAGE,UAAU;EACrB;EACA,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}