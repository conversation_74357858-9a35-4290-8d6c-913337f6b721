{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genVirtualStyle = token => {\n  const {\n    componentCls,\n    motionDurationMid,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = \"\".concat(unit(lineWidth), \" \").concat(lineType, \" \").concat(tableBorderColor);\n  const rowCellCls = \"\".concat(componentCls, \"-expanded-row-cell\");\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      // ========================== Row ==========================\n      [\"\".concat(componentCls, \"-tbody-virtual\")]: {\n        [\"\".concat(componentCls, \"-tbody-virtual-holder-inner\")]: {\n          [\"\\n            & > \".concat(componentCls, \"-row, \\n            & > div:not(\").concat(componentCls, \"-row) > \").concat(componentCls, \"-row\\n          \")]: {\n            display: 'flex',\n            boxSizing: 'border-box',\n            width: '100%'\n          }\n        },\n        [\"\".concat(componentCls, \"-cell\")]: {\n          borderBottom: tableBorder,\n          transition: \"background \".concat(motionDurationMid)\n        },\n        [\"\".concat(componentCls, \"-expanded-row\")]: {\n          [\"\".concat(rowCellCls).concat(rowCellCls, \"-fixed\")]: {\n            position: 'sticky',\n            insetInlineStart: 0,\n            overflow: 'hidden',\n            width: \"calc(var(--virtual-width) - \".concat(unit(lineWidth), \")\"),\n            borderInlineEnd: 'none'\n          }\n        }\n      },\n      // ======================== Border =========================\n      [\"\".concat(componentCls, \"-bordered\")]: {\n        [\"\".concat(componentCls, \"-tbody-virtual\")]: {\n          '&:after': {\n            content: '\"\"',\n            insetInline: 0,\n            bottom: 0,\n            borderBottom: tableBorder,\n            position: 'absolute'\n          },\n          [\"\".concat(componentCls, \"-cell\")]: {\n            borderInlineEnd: tableBorder,\n            [\"&\".concat(componentCls, \"-cell-fix-right-first:before\")]: {\n              content: '\"\"',\n              position: 'absolute',\n              insetBlock: 0,\n              insetInlineStart: calc(lineWidth).mul(-1).equal(),\n              borderInlineStart: tableBorder\n            }\n          }\n        },\n        // Empty placeholder\n        [\"&\".concat(componentCls, \"-virtual\")]: {\n          [\"\".concat(componentCls, \"-placeholder \").concat(componentCls, \"-cell\")]: {\n            borderInlineEnd: tableBorder,\n            borderBottom: tableBorder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genVirtualStyle;", "map": {"version": 3, "names": ["unit", "genVirtualStyle", "token", "componentCls", "motionDurationMid", "lineWidth", "lineType", "tableBorderColor", "calc", "tableBorder", "concat", "rowCellCls", "display", "boxSizing", "width", "borderBottom", "transition", "position", "insetInlineStart", "overflow", "borderInlineEnd", "content", "insetInline", "bottom", "insetBlock", "mul", "equal", "borderInlineStart"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/virtual.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genVirtualStyle = token => {\n  const {\n    componentCls,\n    motionDurationMid,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const rowCellCls = `${componentCls}-expanded-row-cell`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Row ==========================\n      [`${componentCls}-tbody-virtual`]: {\n        [`${componentCls}-tbody-virtual-holder-inner`]: {\n          [`\n            & > ${componentCls}-row, \n            & > div:not(${componentCls}-row) > ${componentCls}-row\n          `]: {\n            display: 'flex',\n            boxSizing: 'border-box',\n            width: '100%'\n          }\n        },\n        [`${componentCls}-cell`]: {\n          borderBottom: tableBorder,\n          transition: `background ${motionDurationMid}`\n        },\n        [`${componentCls}-expanded-row`]: {\n          [`${rowCellCls}${rowCellCls}-fixed`]: {\n            position: 'sticky',\n            insetInlineStart: 0,\n            overflow: 'hidden',\n            width: `calc(var(--virtual-width) - ${unit(lineWidth)})`,\n            borderInlineEnd: 'none'\n          }\n        }\n      },\n      // ======================== Border =========================\n      [`${componentCls}-bordered`]: {\n        [`${componentCls}-tbody-virtual`]: {\n          '&:after': {\n            content: '\"\"',\n            insetInline: 0,\n            bottom: 0,\n            borderBottom: tableBorder,\n            position: 'absolute'\n          },\n          [`${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            [`&${componentCls}-cell-fix-right-first:before`]: {\n              content: '\"\"',\n              position: 'absolute',\n              insetBlock: 0,\n              insetInlineStart: calc(lineWidth).mul(-1).equal(),\n              borderInlineStart: tableBorder\n            }\n          }\n        },\n        // Empty placeholder\n        [`&${componentCls}-virtual`]: {\n          [`${componentCls}-placeholder ${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            borderBottom: tableBorder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genVirtualStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,iBAAiB;IACjBC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC;EACF,CAAC,GAAGN,KAAK;EACT,MAAMO,WAAW,MAAAC,MAAA,CAAMV,IAAI,CAACK,SAAS,CAAC,OAAAK,MAAA,CAAIJ,QAAQ,OAAAI,MAAA,CAAIH,gBAAgB,CAAE;EACxE,MAAMI,UAAU,MAAAD,MAAA,CAAMP,YAAY,uBAAoB;EACtD,OAAO;IACL,IAAAO,MAAA,CAAIP,YAAY,gBAAa;MAC3B;MACA,IAAAO,MAAA,CAAIP,YAAY,sBAAmB;QACjC,IAAAO,MAAA,CAAIP,YAAY,mCAAgC;UAC9C,sBAAAO,MAAA,CACQP,YAAY,sCAAAO,MAAA,CACJP,YAAY,cAAAO,MAAA,CAAWP,YAAY,wBAC/C;YACFS,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE,YAAY;YACvBC,KAAK,EAAE;UACT;QACF,CAAC;QACD,IAAAJ,MAAA,CAAIP,YAAY,aAAU;UACxBY,YAAY,EAAEN,WAAW;UACzBO,UAAU,gBAAAN,MAAA,CAAgBN,iBAAiB;QAC7C,CAAC;QACD,IAAAM,MAAA,CAAIP,YAAY,qBAAkB;UAChC,IAAAO,MAAA,CAAIC,UAAU,EAAAD,MAAA,CAAGC,UAAU,cAAW;YACpCM,QAAQ,EAAE,QAAQ;YAClBC,gBAAgB,EAAE,CAAC;YACnBC,QAAQ,EAAE,QAAQ;YAClBL,KAAK,iCAAAJ,MAAA,CAAiCV,IAAI,CAACK,SAAS,CAAC,MAAG;YACxDe,eAAe,EAAE;UACnB;QACF;MACF,CAAC;MACD;MACA,IAAAV,MAAA,CAAIP,YAAY,iBAAc;QAC5B,IAAAO,MAAA,CAAIP,YAAY,sBAAmB;UACjC,SAAS,EAAE;YACTkB,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE,CAAC;YACdC,MAAM,EAAE,CAAC;YACTR,YAAY,EAAEN,WAAW;YACzBQ,QAAQ,EAAE;UACZ,CAAC;UACD,IAAAP,MAAA,CAAIP,YAAY,aAAU;YACxBiB,eAAe,EAAEX,WAAW;YAC5B,KAAAC,MAAA,CAAKP,YAAY,oCAAiC;cAChDkB,OAAO,EAAE,IAAI;cACbJ,QAAQ,EAAE,UAAU;cACpBO,UAAU,EAAE,CAAC;cACbN,gBAAgB,EAAEV,IAAI,CAACH,SAAS,CAAC,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;cACjDC,iBAAiB,EAAElB;YACrB;UACF;QACF,CAAC;QACD;QACA,KAAAC,MAAA,CAAKP,YAAY,gBAAa;UAC5B,IAAAO,MAAA,CAAIP,YAAY,mBAAAO,MAAA,CAAgBP,YAAY,aAAU;YACpDiB,eAAe,EAAEX,WAAW;YAC5BM,YAAY,EAAEN;UAChB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}