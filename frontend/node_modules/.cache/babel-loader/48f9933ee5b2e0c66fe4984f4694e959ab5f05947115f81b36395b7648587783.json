{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Modal, Form, Select, DatePicker, message, Popconfirm, Card, Tag, Input, Tooltip, Alert, Row, Col, Statistic, Typography } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, CheckOutlined, CloseOutlined, ReadOutlined, ClockCircleOutlined, ReloadOutlined, ExportOutlined, UserOutlined, BookOutlined, WarningOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { borrowService } from '../services/borrowService';\nimport { userService } from '../services/userService';\nimport { bookService } from '../services/bookService';\nimport { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';\nimport ActionFeedback from '../components/Common/ActionFeedback';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Search\n} = Input;\nconst {\n  Title,\n  Text\n} = Typography;\nconst BorrowManagement = () => {\n  _s();\n  const [borrows, setBorrows] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [books, setBooks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingBorrow, setEditingBorrow] = useState(null);\n  const [statistics, setStatistics] = useState({\n    total: 0,\n    pending: 0,\n    borrowed: 0,\n    returned: 0,\n    overdue: 0,\n    rejected: 0\n  });\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [searchParams, setSearchParams] = useState({\n    userName: '',\n    bookName: '',\n    status: undefined\n  });\n  const [form] = Form.useForm();\n  const {\n    hasPermission\n  } = usePermission();\n  useEffect(() => {\n    fetchBorrows();\n    fetchUsers();\n    fetchBooks();\n  }, [pagination.current, pagination.pageSize]);\n  const fetchBorrows = async () => {\n    setLoading(true);\n    try {\n      const params = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams\n      };\n      const response = await borrowService.getBorrows(params);\n      const records = (response === null || response === void 0 ? void 0 : response.records) || [];\n      setBorrows(records);\n      setPagination(prev => ({\n        ...prev,\n        total: (response === null || response === void 0 ? void 0 : response.total) || 0\n      }));\n\n      // 计算统计信息\n      const stats = {\n        total: (response === null || response === void 0 ? void 0 : response.total) || 0,\n        pending: records.filter(item => item.status === '待审核').length,\n        borrowed: records.filter(item => item.status === '已借出').length,\n        returned: records.filter(item => item.status === '已归还').length,\n        overdue: records.filter(item => item.status === '已借出' && isOverdue(item.expectedReturnTime, item.status)).length,\n        rejected: records.filter(item => item.status === '已拒绝').length\n      };\n      setStatistics(stats);\n    } catch (error) {\n      console.error('获取借阅记录失败:', error);\n      ActionFeedback.error('获取借阅记录失败');\n      setBorrows([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchUsers = async () => {\n    try {\n      const response = await userService.getUsers({\n        current: 1,\n        size: 100\n      });\n      setUsers(response.records);\n    } catch (error) {\n      message.error('获取用户列表失败');\n    }\n  };\n  const fetchBooks = async () => {\n    try {\n      const response = await bookService.getBooks({\n        current: 1,\n        size: 100\n      });\n      setBooks(response.records.filter(book => book.status === '可借阅'));\n    } catch (error) {\n      message.error('获取图书列表失败');\n    }\n  };\n  const handleAdd = () => {\n    setEditingBorrow(null);\n    setModalVisible(true);\n    form.resetFields();\n    // 设置默认的预期归还时间（30天后）\n    form.setFieldsValue({\n      expectedReturnTime: dayjs().add(30, 'day')\n    });\n  };\n  const handleEdit = record => {\n    setEditingBorrow(record);\n    setModalVisible(true);\n    form.setFieldsValue({\n      ...record,\n      borrowTime: record.borrowTime ? dayjs(record.borrowTime) : null,\n      expectedReturnTime: record.expectedReturnTime ? dayjs(record.expectedReturnTime) : null\n    });\n  };\n  const handleDelete = async id => {\n    try {\n      await borrowService.deleteBorrow(id);\n      message.success('删除成功');\n      fetchBorrows();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleApprove = async id => {\n    try {\n      await borrowService.approveBorrow(id);\n      message.success('审核通过');\n      fetchBorrows();\n    } catch (error) {\n      message.error('审核失败');\n    }\n  };\n  const handleReject = async id => {\n    try {\n      await borrowService.rejectBorrow(id);\n      message.success('审核拒绝');\n      fetchBorrows();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$borrowTime, _values$expectedRetur;\n      const submitData = {\n        ...values,\n        borrowTime: (_values$borrowTime = values.borrowTime) === null || _values$borrowTime === void 0 ? void 0 : _values$borrowTime.format('YYYY-MM-DD HH:mm:ss'),\n        expectedReturnTime: (_values$expectedRetur = values.expectedReturnTime) === null || _values$expectedRetur === void 0 ? void 0 : _values$expectedRetur.format('YYYY-MM-DD HH:mm:ss'),\n        status: editingBorrow ? editingBorrow.status : '待审核'\n      };\n      if (editingBorrow) {\n        await borrowService.updateBorrow({\n          ...editingBorrow,\n          ...submitData\n        });\n        message.success('更新成功');\n      } else {\n        await borrowService.createBorrow(submitData);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchBorrows();\n    } catch (error) {\n      message.error(editingBorrow ? '更新失败' : '创建失败');\n    }\n  };\n  const handleSearch = () => {\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    fetchBorrows();\n  };\n  const handleReset = () => {\n    setSearchParams({\n      userName: '',\n      bookName: '',\n      status: undefined\n    });\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    setTimeout(fetchBorrows, 100);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case '待审核':\n        return 'orange';\n      case '已借出':\n        return 'blue';\n      case '已归还':\n        return 'green';\n      case '已拒绝':\n        return 'red';\n      case '逾期':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const isOverdue = (expectedReturnTime, status) => {\n    if (status === '已归还') return false;\n    return dayjs().isAfter(dayjs(expectedReturnTime));\n  };\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '用户姓名',\n    dataIndex: 'userName',\n    key: 'userName',\n    width: 120\n  }, {\n    title: '图书名称',\n    dataIndex: 'bookName',\n    key: 'bookName',\n    ellipsis: true\n  }, {\n    title: '借阅时间',\n    dataIndex: 'borrowTime',\n    key: 'borrowTime',\n    width: 150,\n    render: time => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'\n  }, {\n    title: '预期归还时间',\n    dataIndex: 'expectedReturnTime',\n    key: 'expectedReturnTime',\n    width: 150,\n    render: (time, record) => {\n      const isLate = isOverdue(time, record.status);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: isLate ? '#ff4d4f' : undefined\n        },\n        children: [dayjs(time).format('YYYY-MM-DD'), isLate && /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"red\",\n          style: {\n            marginLeft: 8\n          },\n          children: \"\\u903E\\u671F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => {\n      const displayStatus = isOverdue(record.expectedReturnTime, status) && status === '已借出' ? '逾期' : status;\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: getStatusColor(displayStatus),\n        children: displayStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 250,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [record.status === '待审核' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5BA1\\u6838\\u901A\\u8FC7\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 25\n            }, this),\n            onClick: () => handleApprove(record.id),\n            style: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5BA1\\u6838\\u62D2\\u7EDD\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 25\n            }, this),\n            onClick: () => handleReject(record.id),\n            style: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u6761\\u501F\\u9605\\u8BB0\\u5F55\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '0 4px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u501F\\u9605\\u6570\",\n            value: statistics.total,\n            prefix: /*#__PURE__*/_jsxDEV(ReadOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5BA1\\u6838\",\n            value: statistics.pending,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u501F\\u51FA\",\n            value: statistics.borrowed,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5F52\\u8FD8\",\n            value: statistics.returned,\n            prefix: /*#__PURE__*/_jsxDEV(CheckOutlined, {\n              style: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u903E\\u671F\\u672A\\u8FD8\",\n            value: statistics.overdue,\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {\n              style: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u62D2\\u7EDD\",\n            value: statistics.rejected,\n            prefix: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n              style: {\n                color: '#8c8c8c'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#8c8c8c'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(ReadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          style: {\n            margin: 0\n          },\n          children: \"\\u501F\\u9605\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5237\\u65B0\\u6570\\u636E\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 23\n            }, this),\n            onClick: fetchBorrows,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5BFC\\u51FA\\u6570\\u636E\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u501F\\u9605\\u7BA1\\u7406\\u8BF4\\u660E\",\n        description: \"\\u7BA1\\u7406\\u56FE\\u4E66\\u501F\\u9605\\u7533\\u8BF7\\uFF0C\\u5305\\u62EC\\u5BA1\\u6838\\u3001\\u7F16\\u8F91\\u548C\\u5220\\u9664\\u501F\\u9605\\u8BB0\\u5F55\\u3002\\u903E\\u671F\\u672A\\u8FD8\\u7684\\u56FE\\u4E66\\u4F1A\\u81EA\\u52A8\\u6807\\u8BB0\\u4E3A\\u903E\\u671F\\u72B6\\u6001\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        style: {\n          marginBottom: 16,\n          background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',\n          border: '1px solid #b7eb8f'\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u641C\\u7D22\\u7528\\u6237\\u59D3\\u540D\",\n              value: searchParams.userName,\n              onChange: e => setSearchParams(prev => ({\n                ...prev,\n                userName: e.target.value\n              })),\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 25\n              }, this),\n              allowClear: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u641C\\u7D22\\u56FE\\u4E66\\u540D\\u79F0\",\n              value: searchParams.bookName,\n              onChange: e => setSearchParams(prev => ({\n                ...prev,\n                bookName: e.target.value\n              })),\n              prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 25\n              }, this),\n              allowClear: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n              value: searchParams.status,\n              onChange: value => setSearchParams(prev => ({\n                ...prev,\n                status: value\n              })),\n              style: {\n                width: '100%'\n              },\n              allowClear: true,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u5F85\\u5BA1\\u6838\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this), \"\\u5F85\\u5BA1\\u6838\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u5DF2\\u501F\\u51FA\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this), \"\\u5DF2\\u501F\\u51FA\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u5DF2\\u5F52\\u8FD8\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), \"\\u5DF2\\u5F52\\u8FD8\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u5DF2\\u62D2\\u7EDD\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this), \"\\u5DF2\\u62D2\\u7EDD\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 25\n                }, this),\n                onClick: handleSearch,\n                children: \"\\u641C\\u7D22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleReset,\n                children: \"\\u91CD\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: hasPermission(PERMISSIONS.BORROW_APPROVE) && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 25\n              }, this),\n              onClick: handleAdd,\n              size: \"large\",\n              children: \"\\u65B0\\u589E\\u501F\\u9605\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 16,\n          style: {\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u5171\\u627E\\u5230 \", pagination.total, \" \\u6761\\u501F\\u9605\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: borrows,\n        loading: loading,\n        pagination: {\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10\n            }));\n          }\n        },\n        rowKey: \"id\",\n        scroll: {\n          x: 1200\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: editingBorrow ? '编辑借阅记录' : '新增借阅记录',\n        open: modalVisible,\n        onCancel: () => setModalVisible(false),\n        onOk: () => form.submit(),\n        width: 600,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          onFinish: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"userId\",\n            label: \"\\u501F\\u9605\\u7528\\u6237\",\n            rules: [{\n              required: true,\n              message: '请选择借阅用户'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u501F\\u9605\\u7528\\u6237\",\n              showSearch: true,\n              optionFilterProp: \"children\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(Option, {\n                value: user.id,\n                children: [user.name, \" (\", user.username, \")\"]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"bookId\",\n            label: \"\\u501F\\u9605\\u56FE\\u4E66\",\n            rules: [{\n              required: true,\n              message: '请选择借阅图书'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u501F\\u9605\\u56FE\\u4E66\",\n              showSearch: true,\n              optionFilterProp: \"children\",\n              children: books.map(book => /*#__PURE__*/_jsxDEV(Option, {\n                value: book.id,\n                children: [book.name, \" - \", book.author]\n              }, book.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"borrowTime\",\n            label: \"\\u501F\\u9605\\u65F6\\u95F4\",\n            rules: [{\n              required: true,\n              message: '请选择借阅时间'\n            }],\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              showTime: true,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u501F\\u9605\\u65F6\\u95F4\",\n              style: {\n                width: '100%'\n              },\n              format: \"YYYY-MM-DD HH:mm:ss\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"expectedReturnTime\",\n            label: \"\\u9884\\u671F\\u5F52\\u8FD8\\u65F6\\u95F4\",\n            rules: [{\n              required: true,\n              message: '请选择预期归还时间'\n            }],\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u9884\\u671F\\u5F52\\u8FD8\\u65F6\\u95F4\",\n              style: {\n                width: '100%'\n              },\n              format: \"YYYY-MM-DD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 362,\n    columnNumber: 5\n  }, this);\n};\n_s(BorrowManagement, \"dCAtkXMJTS+UpZxcB2xm94BeFEI=\", false, function () {\n  return [Form.useForm, usePermission];\n});\n_c = BorrowManagement;\nexport default BorrowManagement;\nvar _c;\n$RefreshReg$(_c, \"BorrowManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Modal", "Form", "Select", "DatePicker", "message", "Popconfirm", "Card", "Tag", "Input", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Typography", "PlusOutlined", "EditOutlined", "DeleteOutlined", "SearchOutlined", "CheckOutlined", "CloseOutlined", "ReadOutlined", "ClockCircleOutlined", "ReloadOutlined", "ExportOutlined", "UserOutlined", "BookOutlined", "WarningOutlined", "dayjs", "borrowService", "userService", "bookService", "PERMISSIONS", "usePermission", "ActionFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Search", "Title", "Text", "BorrowManagement", "_s", "borrows", "setBorrows", "users", "setUsers", "books", "setBooks", "loading", "setLoading", "modalVisible", "setModalVisible", "editing<PERSON><PERSON>row", "setEditingBorrow", "statistics", "setStatistics", "total", "pending", "borrowed", "returned", "overdue", "rejected", "pagination", "setPagination", "current", "pageSize", "searchParams", "setSearchParams", "userName", "bookName", "status", "undefined", "form", "useForm", "hasPermission", "fetchBorrows", "fetchUsers", "fetchBooks", "params", "size", "response", "getBorrows", "records", "prev", "stats", "filter", "item", "length", "isOverdue", "expectedReturnTime", "error", "console", "getUsers", "getBooks", "book", "handleAdd", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "handleEdit", "record", "borrowTime", "handleDelete", "id", "deleteBorrow", "success", "handleApprove", "approve<PERSON><PERSON>row", "handleReject", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "values", "_values$borrowTime", "_values$expectedRetur", "submitData", "format", "updateBorrow", "createBorrow", "handleSearch", "handleReset", "setTimeout", "getStatusColor", "isAfter", "columns", "title", "dataIndex", "key", "width", "ellipsis", "render", "time", "isLate", "style", "color", "children", "marginLeft", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "displayStatus", "_", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "padding", "gutter", "marginBottom", "xs", "sm", "md", "lg", "value", "prefix", "valueStyle", "level", "margin", "extra", "description", "showIcon", "background", "border", "placeholder", "onChange", "e", "target", "allowClear", "BORROW_APPROVE", "textAlign", "dataSource", "showSizeChanger", "showQuickJumper", "showTotal", "page", "<PERSON><PERSON><PERSON>", "scroll", "x", "open", "onCancel", "onOk", "submit", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "rules", "required", "showSearch", "optionFilterProp", "map", "user", "username", "author", "showTime", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Select,\n  DatePicker,\n  message,\n  Popconfirm,\n  Card,\n  Tag,\n  Input,\n  Tooltip,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  Badge,\n  Avatar,\n  Progress,\n  Divider,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  CheckOutlined,\n  CloseOutlined,\n  ReadOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  UserOutlined,\n  BookOutlined,\n  CalendarOutlined,\n  WarningOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { BookBorrow, User, Book, PageParams } from '../types';\nimport { borrowService } from '../services/borrowService';\nimport { userService } from '../services/userService';\nimport { bookService } from '../services/bookService';\nimport PermissionWrapper, { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';\nimport ActionFeedback from '../components/Common/ActionFeedback';\n\nconst { Option } = Select;\nconst { Search } = Input;\nconst { Title, Text } = Typography;\n\nconst BorrowManagement: React.FC = () => {\n  const [borrows, setBorrows] = useState<BookBorrow[]>([]);\n  const [users, setUsers] = useState<User[]>([]);\n  const [books, setBooks] = useState<Book[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingBorrow, setEditingBorrow] = useState<BookBorrow | null>(null);\n  const [statistics, setStatistics] = useState({\n    total: 0,\n    pending: 0,\n    borrowed: 0,\n    returned: 0,\n    overdue: 0,\n    rejected: 0,\n  });\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [searchParams, setSearchParams] = useState({\n    userName: '',\n    bookName: '',\n    status: undefined as string | undefined,\n  });\n  const [form] = Form.useForm();\n  const { hasPermission } = usePermission();\n\n  useEffect(() => {\n    fetchBorrows();\n    fetchUsers();\n    fetchBooks();\n  }, [pagination.current, pagination.pageSize]);\n\n  const fetchBorrows = async () => {\n    setLoading(true);\n    try {\n      const params: PageParams = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams,\n      };\n      const response = await borrowService.getBorrows(params);\n      const records = response?.records || [];\n      setBorrows(records);\n      setPagination(prev => ({\n        ...prev,\n        total: response?.total || 0,\n      }));\n\n      // 计算统计信息\n      const stats = {\n        total: response?.total || 0,\n        pending: records.filter((item: BookBorrow) => item.status === '待审核').length,\n        borrowed: records.filter((item: BookBorrow) => item.status === '已借出').length,\n        returned: records.filter((item: BookBorrow) => item.status === '已归还').length,\n        overdue: records.filter((item: BookBorrow) =>\n          item.status === '已借出' && isOverdue(item.expectedReturnTime, item.status)\n        ).length,\n        rejected: records.filter((item: BookBorrow) => item.status === '已拒绝').length,\n      };\n      setStatistics(stats);\n    } catch (error) {\n      console.error('获取借阅记录失败:', error);\n      ActionFeedback.error('获取借阅记录失败');\n      setBorrows([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const response = await userService.getUsers({ current: 1, size: 100 });\n      setUsers(response.records);\n    } catch (error) {\n      message.error('获取用户列表失败');\n    }\n  };\n\n  const fetchBooks = async () => {\n    try {\n      const response = await bookService.getBooks({ current: 1, size: 100 });\n      setBooks(response.records.filter(book => book.status === '可借阅'));\n    } catch (error) {\n      message.error('获取图书列表失败');\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingBorrow(null);\n    setModalVisible(true);\n    form.resetFields();\n    // 设置默认的预期归还时间（30天后）\n    form.setFieldsValue({\n      expectedReturnTime: dayjs().add(30, 'day'),\n    });\n  };\n\n  const handleEdit = (record: BookBorrow) => {\n    setEditingBorrow(record);\n    setModalVisible(true);\n    form.setFieldsValue({\n      ...record,\n      borrowTime: record.borrowTime ? dayjs(record.borrowTime) : null,\n      expectedReturnTime: record.expectedReturnTime ? dayjs(record.expectedReturnTime) : null,\n    });\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await borrowService.deleteBorrow(id);\n      message.success('删除成功');\n      fetchBorrows();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleApprove = async (id: number) => {\n    try {\n      await borrowService.approveBorrow(id);\n      message.success('审核通过');\n      fetchBorrows();\n    } catch (error) {\n      message.error('审核失败');\n    }\n  };\n\n  const handleReject = async (id: number) => {\n    try {\n      await borrowService.rejectBorrow(id);\n      message.success('审核拒绝');\n      fetchBorrows();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const submitData = {\n        ...values,\n        borrowTime: values.borrowTime?.format('YYYY-MM-DD HH:mm:ss'),\n        expectedReturnTime: values.expectedReturnTime?.format('YYYY-MM-DD HH:mm:ss'),\n        status: editingBorrow ? editingBorrow.status : '待审核',\n      };\n\n      if (editingBorrow) {\n        await borrowService.updateBorrow({ ...editingBorrow, ...submitData });\n        message.success('更新成功');\n      } else {\n        await borrowService.createBorrow(submitData);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchBorrows();\n    } catch (error) {\n      message.error(editingBorrow ? '更新失败' : '创建失败');\n    }\n  };\n\n  const handleSearch = () => {\n    setPagination(prev => ({ ...prev, current: 1 }));\n    fetchBorrows();\n  };\n\n  const handleReset = () => {\n    setSearchParams({\n      userName: '',\n      bookName: '',\n      status: undefined,\n    });\n    setPagination(prev => ({ ...prev, current: 1 }));\n    setTimeout(fetchBorrows, 100);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case '待审核':\n        return 'orange';\n      case '已借出':\n        return 'blue';\n      case '已归还':\n        return 'green';\n      case '已拒绝':\n        return 'red';\n      case '逾期':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const isOverdue = (expectedReturnTime: string, status: string) => {\n    if (status === '已归还') return false;\n    return dayjs().isAfter(dayjs(expectedReturnTime));\n  };\n\n  const columns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户姓名',\n      dataIndex: 'userName',\n      key: 'userName',\n      width: 120,\n    },\n    {\n      title: '图书名称',\n      dataIndex: 'bookName',\n      key: 'bookName',\n      ellipsis: true,\n    },\n    {\n      title: '借阅时间',\n      dataIndex: 'borrowTime',\n      key: 'borrowTime',\n      width: 150,\n      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',\n    },\n    {\n      title: '预期归还时间',\n      dataIndex: 'expectedReturnTime',\n      key: 'expectedReturnTime',\n      width: 150,\n      render: (time: string, record: BookBorrow) => {\n        const isLate = isOverdue(time, record.status);\n        return (\n          <span style={{ color: isLate ? '#ff4d4f' : undefined }}>\n            {dayjs(time).format('YYYY-MM-DD')}\n            {isLate && <Tag color=\"red\" style={{ marginLeft: 8 }}>逾期</Tag>}\n          </span>\n        );\n      },\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string, record: BookBorrow) => {\n        const displayStatus = isOverdue(record.expectedReturnTime, status) && status === '已借出' ? '逾期' : status;\n        return <Tag color={getStatusColor(displayStatus)}>{displayStatus}</Tag>;\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 250,\n      render: (_: any, record: BookBorrow) => (\n        <Space size=\"small\">\n          {record.status === '待审核' && (\n            <>\n              <Tooltip title=\"审核通过\">\n                <Button\n                  type=\"link\"\n                  size=\"small\"\n                  icon={<CheckOutlined />}\n                  onClick={() => handleApprove(record.id)}\n                  style={{ color: '#52c41a' }}\n                />\n              </Tooltip>\n              <Tooltip title=\"审核拒绝\">\n                <Button\n                  type=\"link\"\n                  size=\"small\"\n                  icon={<CloseOutlined />}\n                  onClick={() => handleReject(record.id)}\n                  style={{ color: '#ff4d4f' }}\n                />\n              </Tooltip>\n            </>\n          )}\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这条借阅记录吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"link\"\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '0 4px' }}>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"总借阅数\"\n              value={statistics.total}\n              prefix={<ReadOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"待审核\"\n              value={statistics.pending}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"已借出\"\n              value={statistics.borrowed}\n              prefix={<BookOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"已归还\"\n              value={statistics.returned}\n              prefix={<CheckOutlined style={{ color: '#722ed1' }} />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"逾期未还\"\n              value={statistics.overdue}\n              prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={4}>\n          <Card>\n            <Statistic\n              title=\"已拒绝\"\n              value={statistics.rejected}\n              prefix={<CloseOutlined style={{ color: '#8c8c8c' }} />}\n              valueStyle={{ color: '#8c8c8c' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容卡片 */}\n      <Card\n        title={\n          <Space>\n            <ReadOutlined />\n            <Title level={4} style={{ margin: 0 }}>借阅管理</Title>\n          </Space>\n        }\n        extra={\n          <Space>\n            <Tooltip title=\"刷新数据\">\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={fetchBorrows}\n                loading={loading}\n              />\n            </Tooltip>\n            <Tooltip title=\"导出数据\">\n              <Button icon={<ExportOutlined />} />\n            </Tooltip>\n          </Space>\n        }\n      >\n        {/* 提示信息 */}\n        <Alert\n          message=\"借阅管理说明\"\n          description=\"管理图书借阅申请，包括审核、编辑和删除借阅记录。逾期未还的图书会自动标记为逾期状态。\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n\n        {/* 搜索区域 */}\n        <Card\n          size=\"small\"\n          style={{\n            marginBottom: 16,\n            background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',\n            border: '1px solid #b7eb8f'\n          }}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24} sm={12} md={6}>\n              <Input\n                placeholder=\"搜索用户姓名\"\n                value={searchParams.userName}\n                onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}\n                prefix={<UserOutlined />}\n                allowClear\n              />\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Input\n                placeholder=\"搜索图书名称\"\n                value={searchParams.bookName}\n                onChange={(e) => setSearchParams(prev => ({ ...prev, bookName: e.target.value }))}\n                prefix={<BookOutlined />}\n                allowClear\n              />\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Select\n                placeholder=\"选择状态\"\n                value={searchParams.status}\n                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}\n                style={{ width: '100%' }}\n                allowClear\n              >\n                <Option value=\"待审核\">\n                  <Space>\n                    <ClockCircleOutlined />\n                    待审核\n                  </Space>\n                </Option>\n                <Option value=\"已借出\">\n                  <Space>\n                    <BookOutlined />\n                    已借出\n                  </Space>\n                </Option>\n                <Option value=\"已归还\">\n                  <Space>\n                    <CheckOutlined />\n                    已归还\n                  </Space>\n                </Option>\n                <Option value=\"已拒绝\">\n                  <Space>\n                    <CloseOutlined />\n                    已拒绝\n                  </Space>\n                </Option>\n              </Select>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Space style={{ width: '100%' }}>\n                <Button\n                  type=\"primary\"\n                  icon={<SearchOutlined />}\n                  onClick={handleSearch}\n                >\n                  搜索\n                </Button>\n                <Button onClick={handleReset}>重置</Button>\n              </Space>\n            </Col>\n          </Row>\n        </Card>\n\n        {/* 操作按钮 */}\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={12} md={8}>\n            <Space>\n              {hasPermission(PERMISSIONS.BORROW_APPROVE) && (\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={handleAdd}\n                  size=\"large\"\n                >\n                  新增借阅\n                </Button>\n              )}\n            </Space>\n          </Col>\n          <Col xs={24} sm={12} md={16} style={{ textAlign: 'right' }}>\n            <Text type=\"secondary\">\n              共找到 {pagination.total} 条借阅记录\n            </Text>\n          </Col>\n        </Row>\n\n      {/* 表格 */}\n      <Table\n        columns={columns}\n        dataSource={borrows}\n        loading={loading}\n        pagination={{\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10,\n            }));\n          },\n        }}\n        rowKey=\"id\"\n        scroll={{ x: 1200 }}\n      />\n\n      {/* 编辑/新增模态框 */}\n      <Modal\n        title={editingBorrow ? '编辑借阅记录' : '新增借阅记录'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"userId\"\n            label=\"借阅用户\"\n            rules={[{ required: true, message: '请选择借阅用户' }]}\n          >\n            <Select placeholder=\"请选择借阅用户\" showSearch optionFilterProp=\"children\">\n              {users.map(user => (\n                <Option key={user.id} value={user.id}>\n                  {user.name} ({user.username})\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"bookId\"\n            label=\"借阅图书\"\n            rules={[{ required: true, message: '请选择借阅图书' }]}\n          >\n            <Select placeholder=\"请选择借阅图书\" showSearch optionFilterProp=\"children\">\n              {books.map(book => (\n                <Option key={book.id} value={book.id}>\n                  {book.name} - {book.author}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"borrowTime\"\n            label=\"借阅时间\"\n            rules={[{ required: true, message: '请选择借阅时间' }]}\n          >\n            <DatePicker\n              showTime\n              placeholder=\"请选择借阅时间\"\n              style={{ width: '100%' }}\n              format=\"YYYY-MM-DD HH:mm:ss\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"expectedReturnTime\"\n            label=\"预期归还时间\"\n            rules={[{ required: true, message: '请选择预期归还时间' }]}\n          >\n            <DatePicker\n              placeholder=\"请选择预期归还时间\"\n              style={{ width: '100%' }}\n              format=\"YYYY-MM-DD\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n      </Card>\n    </div>\n  );\n};\n\nexport default BorrowManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,UAAU,QAKL,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EAEnBC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,YAAY,EAEZC,eAAe,QACV,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAA4BC,WAAW,EAAEC,aAAa,QAAQ,sCAAsC;AACpG,OAAOC,cAAc,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjE,MAAM;EAAEC;AAAO,CAAC,GAAGrC,MAAM;AACzB,MAAM;EAAEsC;AAAO,CAAC,GAAGhC,KAAK;AACxB,MAAM;EAAEiC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAElC,MAAM6B,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAe,EAAE,CAAC;EACxD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAoB,IAAI,CAAC;EAC3E,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC;IAC3CgE,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC;IAC3CwE,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZT,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC;IAC/C4E,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAEC;EACV,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAG1E,IAAI,CAAC2E,OAAO,CAAC,CAAC;EAC7B,MAAM;IAAEC;EAAc,CAAC,GAAG5C,aAAa,CAAC,CAAC;EAEzCrC,SAAS,CAAC,MAAM;IACdkF,YAAY,CAAC,CAAC;IACdC,UAAU,CAAC,CAAC;IACZC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACf,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC;EAE7C,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B1B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM6B,MAAkB,GAAG;QACzBd,OAAO,EAAEF,UAAU,CAACE,OAAO;QAC3Be,IAAI,EAAEjB,UAAU,CAACG,QAAQ;QACzB,GAAGC;MACL,CAAC;MACD,MAAMc,QAAQ,GAAG,MAAMtD,aAAa,CAACuD,UAAU,CAACH,MAAM,CAAC;MACvD,MAAMI,OAAO,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,OAAO,KAAI,EAAE;MACvCvC,UAAU,CAACuC,OAAO,CAAC;MACnBnB,aAAa,CAACoB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP3B,KAAK,EAAE,CAAAwB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAExB,KAAK,KAAI;MAC5B,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM4B,KAAK,GAAG;QACZ5B,KAAK,EAAE,CAAAwB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAExB,KAAK,KAAI,CAAC;QAC3BC,OAAO,EAAEyB,OAAO,CAACG,MAAM,CAAEC,IAAgB,IAAKA,IAAI,CAAChB,MAAM,KAAK,KAAK,CAAC,CAACiB,MAAM;QAC3E7B,QAAQ,EAAEwB,OAAO,CAACG,MAAM,CAAEC,IAAgB,IAAKA,IAAI,CAAChB,MAAM,KAAK,KAAK,CAAC,CAACiB,MAAM;QAC5E5B,QAAQ,EAAEuB,OAAO,CAACG,MAAM,CAAEC,IAAgB,IAAKA,IAAI,CAAChB,MAAM,KAAK,KAAK,CAAC,CAACiB,MAAM;QAC5E3B,OAAO,EAAEsB,OAAO,CAACG,MAAM,CAAEC,IAAgB,IACvCA,IAAI,CAAChB,MAAM,KAAK,KAAK,IAAIkB,SAAS,CAACF,IAAI,CAACG,kBAAkB,EAAEH,IAAI,CAAChB,MAAM,CACzE,CAAC,CAACiB,MAAM;QACR1B,QAAQ,EAAEqB,OAAO,CAACG,MAAM,CAAEC,IAAgB,IAAKA,IAAI,CAAChB,MAAM,KAAK,KAAK,CAAC,CAACiB;MACxE,CAAC;MACDhC,aAAa,CAAC6B,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3D,cAAc,CAAC2D,KAAK,CAAC,UAAU,CAAC;MAChC/C,UAAU,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMrD,WAAW,CAACiE,QAAQ,CAAC;QAAE5B,OAAO,EAAE,CAAC;QAAEe,IAAI,EAAE;MAAI,CAAC,CAAC;MACtElC,QAAQ,CAACmC,QAAQ,CAACE,OAAO,CAAC;IAC5B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMb,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMpD,WAAW,CAACiE,QAAQ,CAAC;QAAE7B,OAAO,EAAE,CAAC;QAAEe,IAAI,EAAE;MAAI,CAAC,CAAC;MACtEhC,QAAQ,CAACiC,QAAQ,CAACE,OAAO,CAACG,MAAM,CAACS,IAAI,IAAIA,IAAI,CAACxB,MAAM,KAAK,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB1C,gBAAgB,CAAC,IAAI,CAAC;IACtBF,eAAe,CAAC,IAAI,CAAC;IACrBqB,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB;IACAxB,IAAI,CAACyB,cAAc,CAAC;MAClBR,kBAAkB,EAAEhE,KAAK,CAAC,CAAC,CAACyE,GAAG,CAAC,EAAE,EAAE,KAAK;IAC3C,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIC,MAAkB,IAAK;IACzC/C,gBAAgB,CAAC+C,MAAM,CAAC;IACxBjD,eAAe,CAAC,IAAI,CAAC;IACrBqB,IAAI,CAACyB,cAAc,CAAC;MAClB,GAAGG,MAAM;MACTC,UAAU,EAAED,MAAM,CAACC,UAAU,GAAG5E,KAAK,CAAC2E,MAAM,CAACC,UAAU,CAAC,GAAG,IAAI;MAC/DZ,kBAAkB,EAAEW,MAAM,CAACX,kBAAkB,GAAGhE,KAAK,CAAC2E,MAAM,CAACX,kBAAkB,CAAC,GAAG;IACrF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM7E,aAAa,CAAC8E,YAAY,CAACD,EAAE,CAAC;MACpCtG,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvB9B,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAOH,EAAU,IAAK;IAC1C,IAAI;MACF,MAAM7E,aAAa,CAACiF,aAAa,CAACJ,EAAE,CAAC;MACrCtG,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvB9B,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOL,EAAU,IAAK;IACzC,IAAI;MACF,MAAM7E,aAAa,CAACmF,YAAY,CAACN,EAAE,CAAC;MACpCtG,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvB9B,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MAAA,IAAAC,kBAAA,EAAAC,qBAAA;MACF,MAAMC,UAAU,GAAG;QACjB,GAAGH,MAAM;QACTV,UAAU,GAAAW,kBAAA,GAAED,MAAM,CAACV,UAAU,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmBG,MAAM,CAAC,qBAAqB,CAAC;QAC5D1B,kBAAkB,GAAAwB,qBAAA,GAAEF,MAAM,CAACtB,kBAAkB,cAAAwB,qBAAA,uBAAzBA,qBAAA,CAA2BE,MAAM,CAAC,qBAAqB,CAAC;QAC5E7C,MAAM,EAAElB,aAAa,GAAGA,aAAa,CAACkB,MAAM,GAAG;MACjD,CAAC;MAED,IAAIlB,aAAa,EAAE;QACjB,MAAM1B,aAAa,CAAC0F,YAAY,CAAC;UAAE,GAAGhE,aAAa;UAAE,GAAG8D;QAAW,CAAC,CAAC;QACrEjH,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAM/E,aAAa,CAAC2F,YAAY,CAACH,UAAU,CAAC;QAC5CjH,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACzB;MACAtD,eAAe,CAAC,KAAK,CAAC;MACtBwB,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdzF,OAAO,CAACyF,KAAK,CAACtC,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;IAChD;EACF,CAAC;EAED,MAAMkE,YAAY,GAAGA,CAAA,KAAM;IACzBvD,aAAa,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDW,YAAY,CAAC,CAAC;EAChB,CAAC;EAED,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IACxBpD,eAAe,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAEC;IACV,CAAC,CAAC;IACFR,aAAa,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDwD,UAAU,CAAC7C,YAAY,EAAE,GAAG,CAAC;EAC/B,CAAC;EAED,MAAM8C,cAAc,GAAInD,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,MAAM;MACf,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,IAAI;QACP,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMkB,SAAS,GAAGA,CAACC,kBAA0B,EAAEnB,MAAc,KAAK;IAChE,IAAIA,MAAM,KAAK,KAAK,EAAE,OAAO,KAAK;IAClC,OAAO7C,KAAK,CAAC,CAAC,CAACiG,OAAO,CAACjG,KAAK,CAACgE,kBAAkB,CAAC,CAAC;EACnD,CAAC;EAED,MAAMkC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,IAAY,IAAKA,IAAI,GAAGzG,KAAK,CAACyG,IAAI,CAAC,CAACf,MAAM,CAAC,kBAAkB,CAAC,GAAG;EAC5E,CAAC,EACD;IACES,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,oBAAoB;IAC/BC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,IAAY,EAAE9B,MAAkB,KAAK;MAC5C,MAAM+B,MAAM,GAAG3C,SAAS,CAAC0C,IAAI,EAAE9B,MAAM,CAAC9B,MAAM,CAAC;MAC7C,oBACErC,OAAA;QAAMmG,KAAK,EAAE;UAAEC,KAAK,EAAEF,MAAM,GAAG,SAAS,GAAG5D;QAAU,CAAE;QAAA+D,QAAA,GACpD7G,KAAK,CAACyG,IAAI,CAAC,CAACf,MAAM,CAAC,YAAY,CAAC,EAChCgB,MAAM,iBAAIlG,OAAA,CAAC7B,GAAG;UAACiI,KAAK,EAAC,KAAK;UAACD,KAAK,EAAE;YAAEG,UAAU,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAEX;EACF,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAAC3D,MAAc,EAAE8B,MAAkB,KAAK;MAC9C,MAAMwC,aAAa,GAAGpD,SAAS,CAACY,MAAM,CAACX,kBAAkB,EAAEnB,MAAM,CAAC,IAAIA,MAAM,KAAK,KAAK,GAAG,IAAI,GAAGA,MAAM;MACtG,oBAAOrC,OAAA,CAAC7B,GAAG;QAACiI,KAAK,EAAEZ,cAAc,CAACmB,aAAa,CAAE;QAAAN,QAAA,EAAEM;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACzE;EACF,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACY,CAAM,EAAEzC,MAAkB,kBACjCnE,OAAA,CAACrC,KAAK;MAACmF,IAAI,EAAC,OAAO;MAAAuD,QAAA,GAChBlC,MAAM,CAAC9B,MAAM,KAAK,KAAK,iBACtBrC,OAAA,CAAAE,SAAA;QAAAmG,QAAA,gBACErG,OAAA,CAAC3B,OAAO;UAACsH,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACnBrG,OAAA,CAACtC,MAAM;YACLmJ,IAAI,EAAC,MAAM;YACX/D,IAAI,EAAC,OAAO;YACZgE,IAAI,eAAE9G,OAAA,CAACjB,aAAa;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBK,OAAO,EAAEA,CAAA,KAAMtC,aAAa,CAACN,MAAM,CAACG,EAAE,CAAE;YACxC6B,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACV1G,OAAA,CAAC3B,OAAO;UAACsH,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACnBrG,OAAA,CAACtC,MAAM;YACLmJ,IAAI,EAAC,MAAM;YACX/D,IAAI,EAAC,OAAO;YACZgE,IAAI,eAAE9G,OAAA,CAAChB,aAAa;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBK,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACR,MAAM,CAACG,EAAE,CAAE;YACvC6B,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA,eACV,CACH,eACD1G,OAAA,CAAC3B,OAAO;QAACsH,KAAK,EAAC,cAAI;QAAAU,QAAA,eACjBrG,OAAA,CAACtC,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,OAAO;UACZgE,IAAI,eAAE9G,OAAA,CAACpB,YAAY;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBK,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAACC,MAAM;QAAE;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1G,OAAA,CAAC/B,UAAU;QACT0H,KAAK,EAAC,gFAAe;QACrBqB,SAAS,EAAEA,CAAA,KAAM3C,YAAY,CAACF,MAAM,CAACG,EAAE,CAAE;QACzC2C,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAb,QAAA,eAEfrG,OAAA,CAAC3B,OAAO;UAACsH,KAAK,EAAC,cAAI;UAAAU,QAAA,eACjBrG,OAAA,CAACtC,MAAM;YACLmJ,IAAI,EAAC,MAAM;YACX/D,IAAI,EAAC,OAAO;YACZqE,MAAM;YACNL,IAAI,eAAE9G,OAAA,CAACnB,cAAc;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE1G,OAAA;IAAKmG,KAAK,EAAE;MAAEiB,OAAO,EAAE;IAAQ,CAAE;IAAAf,QAAA,gBAE/BrG,OAAA,CAACzB,GAAG;MAAC8I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAClB,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG,CAAE;MAAAjB,QAAA,gBACjDrG,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,0BAAM;YACZgC,KAAK,EAAEtG,UAAU,CAACE,KAAM;YACxBqG,MAAM,eAAE5H,OAAA,CAACf,YAAY;cAACkH,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1G,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXgC,KAAK,EAAEtG,UAAU,CAACG,OAAQ;YAC1BoG,MAAM,eAAE5H,OAAA,CAACd,mBAAmB;cAACiH,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7DmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1G,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXgC,KAAK,EAAEtG,UAAU,CAACI,QAAS;YAC3BmG,MAAM,eAAE5H,OAAA,CAACV,YAAY;cAAC6G,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1G,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXgC,KAAK,EAAEtG,UAAU,CAACK,QAAS;YAC3BkG,MAAM,eAAE5H,OAAA,CAACjB,aAAa;cAACoH,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvDmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1G,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,0BAAM;YACZgC,KAAK,EAAEtG,UAAU,CAACM,OAAQ;YAC1BiG,MAAM,eAAE5H,OAAA,CAACT,eAAe;cAAC4G,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzDmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1G,OAAA,CAACxB,GAAG;QAAC+I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAChCrG,OAAA,CAAC9B,IAAI;UAAAmI,QAAA,eACHrG,OAAA,CAACvB,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXgC,KAAK,EAAEtG,UAAU,CAACO,QAAS;YAC3BgG,MAAM,eAAE5H,OAAA,CAAChB,aAAa;cAACmH,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvDmB,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1G,OAAA,CAAC9B,IAAI;MACHyH,KAAK,eACH3F,OAAA,CAACrC,KAAK;QAAA0I,QAAA,gBACJrG,OAAA,CAACf,YAAY;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChB1G,OAAA,CAACK,KAAK;UAACyH,KAAK,EAAE,CAAE;UAAC3B,KAAK,EAAE;YAAE4B,MAAM,EAAE;UAAE,CAAE;UAAA1B,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACR;MACDsB,KAAK,eACHhI,OAAA,CAACrC,KAAK;QAAA0I,QAAA,gBACJrG,OAAA,CAAC3B,OAAO;UAACsH,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACnBrG,OAAA,CAACtC,MAAM;YACLoJ,IAAI,eAAE9G,OAAA,CAACb,cAAc;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBK,OAAO,EAAErE,YAAa;YACtB3B,OAAO,EAAEA;UAAQ;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACV1G,OAAA,CAAC3B,OAAO;UAACsH,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACnBrG,OAAA,CAACtC,MAAM;YAACoJ,IAAI,eAAE9G,OAAA,CAACZ,cAAc;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;MAAAL,QAAA,gBAGDrG,OAAA,CAAC1B,KAAK;QACJN,OAAO,EAAC,sCAAQ;QAChBiK,WAAW,EAAC,8PAA4C;QACxDpB,IAAI,EAAC,MAAM;QACXqB,QAAQ;QACR/B,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG;MAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAGF1G,OAAA,CAAC9B,IAAI;QACH4E,IAAI,EAAC,OAAO;QACZqD,KAAK,EAAE;UACLmB,YAAY,EAAE,EAAE;UAChBa,UAAU,EAAE,mDAAmD;UAC/DC,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,eAEFrG,OAAA,CAACzB,GAAG;UAAC8I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAhB,QAAA,gBACpBrG,OAAA,CAACxB,GAAG;YAAC+I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACzBrG,OAAA,CAAC5B,KAAK;cACJiK,WAAW,EAAC,sCAAQ;cACpBV,KAAK,EAAE1F,YAAY,CAACE,QAAS;cAC7BmG,QAAQ,EAAGC,CAAC,IAAKrG,eAAe,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEf,QAAQ,EAAEoG,CAAC,CAACC,MAAM,CAACb;cAAM,CAAC,CAAC,CAAE;cAClFC,MAAM,eAAE5H,OAAA,CAACX,YAAY;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB+B,UAAU;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1G,OAAA,CAACxB,GAAG;YAAC+I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACzBrG,OAAA,CAAC5B,KAAK;cACJiK,WAAW,EAAC,sCAAQ;cACpBV,KAAK,EAAE1F,YAAY,CAACG,QAAS;cAC7BkG,QAAQ,EAAGC,CAAC,IAAKrG,eAAe,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEd,QAAQ,EAAEmG,CAAC,CAACC,MAAM,CAACb;cAAM,CAAC,CAAC,CAAE;cAClFC,MAAM,eAAE5H,OAAA,CAACV,YAAY;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB+B,UAAU;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1G,OAAA,CAACxB,GAAG;YAAC+I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACzBrG,OAAA,CAAClC,MAAM;cACLuK,WAAW,EAAC,0BAAM;cAClBV,KAAK,EAAE1F,YAAY,CAACI,MAAO;cAC3BiG,QAAQ,EAAGX,KAAK,IAAKzF,eAAe,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEb,MAAM,EAAEsF;cAAM,CAAC,CAAC,CAAE;cAC3ExB,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cACzB2C,UAAU;cAAApC,QAAA,gBAEVrG,OAAA,CAACG,MAAM;gBAACwH,KAAK,EAAC,oBAAK;gBAAAtB,QAAA,eACjBrG,OAAA,CAACrC,KAAK;kBAAA0I,QAAA,gBACJrG,OAAA,CAACd,mBAAmB;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEzB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACT1G,OAAA,CAACG,MAAM;gBAACwH,KAAK,EAAC,oBAAK;gBAAAtB,QAAA,eACjBrG,OAAA,CAACrC,KAAK;kBAAA0I,QAAA,gBACJrG,OAAA,CAACV,YAAY;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACT1G,OAAA,CAACG,MAAM;gBAACwH,KAAK,EAAC,oBAAK;gBAAAtB,QAAA,eACjBrG,OAAA,CAACrC,KAAK;kBAAA0I,QAAA,gBACJrG,OAAA,CAACjB,aAAa;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACT1G,OAAA,CAACG,MAAM;gBAACwH,KAAK,EAAC,oBAAK;gBAAAtB,QAAA,eACjBrG,OAAA,CAACrC,KAAK;kBAAA0I,QAAA,gBACJrG,OAAA,CAAChB,aAAa;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN1G,OAAA,CAACxB,GAAG;YAAC+I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACzBrG,OAAA,CAACrC,KAAK;cAACwI,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cAAAO,QAAA,gBAC9BrG,OAAA,CAACtC,MAAM;gBACLmJ,IAAI,EAAC,SAAS;gBACdC,IAAI,eAAE9G,OAAA,CAAClB,cAAc;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBK,OAAO,EAAE1B,YAAa;gBAAAgB,QAAA,EACvB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1G,OAAA,CAACtC,MAAM;gBAACqJ,OAAO,EAAEzB,WAAY;gBAAAe,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGP1G,OAAA,CAACzB,GAAG;QAAC8I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAClB,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG,CAAE;QAAAjB,QAAA,gBACjDrG,OAAA,CAACxB,GAAG;UAAC+I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAApB,QAAA,eACzBrG,OAAA,CAACrC,KAAK;YAAA0I,QAAA,EACH5D,aAAa,CAAC7C,WAAW,CAAC8I,cAAc,CAAC,iBACxC1I,OAAA,CAACtC,MAAM;cACLmJ,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE9G,OAAA,CAACrB,YAAY;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBK,OAAO,EAAEjD,SAAU;cACnBhB,IAAI,EAAC,OAAO;cAAAuD,QAAA,EACb;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1G,OAAA,CAACxB,GAAG;UAAC+I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACtB,KAAK,EAAE;YAAEwC,SAAS,EAAE;UAAQ,CAAE;UAAAtC,QAAA,eACzDrG,OAAA,CAACM,IAAI;YAACuG,IAAI,EAAC,WAAW;YAAAR,QAAA,GAAC,qBACjB,EAACxE,UAAU,CAACN,KAAK,EAAC,iCACxB;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR1G,OAAA,CAACvC,KAAK;QACJiI,OAAO,EAAEA,OAAQ;QACjBkD,UAAU,EAAEnI,OAAQ;QACpBM,OAAO,EAAEA,OAAQ;QACjBc,UAAU,EAAE;UACV,GAAGA,UAAU;UACbgH,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGxH,KAAK,IAAK,KAAKA,KAAK,MAAM;UACtC+G,QAAQ,EAAEA,CAACU,IAAI,EAAEhH,QAAQ,KAAK;YAC5BF,aAAa,CAACoB,IAAI,KAAK;cACrB,GAAGA,IAAI;cACPnB,OAAO,EAAEiH,IAAI;cACbhH,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC,CAAC;UACL;QACF,CAAE;QACFiH,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAGF1G,OAAA,CAACpC,KAAK;QACJ+H,KAAK,EAAExE,aAAa,GAAG,QAAQ,GAAG,QAAS;QAC3CiI,IAAI,EAAEnI,YAAa;QACnBoI,QAAQ,EAAEA,CAAA,KAAMnI,eAAe,CAAC,KAAK,CAAE;QACvCoI,IAAI,EAAEA,CAAA,KAAM/G,IAAI,CAACgH,MAAM,CAAC,CAAE;QAC1BzD,KAAK,EAAE,GAAI;QAAAO,QAAA,eAEXrG,OAAA,CAACnC,IAAI;UACH0E,IAAI,EAAEA,IAAK;UACXiH,MAAM,EAAC,UAAU;UACjBC,QAAQ,EAAE5E,YAAa;UAAAwB,QAAA,gBAEvBrG,OAAA,CAACnC,IAAI,CAAC6L,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9L,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAqI,QAAA,eAEhDrG,OAAA,CAAClC,MAAM;cAACuK,WAAW,EAAC,4CAAS;cAAC0B,UAAU;cAACC,gBAAgB,EAAC,UAAU;cAAA3D,QAAA,EACjE1F,KAAK,CAACsJ,GAAG,CAACC,IAAI,iBACblK,OAAA,CAACG,MAAM;gBAAewH,KAAK,EAAEuC,IAAI,CAAC5F,EAAG;gBAAA+B,QAAA,GAClC6D,IAAI,CAACP,IAAI,EAAC,IAAE,EAACO,IAAI,CAACC,QAAQ,EAAC,GAC9B;cAAA,GAFaD,IAAI,CAAC5F,EAAE;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZ1G,OAAA,CAACnC,IAAI,CAAC6L,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9L,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAqI,QAAA,eAEhDrG,OAAA,CAAClC,MAAM;cAACuK,WAAW,EAAC,4CAAS;cAAC0B,UAAU;cAACC,gBAAgB,EAAC,UAAU;cAAA3D,QAAA,EACjExF,KAAK,CAACoJ,GAAG,CAACpG,IAAI,iBACb7D,OAAA,CAACG,MAAM;gBAAewH,KAAK,EAAE9D,IAAI,CAACS,EAAG;gBAAA+B,QAAA,GAClCxC,IAAI,CAAC8F,IAAI,EAAC,KAAG,EAAC9F,IAAI,CAACuG,MAAM;cAAA,GADfvG,IAAI,CAACS,EAAE;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZ1G,OAAA,CAACnC,IAAI,CAAC6L,IAAI;YACRC,IAAI,EAAC,YAAY;YACjBC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9L,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAqI,QAAA,eAEhDrG,OAAA,CAACjC,UAAU;cACTsM,QAAQ;cACRhC,WAAW,EAAC,4CAAS;cACrBlC,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cACzBZ,MAAM,EAAC;YAAqB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ1G,OAAA,CAACnC,IAAI,CAAC6L,IAAI;YACRC,IAAI,EAAC,oBAAoB;YACzBC,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9L,OAAO,EAAE;YAAY,CAAC,CAAE;YAAAqI,QAAA,eAElDrG,OAAA,CAACjC,UAAU;cACTsK,WAAW,EAAC,wDAAW;cACvBlC,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cACzBZ,MAAM,EAAC;YAAY;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClG,EAAA,CAplBID,gBAA0B;EAAA,QAyBf1C,IAAI,CAAC2E,OAAO,EACD3C,aAAa;AAAA;AAAAyK,EAAA,GA1BnC/J,gBAA0B;AAslBhC,eAAeA,gBAAgB;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}