{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nconst onKeyDown = event => {\n  const {\n    keyCode\n  } = event;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nconst FilterDropdownMenuWrapper = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(\"div\", {\n  className: props.className,\n  onClick: e => e.stopPropagation(),\n  onKeyDown: onKeyDown,\n  ref: ref\n}, props.children)));\nif (process.env.NODE_ENV !== 'production') {\n  FilterDropdownMenuWrapper.displayName = 'FilterDropdownMenuWrapper';\n}\nexport default FilterDropdownMenuWrapper;", "map": {"version": 3, "names": ["React", "KeyCode", "onKeyDown", "event", "keyCode", "ENTER", "stopPropagation", "FilterDropdownMenuWrapper", "forwardRef", "props", "ref", "createElement", "className", "onClick", "e", "children", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/hooks/useFilter/FilterWrapper.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nconst onKeyDown = event => {\n  const {\n    keyCode\n  } = event;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nconst FilterDropdownMenuWrapper = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(\"div\", {\n  className: props.className,\n  onClick: e => e.stopPropagation(),\n  onKeyDown: onKeyDown,\n  ref: ref\n}, props.children)));\nif (process.env.NODE_ENV !== 'production') {\n  FilterDropdownMenuWrapper.displayName = 'FilterDropdownMenuWrapper';\n}\nexport default FilterDropdownMenuWrapper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,IAAIC,OAAO,KAAKH,OAAO,CAACI,KAAK,EAAE;IAC7BF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,MAAM,aAAaV,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;EACvHC,SAAS,EAAEH,KAAK,CAACG,SAAS;EAC1BC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACR,eAAe,CAAC,CAAC;EACjCJ,SAAS,EAAEA,SAAS;EACpBQ,GAAG,EAAEA;AACP,CAAC,EAAED,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC;AACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCX,yBAAyB,CAACY,WAAW,GAAG,2BAA2B;AACrE;AACA,eAAeZ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}