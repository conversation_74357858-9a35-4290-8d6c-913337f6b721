{"ast": null, "code": "import { textEllipsis } from '../../style';\nconst genEllipsisStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-cell-ellipsis\")]: Object.assign(Object.assign({}, textEllipsis), {\n        wordBreak: 'keep-all',\n        // Fixed first or last should special process\n        [\"\\n          &\".concat(componentCls, \"-cell-fix-left-last,\\n          &\").concat(componentCls, \"-cell-fix-right-first\\n        \")]: {\n          overflow: 'visible',\n          [\"\".concat(componentCls, \"-cell-content\")]: {\n            display: 'block',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          }\n        },\n        [\"\".concat(componentCls, \"-column-title\")]: {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          wordBreak: 'keep-all'\n        }\n      })\n    }\n  };\n};\nexport default genEllipsisStyle;", "map": {"version": 3, "names": ["textEllipsis", "genEllipsisStyle", "token", "componentCls", "concat", "Object", "assign", "wordBreak", "overflow", "display", "textOverflow"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/ellipsis.js"], "sourcesContent": ["import { textEllipsis } from '../../style';\nconst genEllipsisStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-cell-ellipsis`]: Object.assign(Object.assign({}, textEllipsis), {\n        wordBreak: 'keep-all',\n        // Fixed first or last should special process\n        [`\n          &${componentCls}-cell-fix-left-last,\n          &${componentCls}-cell-fix-right-first\n        `]: {\n          overflow: 'visible',\n          [`${componentCls}-cell-content`]: {\n            display: 'block',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          }\n        },\n        [`${componentCls}-column-title`]: {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          wordBreak: 'keep-all'\n        }\n      })\n    }\n  };\n};\nexport default genEllipsisStyle;"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,IAAAE,MAAA,CAAID,YAAY,gBAAa;MAC3B,IAAAC,MAAA,CAAID,YAAY,sBAAmBE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,YAAY,CAAC,EAAE;QAChFO,SAAS,EAAE,UAAU;QACrB;QACA,iBAAAH,MAAA,CACKD,YAAY,uCAAAC,MAAA,CACZD,YAAY,uCACb;UACFK,QAAQ,EAAE,SAAS;UACnB,IAAAJ,MAAA,CAAID,YAAY,qBAAkB;YAChCM,OAAO,EAAE,OAAO;YAChBD,QAAQ,EAAE,QAAQ;YAClBE,YAAY,EAAE;UAChB;QACF,CAAC;QACD,IAAAN,MAAA,CAAID,YAAY,qBAAkB;UAChCK,QAAQ,EAAE,QAAQ;UAClBE,YAAY,EAAE,UAAU;UACxBH,SAAS,EAAE;QACb;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAeN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}