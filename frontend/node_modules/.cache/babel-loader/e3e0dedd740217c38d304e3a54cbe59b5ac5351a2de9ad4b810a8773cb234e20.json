{"ast": null, "code": "// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */\n\nvar HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nexport function calculateNodeStyling(node) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');\n  if (useCache && computedStyleCache[nodeRef]) {\n    return computedStyleCache[nodeRef];\n  }\n  var style = window.getComputedStyle(node);\n  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');\n  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));\n  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));\n  var sizingStyle = SIZING_STYLE.map(function (name) {\n    return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n  }).join(';');\n  var nodeInfo = {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize,\n    boxSizing: boxSizing\n  };\n  if (useCache && nodeRef) {\n    computedStyleCache[nodeRef] = nodeInfo;\n  }\n  return nodeInfo;\n}\nexport default function calculateAutoSizeStyle(uiTextNode) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tab-index', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    // fix: A form field element should have an id or name attribute\n    // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n    hiddenTextarea.setAttribute('name', 'hiddenTextarea');\n    document.body.appendChild(hiddenTextarea);\n  }\n\n  // Fix wrap=\"off\" issue\n  // https://github.com/ant-design/ant-design/issues/6577\n  if (uiTextNode.getAttribute('wrap')) {\n    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));\n  } else {\n    hiddenTextarea.removeAttribute('wrap');\n  }\n\n  // Copy all CSS properties that have an impact on the height of the content in\n  // the textbox\n  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),\n    paddingSize = _calculateNodeStyling.paddingSize,\n    borderSize = _calculateNodeStyling.borderSize,\n    boxSizing = _calculateNodeStyling.boxSizing,\n    sizingStyle = _calculateNodeStyling.sizingStyle;\n\n  // Need to have the overflow attribute to hide the scrollbar otherwise\n  // text-lines will not calculated properly as the shadow will technically be\n  // narrower for content\n  hiddenTextarea.setAttribute('style', \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';\n  var minHeight = undefined;\n  var maxHeight = undefined;\n  var overflowY;\n  var height = hiddenTextarea.scrollHeight;\n  if (boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    height += borderSize;\n  } else if (boxSizing === 'content-box') {\n    // remove padding, since height = content\n    height -= paddingSize;\n  }\n  if (minRows !== null || maxRows !== null) {\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = ' ';\n    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    if (minRows !== null) {\n      minHeight = singleRowHeight * minRows;\n      if (boxSizing === 'border-box') {\n        minHeight = minHeight + paddingSize + borderSize;\n      }\n      height = Math.max(minHeight, height);\n    }\n    if (maxRows !== null) {\n      maxHeight = singleRowHeight * maxRows;\n      if (boxSizing === 'border-box') {\n        maxHeight = maxHeight + paddingSize + borderSize;\n      }\n      overflowY = height > maxHeight ? '' : 'hidden';\n      height = Math.min(maxHeight, height);\n    }\n  }\n  var style = {\n    height: height,\n    overflowY: overflowY,\n    resize: 'none'\n  };\n  if (minHeight) {\n    style.minHeight = minHeight;\n  }\n  if (maxHeight) {\n    style.maxHeight = maxHeight;\n  }\n  return style;\n}", "map": {"version": 3, "names": ["HIDDEN_TEXTAREA_STYLE", "SIZING_STYLE", "computedStyleCache", "hiddenTextarea", "calculateNodeStyling", "node", "useCache", "arguments", "length", "undefined", "nodeRef", "getAttribute", "style", "window", "getComputedStyle", "boxSizing", "getPropertyValue", "paddingSize", "parseFloat", "borderSize", "sizingStyle", "map", "name", "concat", "join", "nodeInfo", "calculateAutoSizeStyle", "uiTextNode", "minRows", "maxRows", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "removeAttribute", "_calculateNodeStyling", "value", "placeholder", "minHeight", "maxHeight", "overflowY", "height", "scrollHeight", "singleRowHeight", "Math", "max", "min", "resize"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-textarea/es/calculateNodeHeight.js"], "sourcesContent": ["// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */\n\nvar HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nexport function calculateNodeStyling(node) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');\n  if (useCache && computedStyleCache[nodeRef]) {\n    return computedStyleCache[nodeRef];\n  }\n  var style = window.getComputedStyle(node);\n  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');\n  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));\n  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));\n  var sizingStyle = SIZING_STYLE.map(function (name) {\n    return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n  }).join(';');\n  var nodeInfo = {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize,\n    boxSizing: boxSizing\n  };\n  if (useCache && nodeRef) {\n    computedStyleCache[nodeRef] = nodeInfo;\n  }\n  return nodeInfo;\n}\nexport default function calculateAutoSizeStyle(uiTextNode) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tab-index', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    // fix: A form field element should have an id or name attribute\n    // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n    hiddenTextarea.setAttribute('name', 'hiddenTextarea');\n    document.body.appendChild(hiddenTextarea);\n  }\n\n  // Fix wrap=\"off\" issue\n  // https://github.com/ant-design/ant-design/issues/6577\n  if (uiTextNode.getAttribute('wrap')) {\n    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));\n  } else {\n    hiddenTextarea.removeAttribute('wrap');\n  }\n\n  // Copy all CSS properties that have an impact on the height of the content in\n  // the textbox\n  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),\n    paddingSize = _calculateNodeStyling.paddingSize,\n    borderSize = _calculateNodeStyling.borderSize,\n    boxSizing = _calculateNodeStyling.boxSizing,\n    sizingStyle = _calculateNodeStyling.sizingStyle;\n\n  // Need to have the overflow attribute to hide the scrollbar otherwise\n  // text-lines will not calculated properly as the shadow will technically be\n  // narrower for content\n  hiddenTextarea.setAttribute('style', \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';\n  var minHeight = undefined;\n  var maxHeight = undefined;\n  var overflowY;\n  var height = hiddenTextarea.scrollHeight;\n  if (boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    height += borderSize;\n  } else if (boxSizing === 'content-box') {\n    // remove padding, since height = content\n    height -= paddingSize;\n  }\n  if (minRows !== null || maxRows !== null) {\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = ' ';\n    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    if (minRows !== null) {\n      minHeight = singleRowHeight * minRows;\n      if (boxSizing === 'border-box') {\n        minHeight = minHeight + paddingSize + borderSize;\n      }\n      height = Math.max(minHeight, height);\n    }\n    if (maxRows !== null) {\n      maxHeight = singleRowHeight * maxRows;\n      if (boxSizing === 'border-box') {\n        maxHeight = maxHeight + paddingSize + borderSize;\n      }\n      overflowY = height > maxHeight ? '' : 'hidden';\n      height = Math.min(maxHeight, height);\n    }\n  }\n  var style = {\n    height: height,\n    overflowY: overflowY,\n    resize: 'none'\n  };\n  if (minHeight) {\n    style.minHeight = minHeight;\n  }\n  if (maxHeight) {\n    style.maxHeight = maxHeight;\n  }\n  return style;\n}"], "mappings": "AAAA;;AAEA;AACA;AACA;;AAEA,IAAIA,qBAAqB,GAAG,qSAAqS;AACjU,IAAIC,YAAY,GAAG,CAAC,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;AACxS,IAAIC,kBAAkB,GAAG,CAAC,CAAC;AAC3B,IAAIC,cAAc;AAClB,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxF,IAAIG,OAAO,GAAGL,IAAI,CAACM,YAAY,CAAC,IAAI,CAAC,IAAIN,IAAI,CAACM,YAAY,CAAC,cAAc,CAAC,IAAIN,IAAI,CAACM,YAAY,CAAC,MAAM,CAAC;EACvG,IAAIL,QAAQ,IAAIJ,kBAAkB,CAACQ,OAAO,CAAC,EAAE;IAC3C,OAAOR,kBAAkB,CAACQ,OAAO,CAAC;EACpC;EACA,IAAIE,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACT,IAAI,CAAC;EACzC,IAAIU,SAAS,GAAGH,KAAK,CAACI,gBAAgB,CAAC,YAAY,CAAC,IAAIJ,KAAK,CAACI,gBAAgB,CAAC,iBAAiB,CAAC,IAAIJ,KAAK,CAACI,gBAAgB,CAAC,oBAAoB,CAAC;EACjJ,IAAIC,WAAW,GAAGC,UAAU,CAACN,KAAK,CAACI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,GAAGE,UAAU,CAACN,KAAK,CAACI,gBAAgB,CAAC,aAAa,CAAC,CAAC;EAC1H,IAAIG,UAAU,GAAGD,UAAU,CAACN,KAAK,CAACI,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAGE,UAAU,CAACN,KAAK,CAACI,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;EACnI,IAAII,WAAW,GAAGnB,YAAY,CAACoB,GAAG,CAAC,UAAUC,IAAI,EAAE;IACjD,OAAO,EAAE,CAACC,MAAM,CAACD,IAAI,EAAE,GAAG,CAAC,CAACC,MAAM,CAACX,KAAK,CAACI,gBAAgB,CAACM,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;EACZ,IAAIC,QAAQ,GAAG;IACbL,WAAW,EAAEA,WAAW;IACxBH,WAAW,EAAEA,WAAW;IACxBE,UAAU,EAAEA,UAAU;IACtBJ,SAAS,EAAEA;EACb,CAAC;EACD,IAAIT,QAAQ,IAAII,OAAO,EAAE;IACvBR,kBAAkB,CAACQ,OAAO,CAAC,GAAGe,QAAQ;EACxC;EACA,OAAOA,QAAQ;AACjB;AACA,eAAe,SAASC,sBAAsBA,CAACC,UAAU,EAAE;EACzD,IAAIrB,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxF,IAAIqB,OAAO,GAAGrB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACtF,IAAIsB,OAAO,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACtF,IAAI,CAACJ,cAAc,EAAE;IACnBA,cAAc,GAAG2B,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IACnD5B,cAAc,CAAC6B,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;IAC9C7B,cAAc,CAAC6B,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAClD;IACA;IACA;IACA7B,cAAc,CAAC6B,YAAY,CAAC,MAAM,EAAE,gBAAgB,CAAC;IACrDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC/B,cAAc,CAAC;EAC3C;;EAEA;EACA;EACA,IAAIwB,UAAU,CAAChB,YAAY,CAAC,MAAM,CAAC,EAAE;IACnCR,cAAc,CAAC6B,YAAY,CAAC,MAAM,EAAEL,UAAU,CAAChB,YAAY,CAAC,MAAM,CAAC,CAAC;EACtE,CAAC,MAAM;IACLR,cAAc,CAACgC,eAAe,CAAC,MAAM,CAAC;EACxC;;EAEA;EACA;EACA,IAAIC,qBAAqB,GAAGhC,oBAAoB,CAACuB,UAAU,EAAErB,QAAQ,CAAC;IACpEW,WAAW,GAAGmB,qBAAqB,CAACnB,WAAW;IAC/CE,UAAU,GAAGiB,qBAAqB,CAACjB,UAAU;IAC7CJ,SAAS,GAAGqB,qBAAqB,CAACrB,SAAS;IAC3CK,WAAW,GAAGgB,qBAAqB,CAAChB,WAAW;;EAEjD;EACA;EACA;EACAjB,cAAc,CAAC6B,YAAY,CAAC,OAAO,EAAE,EAAE,CAACT,MAAM,CAACH,WAAW,EAAE,GAAG,CAAC,CAACG,MAAM,CAACvB,qBAAqB,CAAC,CAAC;EAC/FG,cAAc,CAACkC,KAAK,GAAGV,UAAU,CAACU,KAAK,IAAIV,UAAU,CAACW,WAAW,IAAI,EAAE;EACvE,IAAIC,SAAS,GAAG9B,SAAS;EACzB,IAAI+B,SAAS,GAAG/B,SAAS;EACzB,IAAIgC,SAAS;EACb,IAAIC,MAAM,GAAGvC,cAAc,CAACwC,YAAY;EACxC,IAAI5B,SAAS,KAAK,YAAY,EAAE;IAC9B;IACA2B,MAAM,IAAIvB,UAAU;EACtB,CAAC,MAAM,IAAIJ,SAAS,KAAK,aAAa,EAAE;IACtC;IACA2B,MAAM,IAAIzB,WAAW;EACvB;EACA,IAAIW,OAAO,KAAK,IAAI,IAAIC,OAAO,KAAK,IAAI,EAAE;IACxC;IACA1B,cAAc,CAACkC,KAAK,GAAG,GAAG;IAC1B,IAAIO,eAAe,GAAGzC,cAAc,CAACwC,YAAY,GAAG1B,WAAW;IAC/D,IAAIW,OAAO,KAAK,IAAI,EAAE;MACpBW,SAAS,GAAGK,eAAe,GAAGhB,OAAO;MACrC,IAAIb,SAAS,KAAK,YAAY,EAAE;QAC9BwB,SAAS,GAAGA,SAAS,GAAGtB,WAAW,GAAGE,UAAU;MAClD;MACAuB,MAAM,GAAGG,IAAI,CAACC,GAAG,CAACP,SAAS,EAAEG,MAAM,CAAC;IACtC;IACA,IAAIb,OAAO,KAAK,IAAI,EAAE;MACpBW,SAAS,GAAGI,eAAe,GAAGf,OAAO;MACrC,IAAId,SAAS,KAAK,YAAY,EAAE;QAC9ByB,SAAS,GAAGA,SAAS,GAAGvB,WAAW,GAAGE,UAAU;MAClD;MACAsB,SAAS,GAAGC,MAAM,GAAGF,SAAS,GAAG,EAAE,GAAG,QAAQ;MAC9CE,MAAM,GAAGG,IAAI,CAACE,GAAG,CAACP,SAAS,EAAEE,MAAM,CAAC;IACtC;EACF;EACA,IAAI9B,KAAK,GAAG;IACV8B,MAAM,EAAEA,MAAM;IACdD,SAAS,EAAEA,SAAS;IACpBO,MAAM,EAAE;EACV,CAAC;EACD,IAAIT,SAAS,EAAE;IACb3B,KAAK,CAAC2B,SAAS,GAAGA,SAAS;EAC7B;EACA,IAAIC,SAAS,EAAE;IACb5B,KAAK,CAAC4B,SAAS,GAAGA,SAAS;EAC7B;EACA,OAAO5B,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}