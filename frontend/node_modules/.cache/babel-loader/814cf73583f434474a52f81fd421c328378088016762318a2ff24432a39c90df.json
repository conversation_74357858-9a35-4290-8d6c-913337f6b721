{"ast": null, "code": "import { StackY } from './stackY';\n// Avoid duplicate stackY.\n// In most of case only one of stackY and dodgeX is needed.\n// So pass statistic with stackY and dodgeX.\nfunction exclude(transform) {\n  const {\n    type\n  } = transform;\n  const excludes = ['stackY', 'dodgeX', 'groupX'];\n  return typeof type === 'string' && excludes.includes(type);\n}\n/**\n * Add zero constant encode for x channel.\n * This is useful for interval geometry.\n */\nexport const MaybeStackY = options => {\n  return (I, mark, context) => {\n    // Skip some transform.\n    const {\n      encode,\n      transform = []\n    } = mark;\n    if (transform.some(exclude)) return [I, mark];\n    // StackY need both x and y channel values.\n    const {\n      x,\n      y\n    } = encode;\n    if (x === undefined || y === undefined) return [I, mark];\n    const {\n      series\n    } = options;\n    const groupBy = series ? ['x', 'series'] : 'x';\n    return StackY({\n      groupBy\n    })(I, mark, context);\n  };\n};\nMaybeStackY.props = {};", "map": {"version": 3, "names": ["StackY", "exclude", "transform", "type", "excludes", "includes", "MaybeStackY", "options", "I", "mark", "context", "encode", "some", "x", "y", "undefined", "series", "groupBy", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/maybeStackY.ts"], "sourcesContent": ["import { TransformComponent as TC, TransformSpec } from '../runtime';\nimport { StackY } from './stackY';\n\nexport type MaybeStackYOptions = {\n  series?: boolean;\n};\n\n// Avoid duplicate stackY.\n// In most of case only one of stackY and dodgeX is needed.\n// So pass statistic with stackY and dodgeX.\nfunction exclude(transform: TransformSpec): boolean {\n  const { type } = transform;\n  const excludes = ['stackY', 'dodgeX', 'groupX'];\n  return typeof type === 'string' && excludes.includes(type);\n}\n\n/**\n * Add zero constant encode for x channel.\n * This is useful for interval geometry.\n */\nexport const MaybeStackY: TC<MaybeStackYOptions> = (options) => {\n  return (I, mark, context) => {\n    // Skip some transform.\n    const { encode, transform = [] } = mark;\n    if (transform.some(exclude)) return [I, mark];\n\n    // StackY need both x and y channel values.\n    const { x, y } = encode;\n    if (x === undefined || y === undefined) return [I, mark];\n\n    const { series } = options;\n    const groupBy = series ? ['x', 'series'] : 'x';\n    return StackY({ groupBy })(I, mark, context);\n  };\n};\n\nMaybeStackY.props = {};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,UAAU;AAMjC;AACA;AACA;AACA,SAASC,OAAOA,CAACC,SAAwB;EACvC,MAAM;IAAEC;EAAI,CAAE,GAAGD,SAAS;EAC1B,MAAME,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC/C,OAAO,OAAOD,IAAI,KAAK,QAAQ,IAAIC,QAAQ,CAACC,QAAQ,CAACF,IAAI,CAAC;AAC5D;AAEA;;;;AAIA,OAAO,MAAMG,WAAW,GAA4BC,OAAO,IAAI;EAC7D,OAAO,CAACC,CAAC,EAAEC,IAAI,EAAEC,OAAO,KAAI;IAC1B;IACA,MAAM;MAAEC,MAAM;MAAET,SAAS,GAAG;IAAE,CAAE,GAAGO,IAAI;IACvC,IAAIP,SAAS,CAACU,IAAI,CAACX,OAAO,CAAC,EAAE,OAAO,CAACO,CAAC,EAAEC,IAAI,CAAC;IAE7C;IACA,MAAM;MAAEI,CAAC;MAAEC;IAAC,CAAE,GAAGH,MAAM;IACvB,IAAIE,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,EAAE,OAAO,CAACP,CAAC,EAAEC,IAAI,CAAC;IAExD,MAAM;MAAEO;IAAM,CAAE,GAAGT,OAAO;IAC1B,MAAMU,OAAO,GAAGD,MAAM,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG;IAC9C,OAAOhB,MAAM,CAAC;MAAEiB;IAAO,CAAE,CAAC,CAACT,CAAC,EAAEC,IAAI,EAAEC,OAAO,CAAC;EAC9C,CAAC;AACH,CAAC;AAEDJ,WAAW,CAACY,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}