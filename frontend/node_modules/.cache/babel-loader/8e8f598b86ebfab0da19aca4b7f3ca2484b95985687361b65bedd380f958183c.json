{"ast": null, "code": "import { Sort } from './sort';\n/**\n * Sort domain of x scale of mark groups by groups.\n */\nexport const SortX = (options = {}) => {\n  return Sort(Object.assign(Object.assign({}, options), {\n    channel: 'x'\n  }));\n};\nSortX.props = {};", "map": {"version": 3, "names": ["Sort", "SortX", "options", "Object", "assign", "channel", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/sortX.ts"], "sourcesContent": ["import { TransformComponent as TC } from '../runtime';\nimport { SortXTransform } from '../spec';\nimport { Sort } from './sort';\n\nexport type SortXOptions = Omit<SortXTransform, 'type'>;\n\n/**\n * Sort domain of x scale of mark groups by groups.\n */\nexport const SortX: TC<SortXOptions> = (options = {}) => {\n  return Sort({ ...options, channel: 'x' });\n};\n\nSortX.props = {};\n"], "mappings": "AAEA,SAASA,IAAI,QAAQ,QAAQ;AAI7B;;;AAGA,OAAO,MAAMC,KAAK,GAAqBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACtD,OAAOF,IAAI,CAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMF,OAAO;IAAEG,OAAO,EAAE;EAAG,GAAG;AAC3C,CAAC;AAEDJ,KAAK,CAACK,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}