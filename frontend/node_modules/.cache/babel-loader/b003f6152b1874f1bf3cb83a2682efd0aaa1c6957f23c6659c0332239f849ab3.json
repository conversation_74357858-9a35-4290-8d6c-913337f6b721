{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = \"\".concat(unit(lineWidth), \" \").concat(token.lineType, \" \").concat(tableBorderColor);\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-summary\")]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [\"div\".concat(componentCls, \"-summary\")]: {\n        boxShadow: \"0 \".concat(unit(calc(lineWidth).mul(-1).equal()), \" 0 \").concat(tableBorderColor)\n      }\n    }\n  };\n};\nexport default genSummaryStyle;", "map": {"version": 3, "names": ["unit", "genSummaryStyle", "token", "componentCls", "lineWidth", "tableBorderColor", "calc", "tableBorder", "concat", "lineType", "position", "zIndex", "zIndexTableFixed", "background", "tableBg", "borderBottom", "boxShadow", "mul", "equal"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/summary.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 ${unit(calc(lineWidth).mul(-1).equal())} 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,MAAAC,MAAA,CAAMR,IAAI,CAACI,SAAS,CAAC,OAAAI,MAAA,CAAIN,KAAK,CAACO,QAAQ,OAAAD,MAAA,CAAIH,gBAAgB,CAAE;EAC9E,OAAO;IACL,IAAAG,MAAA,CAAIL,YAAY,gBAAa;MAC3B,IAAAK,MAAA,CAAIL,YAAY,gBAAa;QAC3BO,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAET,KAAK,CAACU,gBAAgB;QAC9BC,UAAU,EAAEX,KAAK,CAACY,OAAO;QACzB,MAAM,EAAE;UACN,YAAY,EAAE;YACZC,YAAY,EAAER;UAChB;QACF;MACF,CAAC;MACD,OAAAC,MAAA,CAAOL,YAAY,gBAAa;QAC9Ba,SAAS,OAAAR,MAAA,CAAOR,IAAI,CAACM,IAAI,CAACF,SAAS,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,SAAAV,MAAA,CAAMH,gBAAgB;MAC7E;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}