{"ast": null, "code": "// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-tbody > tr\").concat(componentCls, \"-placeholder\")]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [\"\\n          &:hover > th,\\n          &:hover > td,\\n        \"]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;", "map": {"version": 3, "names": ["genEmptyStyle", "token", "componentCls", "concat", "textAlign", "color", "colorTextDisabled", "background", "colorBgContainer"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/empty.js"], "sourcesContent": ["// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-tbody > tr${componentCls}-placeholder`]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [`\n          &:hover > th,\n          &:hover > td,\n        `]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;"], "mappings": "AAAA;AACA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,IAAAE,MAAA,CAAID,YAAY,gBAAa;MAC3B,IAAAC,MAAA,CAAID,YAAY,iBAAAC,MAAA,CAAcD,YAAY,oBAAiB;QACzDE,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAEJ,KAAK,CAACK,iBAAiB;QAC9B,kEAGI;UACFC,UAAU,EAAEN,KAAK,CAACO;QACpB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}