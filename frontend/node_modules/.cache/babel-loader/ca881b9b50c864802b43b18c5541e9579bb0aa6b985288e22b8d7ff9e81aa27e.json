{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genRadiusStyle = token => {\n  const {\n    componentCls,\n    tableRadius\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [componentCls]: {\n        // https://github.com/ant-design/ant-design/issues/39115#issuecomment-1362314574\n        [\"\".concat(componentCls, \"-title, \").concat(componentCls, \"-header\")]: {\n          borderRadius: \"\".concat(unit(tableRadius), \" \").concat(unit(tableRadius), \" 0 0\")\n        },\n        [\"\".concat(componentCls, \"-title + \").concat(componentCls, \"-container\")]: {\n          borderStartStartRadius: 0,\n          borderStartEndRadius: 0,\n          // https://github.com/ant-design/ant-design/issues/41975\n          [\"\".concat(componentCls, \"-header, table\")]: {\n            borderRadius: 0\n          },\n          'table > thead > tr:first-child': {\n            'th:first-child, th:last-child, td:first-child, td:last-child': {\n              borderRadius: 0\n            }\n          }\n        },\n        '&-container': {\n          borderStartStartRadius: tableRadius,\n          borderStartEndRadius: tableRadius,\n          'table > thead > tr:first-child': {\n            '> *:first-child': {\n              borderStartStartRadius: tableRadius\n            },\n            '> *:last-child': {\n              borderStartEndRadius: tableRadius\n            }\n          }\n        },\n        '&-footer': {\n          borderRadius: \"0 0 \".concat(unit(tableRadius), \" \").concat(unit(tableRadius))\n        }\n      }\n    }\n  };\n};\nexport default genRadiusStyle;", "map": {"version": 3, "names": ["unit", "genRadiusStyle", "token", "componentCls", "tableRadius", "concat", "borderRadius", "borderStartStartRadius", "borderStartEndRadius"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/radius.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genRadiusStyle = token => {\n  const {\n    componentCls,\n    tableRadius\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [componentCls]: {\n        // https://github.com/ant-design/ant-design/issues/39115#issuecomment-1362314574\n        [`${componentCls}-title, ${componentCls}-header`]: {\n          borderRadius: `${unit(tableRadius)} ${unit(tableRadius)} 0 0`\n        },\n        [`${componentCls}-title + ${componentCls}-container`]: {\n          borderStartStartRadius: 0,\n          borderStartEndRadius: 0,\n          // https://github.com/ant-design/ant-design/issues/41975\n          [`${componentCls}-header, table`]: {\n            borderRadius: 0\n          },\n          'table > thead > tr:first-child': {\n            'th:first-child, th:last-child, td:first-child, td:last-child': {\n              borderRadius: 0\n            }\n          }\n        },\n        '&-container': {\n          borderStartStartRadius: tableRadius,\n          borderStartEndRadius: tableRadius,\n          'table > thead > tr:first-child': {\n            '> *:first-child': {\n              borderStartStartRadius: tableRadius\n            },\n            '> *:last-child': {\n              borderStartEndRadius: tableRadius\n            }\n          }\n        },\n        '&-footer': {\n          borderRadius: `0 0 ${unit(tableRadius)} ${unit(tableRadius)}`\n        }\n      }\n    }\n  };\n};\nexport default genRadiusStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,IAAAG,MAAA,CAAIF,YAAY,gBAAa;MAC3B,CAACA,YAAY,GAAG;QACd;QACA,IAAAE,MAAA,CAAIF,YAAY,cAAAE,MAAA,CAAWF,YAAY,eAAY;UACjDG,YAAY,KAAAD,MAAA,CAAKL,IAAI,CAACI,WAAW,CAAC,OAAAC,MAAA,CAAIL,IAAI,CAACI,WAAW,CAAC;QACzD,CAAC;QACD,IAAAC,MAAA,CAAIF,YAAY,eAAAE,MAAA,CAAYF,YAAY,kBAAe;UACrDI,sBAAsB,EAAE,CAAC;UACzBC,oBAAoB,EAAE,CAAC;UACvB;UACA,IAAAH,MAAA,CAAIF,YAAY,sBAAmB;YACjCG,YAAY,EAAE;UAChB,CAAC;UACD,gCAAgC,EAAE;YAChC,8DAA8D,EAAE;cAC9DA,YAAY,EAAE;YAChB;UACF;QACF,CAAC;QACD,aAAa,EAAE;UACbC,sBAAsB,EAAEH,WAAW;UACnCI,oBAAoB,EAAEJ,WAAW;UACjC,gCAAgC,EAAE;YAChC,iBAAiB,EAAE;cACjBG,sBAAsB,EAAEH;YAC1B,CAAC;YACD,gBAAgB,EAAE;cAChBI,oBAAoB,EAAEJ;YACxB;UACF;QACF,CAAC;QACD,UAAU,EAAE;UACVE,YAAY,SAAAD,MAAA,CAASL,IAAI,CAACI,WAAW,CAAC,OAAAC,MAAA,CAAIL,IAAI,CAACI,WAAW,CAAC;QAC7D;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}