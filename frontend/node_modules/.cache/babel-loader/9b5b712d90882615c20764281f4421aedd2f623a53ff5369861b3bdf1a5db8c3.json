{"ast": null, "code": "/*!\n * @antv/g-canvas\n * @description A renderer implemented by Canvas 2D API\n * @version 2.0.44\n * @date 5/30/2025, 2:57:07 AM\n * <AUTHOR>\n * @docs https://g.antv.antgroup.com/\n */\nimport _createClass from '@babel/runtime/helpers/createClass';\nimport _classCallCheck from '@babel/runtime/helpers/classCallCheck';\nimport _callSuper from '@babel/runtime/helpers/callSuper';\nimport _inherits from '@babel/runtime/helpers/inherits';\nimport { setDOMSize, RenderReason, AbstractRendererPlugin, AbstractRenderer } from '@antv/g-lite';\nimport * as CanvasPathGenerator from '@antv/g-plugin-canvas-path-generator';\nexport { CanvasPathGenerator };\nimport * as CanvasPicker from '@antv/g-plugin-canvas-picker';\nexport { CanvasPicker };\nimport * as CanvasRenderer from '@antv/g-plugin-canvas-renderer';\nexport { CanvasRenderer };\nimport * as DomInteraction from '@antv/g-plugin-dom-interaction';\nexport { DomInteraction };\nimport * as HTMLRenderer from '@antv/g-plugin-html-renderer';\nexport { HTMLRenderer };\nimport * as ImageLoader from '@antv/g-plugin-image-loader';\nexport { ImageLoader };\nimport _regeneratorRuntime from '@babel/runtime/helpers/regeneratorRuntime';\nimport _asyncToGenerator from '@babel/runtime/helpers/asyncToGenerator';\nimport { isString } from '@antv/util';\nvar Canvas2DContextService = /*#__PURE__*/function () {\n  function Canvas2DContextService(context) {\n    _classCallCheck(this, Canvas2DContextService);\n    this.renderingContext = context.renderingContext;\n    this.canvasConfig = context.config;\n  }\n  return _createClass(Canvas2DContextService, [{\n    key: \"init\",\n    value: function init() {\n      var _this$canvasConfig = this.canvasConfig,\n        container = _this$canvasConfig.container,\n        canvas = _this$canvasConfig.canvas;\n      if (canvas) {\n        this.$canvas = canvas;\n        if (container && canvas.parentElement !== container) {\n          container.appendChild(canvas);\n        }\n        this.$container = canvas.parentElement;\n        this.canvasConfig.container = this.$container;\n      } else if (container) {\n        // create container\n        this.$container = isString(container) ? document.getElementById(container) : container;\n        if (this.$container) {\n          // create canvas\n          var $canvas = document.createElement('canvas');\n          this.$container.appendChild($canvas);\n          if (!this.$container.style.position) {\n            this.$container.style.position = 'relative';\n          }\n          this.$canvas = $canvas;\n        }\n      }\n      this.context = this.$canvas.getContext('2d');\n      this.resize(this.canvasConfig.width, this.canvasConfig.height);\n    }\n  }, {\n    key: \"getContext\",\n    value: function getContext() {\n      return this.context;\n    }\n  }, {\n    key: \"getDomElement\",\n    value: function getDomElement() {\n      return this.$canvas;\n    }\n  }, {\n    key: \"getDPR\",\n    value: function getDPR() {\n      return this.dpr;\n    }\n  }, {\n    key: \"getBoundingClientRect\",\n    value: function getBoundingClientRect() {\n      if (this.$canvas.getBoundingClientRect) {\n        return this.$canvas.getBoundingClientRect();\n      }\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      // @ts-ignore\n      if (this.$container && this.$canvas && this.$canvas.parentNode) {\n        // destroy context\n        // @ts-ignore\n        this.$container.removeChild(this.$canvas);\n      }\n    }\n  }, {\n    key: \"resize\",\n    value: function resize(width, height) {\n      var dpr = this.canvasConfig.devicePixelRatio;\n      this.dpr = dpr;\n      if (this.$canvas) {\n        // set canvas width & height\n        this.$canvas.width = this.dpr * width;\n        this.$canvas.height = this.dpr * height;\n\n        // set CSS style width & height\n        setDOMSize(this.$canvas, width, height);\n\n        // const dpr = this.getDPR();\n        // scale all drawing operations by the dpr\n        // @see https://www.html5rocks.com/en/tutorials/canvas/hidpi/\n        // this.context.scale(dpr, dpr);\n      }\n      this.renderingContext.renderReasons.add(RenderReason.CAMERA_CHANGED);\n    }\n  }, {\n    key: \"applyCursorStyle\",\n    value: function applyCursorStyle(cursor) {\n      if (this.$container && this.$container.style) {\n        this.$container.style.cursor = cursor;\n      }\n    }\n  }, {\n    key: \"toDataURL\",\n    value: function () {\n      var _toDataURL = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var options,\n          type,\n          encoderOptions,\n          _args = arguments;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              options = _args.length > 0 && _args[0] !== undefined ? _args[0] : {};\n              type = options.type, encoderOptions = options.encoderOptions;\n              return _context.abrupt(\"return\", this.context.canvas.toDataURL(type, encoderOptions));\n            case 3:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function toDataURL() {\n        return _toDataURL.apply(this, arguments);\n      }\n      return toDataURL;\n    }()\n  }]);\n}();\nvar ContextRegisterPlugin = /*#__PURE__*/function (_AbstractRendererPlug) {\n  function ContextRegisterPlugin() {\n    var _this;\n    _classCallCheck(this, ContextRegisterPlugin);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, ContextRegisterPlugin, [].concat(args));\n    _this.name = 'canvas-context-register';\n    return _this;\n  }\n  _inherits(ContextRegisterPlugin, _AbstractRendererPlug);\n  return _createClass(ContextRegisterPlugin, [{\n    key: \"init\",\n    value: function init() {\n      this.context.ContextService = Canvas2DContextService;\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      delete this.context.ContextService;\n    }\n  }]);\n}(AbstractRendererPlugin);\nvar Renderer = /*#__PURE__*/function (_AbstractRenderer) {\n  function Renderer(config) {\n    var _this;\n    _classCallCheck(this, Renderer);\n    _this = _callSuper(this, Renderer, [config]);\n\n    // register Canvas2DContext\n    _this.registerPlugin(new ContextRegisterPlugin());\n    _this.registerPlugin(new ImageLoader.Plugin());\n    _this.registerPlugin(new CanvasPathGenerator.Plugin());\n    // enable rendering with Canvas2D API\n    _this.registerPlugin(new CanvasRenderer.Plugin());\n    _this.registerPlugin(new DomInteraction.Plugin());\n    // enable picking with Canvas2D API\n    _this.registerPlugin(new CanvasPicker.Plugin());\n\n    // render HTML component\n    _this.registerPlugin(new HTMLRenderer.Plugin());\n    return _this;\n  }\n  _inherits(Renderer, _AbstractRenderer);\n  return _createClass(Renderer);\n}(AbstractRenderer);\nexport { Renderer };", "map": {"version": 3, "names": ["Canvas2DContextService", "context", "_classCallCheck", "renderingContext", "canvasConfig", "config", "_createClass", "key", "value", "init", "_this$canvasConfig", "container", "canvas", "$canvas", "parentElement", "append<PERSON><PERSON><PERSON>", "$container", "isString", "document", "getElementById", "createElement", "style", "position", "getContext", "resize", "width", "height", "getDomElement", "getDPR", "dpr", "getBoundingClientRect", "destroy", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "devicePixelRatio", "setDOMSize", "renderReasons", "add", "RenderReason", "CAMERA_CHANGED", "applyCursorStyle", "cursor", "_toDataURL", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "options", "type", "encoderOptions", "_args", "arguments", "wrap", "_callee$", "_context", "prev", "next", "length", "undefined", "abrupt", "toDataURL", "stop", "apply", "ContextRegisterPlugin", "_AbstractRendererPlug", "_this", "_len", "args", "Array", "_key", "_callSuper", "concat", "name", "_inherits", "ContextService", "AbstractRendererPlugin", "<PERSON><PERSON><PERSON>", "_Abstract<PERSON><PERSON><PERSON>", "registerPlugin", "ImageLoader", "Plugin", "CanvasPathGenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DomInteraction", "CanvasPicker", "HTMLR<PERSON><PERSON>", "Abstract<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g-canvas/src/Canvas2DContextService.ts", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g-canvas/src/ContextRegisterPlugin.ts", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g-canvas/src/index.ts"], "sourcesContent": ["import type {\n  RenderingContext,\n  CanvasContext,\n  CanvasLike,\n  DataURLOptions,\n  GlobalRuntime,\n  CanvasConfig,\n  ContextService,\n} from '@antv/g-lite';\nimport { RenderReason, setDOMSize } from '@antv/g-lite';\nimport { isString } from '@antv/util';\n\nexport class Canvas2DContextService\n  implements ContextService<CanvasRenderingContext2D>\n{\n  private $container: HTMLElement | null;\n  private $canvas: CanvasLike | null;\n  private dpr: number;\n  private context:\n    | CanvasRenderingContext2D\n    | OffscreenCanvasRenderingContext2D\n    | null;\n  private canvasConfig: Partial<CanvasConfig>;\n  private renderingContext: RenderingContext;\n\n  constructor(context: GlobalRuntime & CanvasContext) {\n    this.renderingContext = context.renderingContext;\n    this.canvasConfig = context.config;\n  }\n\n  init() {\n    const { container, canvas } = this.canvasConfig;\n\n    if (canvas) {\n      this.$canvas = canvas;\n\n      if (\n        container &&\n        (canvas as HTMLCanvasElement).parentElement !== container\n      ) {\n        (container as HTMLElement).appendChild(canvas as HTMLCanvasElement);\n      }\n\n      this.$container = (canvas as HTMLCanvasElement).parentElement;\n      this.canvasConfig.container = this.$container;\n    } else if (container) {\n      // create container\n      this.$container = isString(container)\n        ? document.getElementById(container)\n        : container;\n      if (this.$container) {\n        // create canvas\n        const $canvas = document.createElement('canvas');\n\n        this.$container.appendChild($canvas);\n        if (!this.$container.style.position) {\n          this.$container.style.position = 'relative';\n        }\n        this.$canvas = $canvas;\n      }\n    }\n\n    this.context = this.$canvas.getContext('2d');\n    this.resize(this.canvasConfig.width, this.canvasConfig.height);\n  }\n\n  getContext() {\n    return this.context as CanvasRenderingContext2D;\n  }\n\n  getDomElement() {\n    return this.$canvas;\n  }\n\n  getDPR() {\n    return this.dpr;\n  }\n\n  getBoundingClientRect() {\n    if ((this.$canvas as HTMLCanvasElement).getBoundingClientRect) {\n      return (this.$canvas as HTMLCanvasElement).getBoundingClientRect();\n    }\n  }\n\n  destroy() {\n    // @ts-ignore\n    if (this.$container && this.$canvas && this.$canvas.parentNode) {\n      // destroy context\n      // @ts-ignore\n      this.$container.removeChild(this.$canvas);\n    }\n  }\n\n  resize(width: number, height: number) {\n    const { devicePixelRatio: dpr } = this.canvasConfig;\n    this.dpr = dpr;\n\n    if (this.$canvas) {\n      // set canvas width & height\n      this.$canvas.width = this.dpr * width;\n      this.$canvas.height = this.dpr * height;\n\n      // set CSS style width & height\n      setDOMSize(this.$canvas, width, height);\n\n      // const dpr = this.getDPR();\n      // scale all drawing operations by the dpr\n      // @see https://www.html5rocks.com/en/tutorials/canvas/hidpi/\n      // this.context.scale(dpr, dpr);\n    }\n\n    this.renderingContext.renderReasons.add(RenderReason.CAMERA_CHANGED);\n  }\n\n  applyCursorStyle(cursor: string) {\n    if (this.$container && this.$container.style) {\n      this.$container.style.cursor = cursor;\n    }\n  }\n\n  async toDataURL(options: Partial<DataURLOptions> = {}) {\n    const { type, encoderOptions } = options;\n    return (this.context.canvas as HTMLCanvasElement).toDataURL(\n      type,\n      encoderOptions,\n    );\n  }\n}\n", "import { AbstractRendererPlugin } from '@antv/g-lite';\nimport { Canvas2DContextService } from './Canvas2DContextService';\n\nexport class ContextRegisterPlugin extends AbstractRendererPlugin {\n  name = 'canvas-context-register';\n  init(): void {\n    this.context.ContextService = Canvas2DContextService;\n  }\n  destroy(): void {\n    delete this.context.ContextService;\n  }\n}\n", "import type { RendererConfig } from '@antv/g-lite';\nimport { AbstractRenderer } from '@antv/g-lite';\nimport * as CanvasPathGenerator from '@antv/g-plugin-canvas-path-generator';\nimport * as CanvasPicker from '@antv/g-plugin-canvas-picker';\nimport * as CanvasRenderer from '@antv/g-plugin-canvas-renderer';\nimport * as DomInteraction from '@antv/g-plugin-dom-interaction';\nimport * as HTMLRenderer from '@antv/g-plugin-html-renderer';\nimport * as ImageLoader from '@antv/g-plugin-image-loader';\nimport { ContextRegisterPlugin } from './ContextRegisterPlugin';\n\nexport {\n  CanvasPathGenerator,\n  CanvasPicker,\n  CanvasRenderer,\n  DomInteraction,\n  HTMLRenderer,\n  ImageLoader,\n};\n\nexport class Renderer extends AbstractRenderer {\n  constructor(config?: Partial<RendererConfig>) {\n    super(config);\n\n    // register Canvas2DContext\n    this.registerPlugin(new ContextRegisterPlugin());\n    this.registerPlugin(new ImageLoader.Plugin());\n    this.registerPlugin(new CanvasPathGenerator.Plugin());\n    // enable rendering with Canvas2D API\n    this.registerPlugin(new CanvasRenderer.Plugin());\n    this.registerPlugin(new DomInteraction.Plugin());\n    // enable picking with Canvas2D API\n    this.registerPlugin(new CanvasPicker.Plugin());\n\n    // render HTML component\n    this.registerPlugin(new HTMLRenderer.Plugin());\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAaA,sBAAsB;EAajC,SAAAA,uBAAYC,OAAsC,EAAE;IAAAC,eAAA,OAAAF,sBAAA;IAClD,IAAI,CAACG,gBAAgB,GAAGF,OAAO,CAACE,gBAAgB;IAChD,IAAI,CAACC,YAAY,GAAGH,OAAO,CAACI,MAAM;EACpC;EAAC,OAAAC,YAAA,CAAAN,sBAAA;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAAC,IAAIA,CAAA,EAAG;MACL,IAAAC,kBAAA,GAA8B,IAAI,CAACN,YAAY;QAAvCO,SAAS,GAAAD,kBAAA,CAATC,SAAS;QAAEC,MAAM,GAAAF,kBAAA,CAANE,MAAM;MAEzB,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,OAAO,GAAGD,MAAM;QAErB,IACED,SAAS,IACRC,MAAM,CAAuBE,aAAa,KAAKH,SAAS,EACzD;UACCA,SAAS,CAAiBI,WAAW,CAACH,MAA2B,CAAC;QACrE;QAEA,IAAI,CAACI,UAAU,GAAIJ,MAAM,CAAuBE,aAAa;QAC7D,IAAI,CAACV,YAAY,CAACO,SAAS,GAAG,IAAI,CAACK,UAAU;OAC9C,MAAM,IAAIL,SAAS,EAAE;QACpB;QACA,IAAI,CAACK,UAAU,GAAGC,QAAQ,CAACN,SAAS,CAAC,GACjCO,QAAQ,CAACC,cAAc,CAACR,SAAS,CAAC,GAClCA,SAAS;QACb,IAAI,IAAI,CAACK,UAAU,EAAE;UACnB;UACA,IAAMH,OAAO,GAAGK,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;UAEhD,IAAI,CAACJ,UAAU,CAACD,WAAW,CAACF,OAAO,CAAC;UACpC,IAAI,CAAC,IAAI,CAACG,UAAU,CAACK,KAAK,CAACC,QAAQ,EAAE;YACnC,IAAI,CAACN,UAAU,CAACK,KAAK,CAACC,QAAQ,GAAG,UAAU;UAC7C;UACA,IAAI,CAACT,OAAO,GAAGA,OAAO;QACxB;MACF;MAEA,IAAI,CAACZ,OAAO,GAAG,IAAI,CAACY,OAAO,CAACU,UAAU,CAAC,IAAI,CAAC;MAC5C,IAAI,CAACC,MAAM,CAAC,IAAI,CAACpB,YAAY,CAACqB,KAAK,EAAE,IAAI,CAACrB,YAAY,CAACsB,MAAM,CAAC;IAChE;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAe,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACtB,OAAO;IACrB;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAmB,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACd,OAAO;IACrB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAoB,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAACC,GAAG;IACjB;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAAsB,qBAAqBA,CAAA,EAAG;MACtB,IAAK,IAAI,CAACjB,OAAO,CAAuBiB,qBAAqB,EAAE;QAC7D,OAAQ,IAAI,CAACjB,OAAO,CAAuBiB,qBAAqB,EAAE;MACpE;IACF;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAED,SAAAuB,OAAOA,CAAA,EAAG;MACR;MACA,IAAI,IAAI,CAACf,UAAU,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACA,OAAO,CAACmB,UAAU,EAAE;QAC9D;QACA;QACA,IAAI,CAAChB,UAAU,CAACiB,WAAW,CAAC,IAAI,CAACpB,OAAO,CAAC;MAC3C;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAgB,MAAMA,CAACC,KAAa,EAAEC,MAAc,EAAE;MACpC,IAA0BG,GAAG,GAAK,IAAI,CAACzB,YAAY,CAA3C8B,gBAAgB;MACxB,IAAI,CAACL,GAAG,GAAGA,GAAG;MAEd,IAAI,IAAI,CAAChB,OAAO,EAAE;QAChB;QACA,IAAI,CAACA,OAAO,CAACY,KAAK,GAAG,IAAI,CAACI,GAAG,GAAGJ,KAAK;QACrC,IAAI,CAACZ,OAAO,CAACa,MAAM,GAAG,IAAI,CAACG,GAAG,GAAGH,MAAM;;QAEvC;QACAS,UAAU,CAAC,IAAI,CAACtB,OAAO,EAAEY,KAAK,EAAEC,MAAM,CAAC;;QAEvC;QACA;QACA;QACA;MACF;MAEA,IAAI,CAACvB,gBAAgB,CAACiC,aAAa,CAACC,GAAG,CAACC,YAAY,CAACC,cAAc,CAAC;IACtE;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAED,SAAAgC,gBAAgBA,CAACC,MAAc,EAAE;MAC/B,IAAI,IAAI,CAACzB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACK,KAAK,EAAE;QAC5C,IAAI,CAACL,UAAU,CAACK,KAAK,CAACoB,MAAM,GAAGA,MAAM;MACvC;IACF;EAAC;IAAAlC,GAAA;IAAAC,KAAA;MAAA,IAAAkC,UAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAED,SAAAC,QAAA;QAAA,IAAAC,OAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,KAAA,GAAAC,SAAA;QAAA,OAAAP,mBAAA,GAAAQ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAgBT,OAAgC,GAAAG,KAAA,CAAAO,MAAA,QAAAP,KAAA,QAAAQ,SAAA,GAAAR,KAAA,CAAG,OAAE;cAC3CF,IAAI,GAAqBD,OAAO,CAAhCC,IAAI,EAAEC,cAAc,GAAKF,OAAO,CAA1BE,cAAc;cAAA,OAAAK,QAAA,CAAAK,MAAA,WACpB,IAAI,CAAC1D,OAAO,CAACW,MAAM,CAAuBgD,SAAS,CACzDZ,IAAI,EACJC,cACF,CAAC;YAAA;YAAA;cAAA,OAAAK,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAf,OAAA;OACF;MAAA,SANKc,SAASA,CAAA;QAAA,OAAAlB,UAAA,CAAAoB,KAAA,OAAAX,SAAA;MAAA;MAAA,OAATS,SAAS;IAAA;EAAA;AAAA;ACrHJ,IAAAG,qBAAqB,0BAAAC,qBAAA;EAAA,SAAAD,sBAAA;IAAA,IAAAE,KAAA;IAAA/D,eAAA,OAAA6D,qBAAA;IAAA,SAAAG,IAAA,GAAAf,SAAA,CAAAM,MAAA,EAAAU,IAAA,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAlB,SAAA,CAAAkB,IAAA;IAAA;IAAAJ,KAAA,GAAAK,UAAA,OAAAP,qBAAA,KAAAQ,MAAA,CAAAJ,IAAA;IAAAF,KAAA,CAChCO,IAAI,GAAG,yBAAyB;IAAA,OAAAP,KAAA;EAAA;EAAAQ,SAAA,CAAAV,qBAAA,EAAAC,qBAAA;EAAA,OAAA1D,YAAA,CAAAyD,qBAAA;IAAAxD,GAAA;IAAAC,KAAA,EAChC,SAAAC,IAAIA,CAAA,EAAS;MACX,IAAI,CAACR,OAAO,CAACyE,cAAc,GAAG1E,sBAAsB;IACtD;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAuB,OAAOA,CAAA,EAAS;MACd,OAAO,IAAI,CAAC9B,OAAO,CAACyE,cAAc;IACpC;EAAC;AAAA,EAPwCC,sBAAsB;ACgBpD,IAAAC,QAAQ,0BAAAC,iBAAA;EACnB,SAAAD,SAAYvE,MAAgC,EAAE;IAAA,IAAA4D,KAAA;IAAA/D,eAAA,OAAA0E,QAAA;IAC5CX,KAAA,GAAAK,UAAA,OAAAM,QAAA,GAAMvE,MAAM;;IAEZ;IACA4D,KAAA,CAAKa,cAAc,CAAC,IAAIf,qBAAqB,EAAE,CAAC;IAChDE,KAAA,CAAKa,cAAc,CAAC,IAAIC,WAAW,CAACC,MAAM,EAAE,CAAC;IAC7Cf,KAAA,CAAKa,cAAc,CAAC,IAAIG,mBAAmB,CAACD,MAAM,EAAE,CAAC;IACrD;IACAf,KAAA,CAAKa,cAAc,CAAC,IAAII,cAAc,CAACF,MAAM,EAAE,CAAC;IAChDf,KAAA,CAAKa,cAAc,CAAC,IAAIK,cAAc,CAACH,MAAM,EAAE,CAAC;IAChD;IACAf,KAAA,CAAKa,cAAc,CAAC,IAAIM,YAAY,CAACJ,MAAM,EAAE,CAAC;;IAE9C;IACAf,KAAA,CAAKa,cAAc,CAAC,IAAIO,YAAY,CAACL,MAAM,EAAE,CAAC;IAAC,OAAAf,KAAA;EACjD;EAACQ,SAAA,CAAAG,QAAA,EAAAC,iBAAA;EAAA,OAAAvE,YAAA,CAAAsE,QAAA;AAAA,EAhB2BU,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}