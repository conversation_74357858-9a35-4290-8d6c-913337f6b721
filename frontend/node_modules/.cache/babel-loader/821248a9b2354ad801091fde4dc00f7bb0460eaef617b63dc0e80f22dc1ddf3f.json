{"ast": null, "code": "import { maxIndex, minIndex } from '@antv/vendor/d3-array';\nimport { columnOf } from './utils/helper';\nimport { createGroups } from './utils/order';\nfunction first(I, V) {\n  return [I[0]];\n}\nfunction last(I, V) {\n  const i = I.length - 1;\n  return [I[i]];\n}\nfunction max(I, V) {\n  const i = maxIndex(I, i => V[i]);\n  return [I[i]];\n}\nfunction min(I, V) {\n  const i = minIndex(I, i => V[i]);\n  return [I[i]];\n}\nfunction normalizeSelector(selector) {\n  if (typeof selector === 'function') return selector;\n  const registry = {\n    first,\n    last,\n    max,\n    min\n  };\n  return registry[selector] || first;\n}\n/**\n * The select transform groups marks with specified channels, and\n * filter index by specified selector for each series, say to\n * pull a single or multiple values out of each series.\n */\nexport const Select = (options = {}) => {\n  const {\n    groupBy = 'series',\n    channel,\n    selector\n  } = options;\n  return (I, mark) => {\n    const {\n      encode\n    } = mark;\n    const groups = createGroups(groupBy, I, mark);\n    const [V] = columnOf(encode, channel);\n    const selectFunction = normalizeSelector(selector);\n    return [groups.flatMap(GI => selectFunction(GI, V)), mark];\n  };\n};\nSelect.props = {};", "map": {"version": 3, "names": ["maxIndex", "minIndex", "columnOf", "createGroups", "first", "I", "V", "last", "i", "length", "max", "min", "normalizeSelector", "selector", "registry", "Select", "options", "groupBy", "channel", "mark", "encode", "groups", "selectFunction", "flatMap", "GI", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/select.ts"], "sourcesContent": ["import { maxIndex, minIndex } from '@antv/vendor/d3-array';\nimport { TransformComponent as TC } from '../runtime';\nimport { SelectTransform, Selector } from '../spec';\nimport { columnOf } from './utils/helper';\nimport { createGroups } from './utils/order';\n\nexport type SelectOptions = Omit<SelectTransform, 'type'>;\n\ntype SelectorFunction = (I: number[], V: number[]) => number[];\n\nfunction first(I: number[], V: number[]): number[] {\n  return [I[0]];\n}\n\nfunction last(I: number[], V: number[]): number[] {\n  const i = I.length - 1;\n  return [I[i]];\n}\n\nfunction max(I: number[], V: number[]): number[] {\n  const i = maxIndex(I, (i) => V[i]);\n  return [I[i]];\n}\n\nfunction min(I: number[], V: number[]): number[] {\n  const i = minIndex(I, (i) => V[i]);\n  return [I[i]];\n}\n\nfunction normalizeSelector(selector: Selector): SelectorFunction {\n  if (typeof selector === 'function') return selector;\n  const registry = { first, last, max, min };\n  return registry[selector] || first;\n}\n\n/**\n * The select transform groups marks with specified channels, and\n * filter index by specified selector for each series, say to\n * pull a single or multiple values out of each series.\n */\nexport const Select: TC<SelectOptions> = (options = {}) => {\n  const { groupBy = 'series', channel, selector } = options;\n  return (I, mark) => {\n    const { encode } = mark;\n    const groups = createGroups(groupBy, I, mark);\n    const [V] = columnOf(encode, channel);\n    const selectFunction = normalizeSelector(selector);\n    return [groups.flatMap((GI) => selectFunction(GI, V as number[])), mark];\n  };\n};\n\nSelect.props = {};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,uBAAuB;AAG1D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,YAAY,QAAQ,eAAe;AAM5C,SAASC,KAAKA,CAACC,CAAW,EAAEC,CAAW;EACrC,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;AAEA,SAASE,IAAIA,CAACF,CAAW,EAAEC,CAAW;EACpC,MAAME,CAAC,GAAGH,CAAC,CAACI,MAAM,GAAG,CAAC;EACtB,OAAO,CAACJ,CAAC,CAACG,CAAC,CAAC,CAAC;AACf;AAEA,SAASE,GAAGA,CAACL,CAAW,EAAEC,CAAW;EACnC,MAAME,CAAC,GAAGR,QAAQ,CAACK,CAAC,EAAGG,CAAC,IAAKF,CAAC,CAACE,CAAC,CAAC,CAAC;EAClC,OAAO,CAACH,CAAC,CAACG,CAAC,CAAC,CAAC;AACf;AAEA,SAASG,GAAGA,CAACN,CAAW,EAAEC,CAAW;EACnC,MAAME,CAAC,GAAGP,QAAQ,CAACI,CAAC,EAAGG,CAAC,IAAKF,CAAC,CAACE,CAAC,CAAC,CAAC;EAClC,OAAO,CAACH,CAAC,CAACG,CAAC,CAAC,CAAC;AACf;AAEA,SAASI,iBAAiBA,CAACC,QAAkB;EAC3C,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE,OAAOA,QAAQ;EACnD,MAAMC,QAAQ,GAAG;IAAEV,KAAK;IAAEG,IAAI;IAAEG,GAAG;IAAEC;EAAG,CAAE;EAC1C,OAAOG,QAAQ,CAACD,QAAQ,CAAC,IAAIT,KAAK;AACpC;AAEA;;;;;AAKA,OAAO,MAAMW,MAAM,GAAsBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACxD,MAAM;IAAEC,OAAO,GAAG,QAAQ;IAAEC,OAAO;IAAEL;EAAQ,CAAE,GAAGG,OAAO;EACzD,OAAO,CAACX,CAAC,EAAEc,IAAI,KAAI;IACjB,MAAM;MAAEC;IAAM,CAAE,GAAGD,IAAI;IACvB,MAAME,MAAM,GAAGlB,YAAY,CAACc,OAAO,EAAEZ,CAAC,EAAEc,IAAI,CAAC;IAC7C,MAAM,CAACb,CAAC,CAAC,GAAGJ,QAAQ,CAACkB,MAAM,EAAEF,OAAO,CAAC;IACrC,MAAMI,cAAc,GAAGV,iBAAiB,CAACC,QAAQ,CAAC;IAClD,OAAO,CAACQ,MAAM,CAACE,OAAO,CAAEC,EAAE,IAAKF,cAAc,CAACE,EAAE,EAAElB,CAAa,CAAC,CAAC,EAAEa,IAAI,CAAC;EAC1E,CAAC;AACH,CAAC;AAEDJ,MAAM,CAACU,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}