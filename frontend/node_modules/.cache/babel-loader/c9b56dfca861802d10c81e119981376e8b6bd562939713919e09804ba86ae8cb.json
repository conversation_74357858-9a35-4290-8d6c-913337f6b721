{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"../lodash\");\nmodule.exports = resolveConflicts;\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = mappedEntries[entry.v] = {\n      indegree: 0,\n      \"in\": [],\n      out: [],\n      vs: [entry.v],\n      i: i\n    };\n    if (!_.isUndefined(entry.barycenter)) {\n      tmp.barycenter = entry.barycenter;\n      tmp.weight = entry.weight;\n    }\n  });\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    return !entry.indegree;\n  });\n  return doResolveConflicts(sourceSet);\n}\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (_.isUndefined(uEntry.barycenter) || _.isUndefined(vEntry.barycenter) || uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry[\"in\"].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry[\"in\"].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n  return _.map(_.filter(entries, function (entry) {\n    return !entry.merged;\n  }), function (entry) {\n    return _.pick(entry, [\"vs\", \"i\", \"barycenter\", \"weight\"]);\n  });\n}\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "resolveConflicts", "entries", "cg", "mappedEntries", "for<PERSON>ach", "entry", "i", "tmp", "v", "indegree", "out", "vs", "isUndefined", "barycenter", "weight", "edges", "e", "entryV", "entryW", "w", "push", "sourceSet", "filter", "doResolveConflicts", "handleIn", "vEntry", "uEntry", "merged", "mergeEntries", "handleOut", "wEntry", "length", "pop", "reverse", "map", "pick", "target", "source", "sum", "concat", "Math", "min"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/dagre/lib/order/resolve-conflicts.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = resolveConflicts;\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function(entry, i) {\n    var tmp = mappedEntries[entry.v] = {\n      indegree: 0,\n      \"in\": [],\n      out: [],\n      vs: [entry.v],\n      i: i\n    };\n    if (!_.isUndefined(entry.barycenter)) {\n      tmp.barycenter = entry.barycenter;\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function(e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function(entry) {\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function(uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (_.isUndefined(uEntry.barycenter) ||\n          _.isUndefined(vEntry.barycenter) ||\n          uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function(wEntry) {\n      wEntry[\"in\"].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry[\"in\"].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(_.filter(entries, function(entry) { return !entry.merged; }),\n    function(entry) {\n      return _.pick(entry, [\"vs\", \"i\", \"barycenter\", \"weight\"]);\n    });\n\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,gBAAgB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEC,EAAE,EAAE;EACrC,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtBP,CAAC,CAACQ,OAAO,CAACH,OAAO,EAAE,UAASI,KAAK,EAAEC,CAAC,EAAE;IACpC,IAAIC,GAAG,GAAGJ,aAAa,CAACE,KAAK,CAACG,CAAC,CAAC,GAAG;MACjCC,QAAQ,EAAE,CAAC;MACX,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,EAAE,EAAE,CAACN,KAAK,CAACG,CAAC,CAAC;MACbF,CAAC,EAAEA;IACL,CAAC;IACD,IAAI,CAACV,CAAC,CAACgB,WAAW,CAACP,KAAK,CAACQ,UAAU,CAAC,EAAE;MACpCN,GAAG,CAACM,UAAU,GAAGR,KAAK,CAACQ,UAAU;MACjCN,GAAG,CAACO,MAAM,GAAGT,KAAK,CAACS,MAAM;IAC3B;EACF,CAAC,CAAC;EAEFlB,CAAC,CAACQ,OAAO,CAACF,EAAE,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAChC,IAAIC,MAAM,GAAGd,aAAa,CAACa,CAAC,CAACR,CAAC,CAAC;IAC/B,IAAIU,MAAM,GAAGf,aAAa,CAACa,CAAC,CAACG,CAAC,CAAC;IAC/B,IAAI,CAACvB,CAAC,CAACgB,WAAW,CAACK,MAAM,CAAC,IAAI,CAACrB,CAAC,CAACgB,WAAW,CAACM,MAAM,CAAC,EAAE;MACpDA,MAAM,CAACT,QAAQ,EAAE;MACjBQ,MAAM,CAACP,GAAG,CAACU,IAAI,CAACjB,aAAa,CAACa,CAAC,CAACG,CAAC,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,IAAIE,SAAS,GAAGzB,CAAC,CAAC0B,MAAM,CAACnB,aAAa,EAAE,UAASE,KAAK,EAAE;IACtD,OAAO,CAACA,KAAK,CAACI,QAAQ;EACxB,CAAC,CAAC;EAEF,OAAOc,kBAAkB,CAACF,SAAS,CAAC;AACtC;AAEA,SAASE,kBAAkBA,CAACF,SAAS,EAAE;EACrC,IAAIpB,OAAO,GAAG,EAAE;EAEhB,SAASuB,QAAQA,CAACC,MAAM,EAAE;IACxB,OAAO,UAASC,MAAM,EAAE;MACtB,IAAIA,MAAM,CAACC,MAAM,EAAE;QACjB;MACF;MACA,IAAI/B,CAAC,CAACgB,WAAW,CAACc,MAAM,CAACb,UAAU,CAAC,IAChCjB,CAAC,CAACgB,WAAW,CAACa,MAAM,CAACZ,UAAU,CAAC,IAChCa,MAAM,CAACb,UAAU,IAAIY,MAAM,CAACZ,UAAU,EAAE;QAC1Ce,YAAY,CAACH,MAAM,EAAEC,MAAM,CAAC;MAC9B;IACF,CAAC;EACH;EAEA,SAASG,SAASA,CAACJ,MAAM,EAAE;IACzB,OAAO,UAASK,MAAM,EAAE;MACtBA,MAAM,CAAC,IAAI,CAAC,CAACV,IAAI,CAACK,MAAM,CAAC;MACzB,IAAI,EAAEK,MAAM,CAACrB,QAAQ,KAAK,CAAC,EAAE;QAC3BY,SAAS,CAACD,IAAI,CAACU,MAAM,CAAC;MACxB;IACF,CAAC;EACH;EAEA,OAAOT,SAAS,CAACU,MAAM,EAAE;IACvB,IAAI1B,KAAK,GAAGgB,SAAS,CAACW,GAAG,CAAC,CAAC;IAC3B/B,OAAO,CAACmB,IAAI,CAACf,KAAK,CAAC;IACnBT,CAAC,CAACQ,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC4B,OAAO,CAAC,CAAC,EAAET,QAAQ,CAACnB,KAAK,CAAC,CAAC;IACjDT,CAAC,CAACQ,OAAO,CAACC,KAAK,CAACK,GAAG,EAAEmB,SAAS,CAACxB,KAAK,CAAC,CAAC;EACxC;EAEA,OAAOT,CAAC,CAACsC,GAAG,CAACtC,CAAC,CAAC0B,MAAM,CAACrB,OAAO,EAAE,UAASI,KAAK,EAAE;IAAE,OAAO,CAACA,KAAK,CAACsB,MAAM;EAAE,CAAC,CAAC,EACvE,UAAStB,KAAK,EAAE;IACd,OAAOT,CAAC,CAACuC,IAAI,CAAC9B,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;EAC3D,CAAC,CAAC;AAEN;AAEA,SAASuB,YAAYA,CAACQ,MAAM,EAAEC,MAAM,EAAE;EACpC,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIxB,MAAM,GAAG,CAAC;EAEd,IAAIsB,MAAM,CAACtB,MAAM,EAAE;IACjBwB,GAAG,IAAIF,MAAM,CAACvB,UAAU,GAAGuB,MAAM,CAACtB,MAAM;IACxCA,MAAM,IAAIsB,MAAM,CAACtB,MAAM;EACzB;EAEA,IAAIuB,MAAM,CAACvB,MAAM,EAAE;IACjBwB,GAAG,IAAID,MAAM,CAACxB,UAAU,GAAGwB,MAAM,CAACvB,MAAM;IACxCA,MAAM,IAAIuB,MAAM,CAACvB,MAAM;EACzB;EAEAsB,MAAM,CAACzB,EAAE,GAAG0B,MAAM,CAAC1B,EAAE,CAAC4B,MAAM,CAACH,MAAM,CAACzB,EAAE,CAAC;EACvCyB,MAAM,CAACvB,UAAU,GAAGyB,GAAG,GAAGxB,MAAM;EAChCsB,MAAM,CAACtB,MAAM,GAAGA,MAAM;EACtBsB,MAAM,CAAC9B,CAAC,GAAGkC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAC/B,CAAC,EAAE8B,MAAM,CAAC9B,CAAC,CAAC;EACvC+B,MAAM,CAACV,MAAM,GAAG,IAAI;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}