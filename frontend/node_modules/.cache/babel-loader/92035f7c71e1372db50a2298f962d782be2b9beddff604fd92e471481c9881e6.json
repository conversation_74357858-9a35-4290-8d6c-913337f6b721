{"ast": null, "code": "export const COMBO_KEY = 'combo';\nexport const TREE_KEY = 'tree';", "map": {"version": 3, "names": ["COMBO_KEY", "TREE_KEY"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g6/src/constants/graphlib.ts"], "sourcesContent": ["export const COMBO_KEY = 'combo';\n\nexport const TREE_KEY = 'tree';\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,GAAG,OAAO;AAEhC,OAAO,MAAMC,QAAQ,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}