{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { defined } from '../utils/helper';\nimport { columnOf } from './utils/helper';\nfunction normalizeValue(value) {\n  if (typeof value === 'object') return [value.value, value.ordinal];else return [value, true];\n}\nfunction filterWhenNoElements(mark) {\n  var _a;\n  const {\n    encode\n  } = mark;\n  // keep y-axis\n  const noElementMark = Object.assign(Object.assign({}, mark), {\n    encode: Object.assign(Object.assign({}, mark.encode), {\n      y: Object.assign(Object.assign({}, mark.encode.y), {\n        value: []\n      })\n    })\n  });\n  const targetField = (_a = encode === null || encode === void 0 ? void 0 : encode.color) === null || _a === void 0 ? void 0 : _a.field;\n  if (!encode || !targetField) {\n    return noElementMark;\n  }\n  // 获取color的筛选源\n  let filterObject;\n  for (const [key, v] of Object.entries(encode)) {\n    if ((key === 'x' || key === 'y') && v.field === targetField) {\n      filterObject = Object.assign(Object.assign({}, filterObject), {\n        [key]: Object.assign(Object.assign({}, v), {\n          value: []\n        })\n      });\n    }\n  }\n  if (!filterObject) {\n    return noElementMark;\n  }\n  return Object.assign(Object.assign({}, mark), {\n    encode: Object.assign(Object.assign({}, mark.encode), filterObject)\n  });\n}\n/**\n * The Filter transform filter channels.\n */\nexport const Filter = (options = {}) => {\n  return (I, mark) => {\n    const {\n      encode,\n      data\n    } = mark;\n    const filters = Object.entries(options).map(([key, v]) => {\n      const [V] = columnOf(encode, key);\n      // Skip empty channel.\n      if (!V) return null;\n      const [value, ordinal = true] = normalizeValue(v);\n      if (typeof value === 'function') return i => value(V[i]);\n      if (ordinal) {\n        const expectedValues = Array.isArray(value) ? value : [value];\n        // Skip empty expected values.\n        if (expectedValues.length === 0) return null;\n        return i => expectedValues.includes(V[i]);\n      } else {\n        const [start, end] = value;\n        return i => V[i] >= start && V[i] <= end;\n      }\n    }).filter(defined);\n    // Filter index and channels.\n    const totalFilter = i => filters.every(f => f(i));\n    const FI = I.filter(totalFilter);\n    const newIndex = FI.map((_, i) => i);\n    if (filters.length === 0) {\n      const targetMark = filterWhenNoElements(mark);\n      return [I, targetMark];\n    }\n    const newEncodes = Object.entries(encode).map(([key, encode]) => {\n      return [key, Object.assign(Object.assign({}, encode), {\n        value: newIndex.map(i => encode.value[FI[i]]).filter(v => v !== undefined)\n      })];\n    });\n    return [newIndex, deepMix({}, mark, {\n      encode: Object.fromEntries(newEncodes),\n      // Filter data for tooltip item.\n      data: FI.map(i => data[i])\n    })];\n  };\n};\nFilter.props = {};", "map": {"version": 3, "names": ["deepMix", "defined", "columnOf", "normalizeValue", "value", "ordinal", "filterWhenNoElements", "mark", "encode", "noElementMark", "Object", "assign", "y", "targetField", "_a", "color", "field", "filterObject", "key", "v", "entries", "Filter", "options", "I", "data", "filters", "map", "V", "i", "expectedV<PERSON>ues", "Array", "isArray", "length", "includes", "start", "end", "filter", "totalFilter", "every", "f", "FI", "newIndex", "_", "targetMark", "newEncodes", "undefined", "fromEntries", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/filter.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { G2Mark, TransformComponent as TC } from '../runtime';\nimport { FilterTransform } from '../spec';\nimport { defined } from '../utils/helper';\nimport { columnOf } from './utils/helper';\n\nexport type FilterOptions = Omit<FilterTransform, 'type'>;\n\nfunction normalizeValue(value) {\n  if (typeof value === 'object') return [value.value, value.ordinal];\n  else return [value, true];\n}\n\nfunction filterWhenNoElements(mark: G2Mark) {\n  const { encode } = mark;\n\n  // keep y-axis\n  const noElementMark = {\n    ...mark,\n    encode: {\n      ...mark.encode,\n      y: {\n        ...mark.encode.y,\n        value: [],\n      },\n    },\n  };\n\n  const targetField = encode?.color?.field;\n  if (!encode || !targetField) {\n    return noElementMark;\n  }\n\n  // 获取color的筛选源\n  let filterObject;\n\n  for (const [key, v] of Object.entries(encode)) {\n    if ((key === 'x' || key === 'y') && v.field === targetField) {\n      filterObject = {\n        ...filterObject,\n        [key]: {\n          ...v,\n          value: [],\n        },\n      };\n    }\n  }\n\n  if (!filterObject) {\n    return noElementMark;\n  }\n\n  return {\n    ...mark,\n    encode: {\n      ...mark.encode,\n      ...filterObject,\n    },\n  };\n}\n\n/**\n * The Filter transform filter channels.\n */\nexport const Filter: TC<FilterOptions> = (options = {}) => {\n  return (I, mark) => {\n    const { encode, data } = mark;\n    const filters = Object.entries(options)\n      .map(([key, v]) => {\n        const [V] = columnOf(encode, key);\n        // Skip empty channel.\n        if (!V) return null;\n        const [value, ordinal = true] = normalizeValue(v);\n        if (typeof value === 'function') return (i) => value(V[i]);\n        if (ordinal) {\n          const expectedValues = Array.isArray(value) ? value : [value];\n          // Skip empty expected values.\n          if (expectedValues.length === 0) return null;\n          return (i) => expectedValues.includes(V[i]);\n        } else {\n          const [start, end] = value;\n          return (i) => V[i] >= start && V[i] <= end;\n        }\n      })\n      .filter(defined);\n    // Filter index and channels.\n    const totalFilter = (i) => filters.every((f) => f(i));\n    const FI = I.filter(totalFilter);\n    const newIndex = FI.map((_, i) => i);\n\n    if (filters.length === 0) {\n      const targetMark = filterWhenNoElements(mark);\n      return [I, targetMark];\n    }\n\n    const newEncodes = Object.entries(encode).map(([key, encode]) => {\n      return [\n        key,\n        {\n          ...encode,\n          value: newIndex\n            .map((i) => encode.value[FI[i]])\n            .filter((v) => v !== undefined),\n        },\n      ];\n    });\n    return [\n      newIndex,\n      deepMix({}, mark, {\n        encode: Object.fromEntries(newEncodes),\n        // Filter data for tooltip item.\n        data: FI.map((i) => data[i]),\n      }),\n    ];\n  };\n};\n\nFilter.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAGpC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,QAAQ,QAAQ,gBAAgB;AAIzC,SAASC,cAAcA,CAACC,KAAK;EAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,CAACA,KAAK,CAACA,KAAK,EAAEA,KAAK,CAACC,OAAO,CAAC,CAAC,KAC9D,OAAO,CAACD,KAAK,EAAE,IAAI,CAAC;AAC3B;AAEA,SAASE,oBAAoBA,CAACC,IAAY;;EACxC,MAAM;IAAEC;EAAM,CAAE,GAAGD,IAAI;EAEvB;EACA,MAAME,aAAa,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACdJ,IAAI;IACPC,MAAM,EAAAE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACDJ,IAAI,CAACC,MAAM;MACdI,CAAC,EAAAF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACIJ,IAAI,CAACC,MAAM,CAACI,CAAC;QAChBR,KAAK,EAAE;MAAE;IAAA;EAAA,EAGd;EAED,MAAMS,WAAW,GAAG,CAAAC,EAAA,GAAAN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,KAAK,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,KAAK;EACxC,IAAI,CAACR,MAAM,IAAI,CAACK,WAAW,EAAE;IAC3B,OAAOJ,aAAa;;EAGtB;EACA,IAAIQ,YAAY;EAEhB,KAAK,MAAM,CAACC,GAAG,EAAEC,CAAC,CAAC,IAAIT,MAAM,CAACU,OAAO,CAACZ,MAAM,CAAC,EAAE;IAC7C,IAAI,CAACU,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,GAAG,KAAKC,CAAC,CAACH,KAAK,KAAKH,WAAW,EAAE;MAC3DI,YAAY,GAAAP,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACPM,YAAY;QACf,CAACC,GAAG,GAACR,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACAQ,CAAC;UACJf,KAAK,EAAE;QAAE;MAAA,EAEZ;;;EAIL,IAAI,CAACa,YAAY,EAAE;IACjB,OAAOR,aAAa;;EAGtB,OAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACKJ,IAAI;IACPC,MAAM,EAAAE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACDJ,IAAI,CAACC,MAAM,GACXS,YAAY;EAAA;AAGrB;AAEA;;;AAGA,OAAO,MAAMI,MAAM,GAAsBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACxD,OAAO,CAACC,CAAC,EAAEhB,IAAI,KAAI;IACjB,MAAM;MAAEC,MAAM;MAAEgB;IAAI,CAAE,GAAGjB,IAAI;IAC7B,MAAMkB,OAAO,GAAGf,MAAM,CAACU,OAAO,CAACE,OAAO,CAAC,CACpCI,GAAG,CAAC,CAAC,CAACR,GAAG,EAAEC,CAAC,CAAC,KAAI;MAChB,MAAM,CAACQ,CAAC,CAAC,GAAGzB,QAAQ,CAACM,MAAM,EAAEU,GAAG,CAAC;MACjC;MACA,IAAI,CAACS,CAAC,EAAE,OAAO,IAAI;MACnB,MAAM,CAACvB,KAAK,EAAEC,OAAO,GAAG,IAAI,CAAC,GAAGF,cAAc,CAACgB,CAAC,CAAC;MACjD,IAAI,OAAOf,KAAK,KAAK,UAAU,EAAE,OAAQwB,CAAC,IAAKxB,KAAK,CAACuB,CAAC,CAACC,CAAC,CAAC,CAAC;MAC1D,IAAIvB,OAAO,EAAE;QACX,MAAMwB,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;QAC7D;QACA,IAAIyB,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC5C,OAAQJ,CAAC,IAAKC,cAAc,CAACI,QAAQ,CAACN,CAAC,CAACC,CAAC,CAAC,CAAC;OAC5C,MAAM;QACL,MAAM,CAACM,KAAK,EAAEC,GAAG,CAAC,GAAG/B,KAAK;QAC1B,OAAQwB,CAAC,IAAKD,CAAC,CAACC,CAAC,CAAC,IAAIM,KAAK,IAAIP,CAAC,CAACC,CAAC,CAAC,IAAIO,GAAG;;IAE9C,CAAC,CAAC,CACDC,MAAM,CAACnC,OAAO,CAAC;IAClB;IACA,MAAMoC,WAAW,GAAIT,CAAC,IAAKH,OAAO,CAACa,KAAK,CAAEC,CAAC,IAAKA,CAAC,CAACX,CAAC,CAAC,CAAC;IACrD,MAAMY,EAAE,GAAGjB,CAAC,CAACa,MAAM,CAACC,WAAW,CAAC;IAChC,MAAMI,QAAQ,GAAGD,EAAE,CAACd,GAAG,CAAC,CAACgB,CAAC,EAAEd,CAAC,KAAKA,CAAC,CAAC;IAEpC,IAAIH,OAAO,CAACO,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMW,UAAU,GAAGrC,oBAAoB,CAACC,IAAI,CAAC;MAC7C,OAAO,CAACgB,CAAC,EAAEoB,UAAU,CAAC;;IAGxB,MAAMC,UAAU,GAAGlC,MAAM,CAACU,OAAO,CAACZ,MAAM,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,GAAG,EAAEV,MAAM,CAAC,KAAI;MAC9D,OAAO,CACLU,GAAG,E,gCAEEV,MAAM;QACTJ,KAAK,EAAEqC,QAAQ,CACZf,GAAG,CAAEE,CAAC,IAAKpB,MAAM,CAACJ,KAAK,CAACoC,EAAE,CAACZ,CAAC,CAAC,CAAC,CAAC,CAC/BQ,MAAM,CAAEjB,CAAC,IAAKA,CAAC,KAAK0B,SAAS;MAAC,GAEpC;IACH,CAAC,CAAC;IACF,OAAO,CACLJ,QAAQ,EACRzC,OAAO,CAAC,EAAE,EAAEO,IAAI,EAAE;MAChBC,MAAM,EAAEE,MAAM,CAACoC,WAAW,CAACF,UAAU,CAAC;MACtC;MACApB,IAAI,EAAEgB,EAAE,CAACd,GAAG,CAAEE,CAAC,IAAKJ,IAAI,CAACI,CAAC,CAAC;KAC5B,CAAC,CACH;EACH,CAAC;AACH,CAAC;AAEDP,MAAM,CAAC0B,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}