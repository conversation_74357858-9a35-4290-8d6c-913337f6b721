{"ast": null, "code": "export { Band } from './band';\nexport { Linear } from './linear';\nexport { Ordinal } from './ordinal';\nexport { Identity } from './identity';\nexport { Point } from './point';\nexport { Time } from './time';\nexport { Log } from './log';\nexport { Pow } from './pow';\nexport { Threshold } from './threshold';\nexport { Quantile } from './quantile';\nexport { Quantize } from './quantize';\nexport { Sqrt } from './sqrt';\nexport { Sequential } from './sequential';\nexport { Constant } from './constant';", "map": {"version": 3, "names": ["Band", "Linear", "Ordinal", "Identity", "Point", "Time", "Log", "<PERSON>w", "<PERSON><PERSON><PERSON><PERSON>", "Quantile", "Quantize", "Sqrt", "Sequential", "Constant"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/scale/index.ts"], "sourcesContent": ["export { Band } from './band';\nexport { Linear } from './linear';\nexport { Ordinal } from './ordinal';\nexport { Identity } from './identity';\nexport { Point } from './point';\nexport { Time } from './time';\nexport { Log } from './log';\nexport { Pow } from './pow';\nexport { Threshold } from './threshold';\nexport { Quantile } from './quantile';\nexport { Quantize } from './quantize';\nexport { Sqrt } from './sqrt';\nexport { Sequential } from './sequential';\nexport { Constant } from './constant';\n\nexport type { BandOptions } from './band';\nexport type { LinearOptions } from './linear';\nexport type { OrdinalOptions } from './ordinal';\nexport type { IdentityOptions } from './identity';\nexport type { PointOptions } from './point';\nexport type { TimeOptions } from './time';\nexport type { LogOptions } from './log';\nexport type { PowOptions } from './pow';\nexport type { ThresholdOptions } from './threshold';\nexport type { QuantileOptions } from './quantile';\nexport type { QuantizeOptions } from './quantize';\nexport type { SqrtOptions } from './sqrt';\nexport type { SequentialOptions } from './sequential';\nexport type { ConstantOptions } from './constant';\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}