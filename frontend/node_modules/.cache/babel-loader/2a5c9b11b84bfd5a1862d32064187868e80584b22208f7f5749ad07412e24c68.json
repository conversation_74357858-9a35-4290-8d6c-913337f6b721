{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\n\n/**\n * Used for `useFilledProps` since it already in the React.useMemo\n */\nexport function fillClearIcon(prefixCls, allowClear, clearIcon) {\n  if (process.env.NODE_ENV !== 'production' && clearIcon) {\n    warning(false, '`clearIcon` will be removed in future. Please use `allowClear` instead.');\n  }\n  if (allowClear === false) {\n    return null;\n  }\n  var config = allowClear && _typeof(allowClear) === 'object' ? allowClear : {};\n  return config.clearIcon || clearIcon || /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-clear-btn\")\n  });\n}", "map": {"version": 3, "names": ["_typeof", "warning", "React", "fillClearIcon", "prefixCls", "allowClear", "clearIcon", "process", "env", "NODE_ENV", "config", "createElement", "className", "concat"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-picker/es/PickerInput/Selector/hooks/useClearIcon.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\n\n/**\n * Used for `useFilledProps` since it already in the React.useMemo\n */\nexport function fillClearIcon(prefixCls, allowClear, clearIcon) {\n  if (process.env.NODE_ENV !== 'production' && clearIcon) {\n    warning(false, '`clearIcon` will be removed in future. Please use `allowClear` instead.');\n  }\n  if (allowClear === false) {\n    return null;\n  }\n  var config = allowClear && _typeof(allowClear) === 'object' ? allowClear : {};\n  return config.clearIcon || clearIcon || /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-clear-btn\")\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC9D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,SAAS,EAAE;IACtDL,OAAO,CAAC,KAAK,EAAE,yEAAyE,CAAC;EAC3F;EACA,IAAII,UAAU,KAAK,KAAK,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAIK,MAAM,GAAGL,UAAU,IAAIL,OAAO,CAACK,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;EAC7E,OAAOK,MAAM,CAACJ,SAAS,IAAIA,SAAS,IAAI,aAAaJ,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC/EC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,YAAY;EAC9C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}