{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport React from 'react';\n\n/**\n * Will be `true` immediately for next effect.\n * But will be `false` for a delay of effect.\n */\nexport default function useDelayState(value, defaultValue, onChange) {\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    state = _useMergedState2[0],\n    setState = _useMergedState2[1];\n  var nextValueRef = React.useRef(value);\n\n  // ============================= Update =============================\n  var rafRef = React.useRef();\n  var cancelRaf = function cancelRaf() {\n    raf.cancel(rafRef.current);\n  };\n  var doUpdate = useEvent(function () {\n    setState(nextValueRef.current);\n    if (onChange && state !== nextValueRef.current) {\n      onChange(nextValueRef.current);\n    }\n  });\n  var updateValue = useEvent(function (next, immediately) {\n    cancelRaf();\n    nextValueRef.current = next;\n    if (next || immediately) {\n      doUpdate();\n    } else {\n      rafRef.current = raf(doUpdate);\n    }\n  });\n  React.useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [state, updateValue];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEvent", "useMergedState", "raf", "React", "useDelayState", "value", "defaultValue", "onChange", "_useMergedState", "_useMergedState2", "state", "setState", "nextValueRef", "useRef", "rafRef", "cancelRaf", "cancel", "current", "doUpdate", "updateValue", "next", "immediately", "useEffect"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-picker/es/PickerInput/hooks/useDelayState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport React from 'react';\n\n/**\n * Will be `true` immediately for next effect.\n * But will be `false` for a delay of effect.\n */\nexport default function useDelayState(value, defaultValue, onChange) {\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    state = _useMergedState2[0],\n    setState = _useMergedState2[1];\n  var nextValueRef = React.useRef(value);\n\n  // ============================= Update =============================\n  var rafRef = React.useRef();\n  var cancelRaf = function cancelRaf() {\n    raf.cancel(rafRef.current);\n  };\n  var doUpdate = useEvent(function () {\n    setState(nextValueRef.current);\n    if (onChange && state !== nextValueRef.current) {\n      onChange(nextValueRef.current);\n    }\n  });\n  var updateValue = useEvent(function (next, immediately) {\n    cancelRaf();\n    nextValueRef.current = next;\n    if (next || immediately) {\n      doUpdate();\n    } else {\n      rafRef.current = raf(doUpdate);\n    }\n  });\n  React.useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [state, updateValue];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,cAAc,QAAQ,SAAS;AAClD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,EAAE;EACnE,IAAIC,eAAe,GAAGP,cAAc,CAACK,YAAY,EAAE;MAC/CD,KAAK,EAAEA;IACT,CAAC,CAAC;IACFI,gBAAgB,GAAGV,cAAc,CAACS,eAAe,EAAE,CAAC,CAAC;IACrDE,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,YAAY,GAAGT,KAAK,CAACU,MAAM,CAACR,KAAK,CAAC;;EAEtC;EACA,IAAIS,MAAM,GAAGX,KAAK,CAACU,MAAM,CAAC,CAAC;EAC3B,IAAIE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCb,GAAG,CAACc,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;EAC5B,CAAC;EACD,IAAIC,QAAQ,GAAGlB,QAAQ,CAAC,YAAY;IAClCW,QAAQ,CAACC,YAAY,CAACK,OAAO,CAAC;IAC9B,IAAIV,QAAQ,IAAIG,KAAK,KAAKE,YAAY,CAACK,OAAO,EAAE;MAC9CV,QAAQ,CAACK,YAAY,CAACK,OAAO,CAAC;IAChC;EACF,CAAC,CAAC;EACF,IAAIE,WAAW,GAAGnB,QAAQ,CAAC,UAAUoB,IAAI,EAAEC,WAAW,EAAE;IACtDN,SAAS,CAAC,CAAC;IACXH,YAAY,CAACK,OAAO,GAAGG,IAAI;IAC3B,IAAIA,IAAI,IAAIC,WAAW,EAAE;MACvBH,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLJ,MAAM,CAACG,OAAO,GAAGf,GAAG,CAACgB,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC;EACFf,KAAK,CAACmB,SAAS,CAAC,YAAY;IAC1B,OAAOP,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACL,KAAK,EAAES,WAAW,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}