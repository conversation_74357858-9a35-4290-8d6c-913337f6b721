{"ast": null, "code": "import { CommonEvent } from '../constants';\n/**\n * <zh/> 捏合手势处理器\n *\n * <en/> Pinch gesture handler\n * @remarks\n * <zh/> 处理双指触摸事件，计算缩放比例并触发回调。通过跟踪两个触摸点的位置变化，计算两点间距离变化率来确定缩放比例。\n *\n * <en/> Handles two-finger touch events, calculates zoom ratio and triggers callbacks. Tracks position changes of two touch points to determine zoom ratio based on distance variation.\n */\nexport class PinchHandler {\n  constructor(emitter, phase, callback) {\n    this.phase = phase;\n    /**\n     * <zh/> 当前跟踪的触摸点集合\n     *\n     * <en/> Currently tracked touch points collection\n     */\n    this.pointerByTouch = [];\n    /**\n     * <zh/> 初始两点间距离\n     *\n     * <en/> Initial distance between two points\n     */\n    this.initialDistance = null;\n    this.emitter = emitter;\n    if (PinchHandler.instance) {\n      PinchHandler.callbacks[this.phase].push(callback);\n      return PinchHandler.instance;\n    }\n    this.onPointerDown = this.onPointerDown.bind(this);\n    this.onPointerMove = this.onPointerMove.bind(this);\n    this.onPointerUp = this.onPointerUp.bind(this);\n    this.bindEvents();\n    PinchHandler.instance = this;\n    PinchHandler.callbacks[this.phase].push(callback);\n  }\n  bindEvents() {\n    const {\n      emitter\n    } = this;\n    emitter.on(CommonEvent.POINTER_DOWN, this.onPointerDown);\n    emitter.on(CommonEvent.POINTER_MOVE, this.onPointerMove);\n    emitter.on(CommonEvent.POINTER_UP, this.onPointerUp);\n  }\n  /**\n   * <zh/> 更新指定指针的位置\n   *\n   * <en/> Update position of specified pointer\n   * @param pointerId - <zh/> 指针唯一标识符 | <en/> Pointer unique identifier<sup>1</sup>\n   * @param x - <zh/> 新的X坐标 | <en/> New X coordinate\n   * @param y - <zh/> 新的Y坐标 | <en/> New Y coordinate\n   */\n  updatePointerPosition(pointerId, x, y) {\n    const index = this.pointerByTouch.findIndex(p => p.pointerId === pointerId);\n    if (index >= 0) {\n      this.pointerByTouch[index] = {\n        x,\n        y,\n        pointerId\n      };\n    }\n  }\n  /**\n   * <zh/> 处理指针按下事件\n   *\n   * <en/> Handle pointer down event\n   * @param event - <zh/> 指针事件对象 | <en/> Pointer event object\n   * @remarks\n   * <zh/> 当检测到两个触摸点时记录初始距离\n   *\n   * <en/> Record initial distance when detecting two touch points\n   */\n  onPointerDown(event) {\n    const {\n      x,\n      y\n    } = event.client || {};\n    if (x === undefined || y === undefined) return;\n    this.pointerByTouch.push({\n      x,\n      y,\n      pointerId: event.pointerId\n    });\n    if (event.pointerType === 'touch' && this.pointerByTouch.length === 2) {\n      PinchHandler.isPinching = true;\n      const dx = this.pointerByTouch[0].x - this.pointerByTouch[1].x;\n      const dy = this.pointerByTouch[0].y - this.pointerByTouch[1].y;\n      this.initialDistance = Math.sqrt(dx * dx + dy * dy);\n      PinchHandler.callbacks.pinchstart.forEach(cb => cb(event, {\n        scale: 0\n      }));\n    }\n  }\n  /**\n   * <zh/> 处理指针移动事件\n   *\n   * <en/> Handle pointer move event\n   * @param event - <zh/> 指针事件对象 | <en/> Pointer event object\n   * @remarks\n   * <zh/> 当存在两个有效触摸点时计算缩放比例\n   *\n   * <en/> Calculate zoom ratio when two valid touch points exist\n   */\n  onPointerMove(event) {\n    if (this.pointerByTouch.length !== 2 || this.initialDistance === null) return;\n    const {\n      x,\n      y\n    } = event.client || {};\n    if (x === undefined || y === undefined) return;\n    this.updatePointerPosition(event.pointerId, x, y);\n    const dx = this.pointerByTouch[0].x - this.pointerByTouch[1].x;\n    const dy = this.pointerByTouch[0].y - this.pointerByTouch[1].y;\n    const currentDistance = Math.sqrt(dx * dx + dy * dy);\n    const ratio = currentDistance / this.initialDistance;\n    PinchHandler.callbacks.pinchmove.forEach(cb => cb(event, {\n      scale: (ratio - 1) * 5\n    }));\n  }\n  /**\n   * <zh/> 处理指针抬起事件\n   *\n   * <en/> Handle pointer up event\n   * @param event\n   * @remarks\n   * <zh/> 重置触摸状态和初始距离\n   *\n   * <en/> Reset touch state and initial distance\n   */\n  onPointerUp(event) {\n    var _a;\n    PinchHandler.callbacks.pinchend.forEach(cb => cb(event, {\n      scale: 0\n    }));\n    PinchHandler.isPinching = false;\n    this.initialDistance = null;\n    this.pointerByTouch = [];\n    (_a = PinchHandler.instance) === null || _a === void 0 ? void 0 : _a.tryDestroy();\n  }\n  /**\n   * <zh/> 销毁捏合手势相关监听\n   *\n   * <en/> Destroy pinch gesture listeners\n   * @remarks\n   * <zh/> 移除指针按下、移动、抬起事件的监听\n   *\n   * <en/> Remove listeners for pointer down, move, and up events\n   */\n  destroy() {\n    this.emitter.off(CommonEvent.POINTER_DOWN, this.onPointerDown);\n    this.emitter.off(CommonEvent.POINTER_MOVE, this.onPointerMove);\n    this.emitter.off(CommonEvent.POINTER_UP, this.onPointerUp);\n    PinchHandler.instance = null;\n  }\n  /**\n   * <zh/> 解绑指定阶段的手势回调\n   * <en/> Unregister gesture callback for specific phase\n   * @param phase - <zh/> 手势阶段：开始(pinchstart)/移动(pinchmove)/结束(pinchend) | <en/> Gesture phase: start/move/end\n   * @param callback - <zh/> 要解绑的回调函数 | <en/> Callback function to unregister\n   * @remarks\n   * <zh/> 从指定阶段的回调列表中移除特定回调，当所有回调都解绑后自动销毁事件监听\n   * <en/> Remove specific callback from the phase's callback list, auto-destroy event listeners when all callbacks are unregistered\n   */\n  off(phase, callback) {\n    const index = PinchHandler.callbacks[phase].indexOf(callback);\n    if (index > -1) PinchHandler.callbacks[phase].splice(index, 1);\n    this.tryDestroy();\n  }\n  /**\n   * <zh/> 尝试销毁手势处理器\n   * <en/> Attempt to destroy the gesture handler\n   * @remarks\n   * <zh/> 当所有阶段（开始/移动/结束）的回调列表都为空时，执行实际销毁操作\n   * <en/> Perform actual destruction when all phase (pinchstart/pinchmove/pinchend) callback lists are empty\n   * <zh/> 自动解除事件监听并重置单例实例\n   * <en/> Automatically remove event listeners and reset singleton instance\n   */\n  tryDestroy() {\n    if (Object.values(PinchHandler.callbacks).every(arr => arr.length === 0)) {\n      this.destroy();\n    }\n  }\n}\n/**\n * <zh/> 是否处于 Pinch 阶段\n *\n * <en/> Whether it is in the Pinch stage\n */\nPinchHandler.isPinching = false;\nPinchHandler.instance = null;\nPinchHandler.callbacks = {\n  pinchstart: [],\n  pinchmove: [],\n  pinchend: []\n};", "map": {"version": 3, "names": ["CommonEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "emitter", "phase", "callback", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialDistance", "instance", "callbacks", "push", "onPointerDown", "bind", "onPointerMove", "onPointerUp", "bindEvents", "on", "POINTER_DOWN", "POINTER_MOVE", "POINTER_UP", "updatePointerPosition", "pointerId", "x", "y", "index", "findIndex", "p", "event", "client", "undefined", "pointerType", "length", "isPinching", "dx", "dy", "Math", "sqrt", "pinchstart", "for<PERSON>ach", "cb", "scale", "currentDistance", "ratio", "pinchmove", "pinchend", "_a", "try<PERSON><PERSON><PERSON>", "destroy", "off", "indexOf", "splice", "Object", "values", "every", "arr"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g6/src/utils/pinch.ts"], "sourcesContent": ["import EventEmitter from '@antv/event-emitter';\nimport { CommonEvent } from '../constants';\nimport { IPointerEvent } from '../types';\n\n/**\n * <zh/> 表示指针位置的点坐标\n *\n * <en/> Represents the coordinates of a pointer position\n */\nexport interface PointerPoint {\n  x: number;\n  y: number;\n  pointerId: number;\n}\n\n/**\n * <zh/> 捏合事件参数\n *\n * <en/> Pinch event parameters\n * @remarks\n * <zh/> 包含与捏合手势相关的参数，当前支持缩放比例，未来可扩展中心点坐标、旋转角度等参数\n *\n * <en/> Contains parameters related to pinch gestures, currently supports scale factor,\n * can be extended with center coordinates, rotation angle etc. in the future\n */\nexport interface PinchEventOptions {\n  /**\n   * <zh/> 缩放比例因子，>1 表示放大，<1 表示缩小\n   *\n   * <en/> Scaling factor, >1 indicates zoom in, <1 indicates zoom out\n   */\n  scale: number;\n}\n\n/**\n * <zh/> 捏合手势阶段类型\n * <en/> Pinch gesture phase type\n * @remarks\n * <zh/> 包含三个手势阶段：\n * - start: 手势开始\n * - move: 手势移动中\n * - end: 手势结束\n *\n * <en/> Contains three gesture phases:\n * - pinchstart: Gesture started\n * - pinchmove: Gesture in progress\n * - pinchend: Gesture ended\n */\nexport type PinchEvent = 'pinchstart' | 'pinchmove' | 'pinchend';\n\n/**\n * <zh/> 捏合手势回调函数类型\n *\n * <en/> Pinch gesture callback function type\n * @param event - <zh/> 原始指针事件对象 | <en/> Original pointer event object\n * @param options - <zh/> 捏合事件参数对象 | <en/> Pinch event parameters object\n */\nexport type PinchCallback = (event: IPointerEvent, options: PinchEventOptions) => void;\n\n/**\n * <zh/> 捏合手势处理器\n *\n * <en/> Pinch gesture handler\n * @remarks\n * <zh/> 处理双指触摸事件，计算缩放比例并触发回调。通过跟踪两个触摸点的位置变化，计算两点间距离变化率来确定缩放比例。\n *\n * <en/> Handles two-finger touch events, calculates zoom ratio and triggers callbacks. Tracks position changes of two touch points to determine zoom ratio based on distance variation.\n */\nexport class PinchHandler {\n  /**\n   * <zh/> 是否处于 Pinch 阶段\n   *\n   * <en/> Whether it is in the Pinch stage\n   */\n  public static isPinching: boolean = false;\n\n  /**\n   * <zh/> 当前跟踪的触摸点集合\n   *\n   * <en/> Currently tracked touch points collection\n   */\n  private pointerByTouch: PointerPoint[] = [];\n\n  /**\n   * <zh/> 初始两点间距离\n   *\n   * <en/> Initial distance between two points\n   */\n  private initialDistance: number | null = null;\n\n  private emitter: EventEmitter;\n  private static instance: PinchHandler | null = null;\n  private static callbacks: {\n    pinchstart: PinchCallback[];\n    pinchmove: PinchCallback[];\n    pinchend: PinchCallback[];\n  } = { pinchstart: [], pinchmove: [], pinchend: [] };\n\n  constructor(\n    emitter: EventEmitter,\n    private phase: PinchEvent,\n    callback: PinchCallback,\n  ) {\n    this.emitter = emitter;\n    if (PinchHandler.instance) {\n      PinchHandler.callbacks[this.phase].push(callback);\n      return PinchHandler.instance;\n    }\n    this.onPointerDown = this.onPointerDown.bind(this);\n    this.onPointerMove = this.onPointerMove.bind(this);\n    this.onPointerUp = this.onPointerUp.bind(this);\n    this.bindEvents();\n    PinchHandler.instance = this;\n    PinchHandler.callbacks[this.phase].push(callback);\n  }\n\n  private bindEvents() {\n    const { emitter } = this;\n    emitter.on(CommonEvent.POINTER_DOWN, this.onPointerDown);\n    emitter.on(CommonEvent.POINTER_MOVE, this.onPointerMove);\n    emitter.on(CommonEvent.POINTER_UP, this.onPointerUp);\n  }\n\n  /**\n   * <zh/> 更新指定指针的位置\n   *\n   * <en/> Update position of specified pointer\n   * @param pointerId - <zh/> 指针唯一标识符 | <en/> Pointer unique identifier<sup>1</sup>\n   * @param x - <zh/> 新的X坐标 | <en/> New X coordinate\n   * @param y - <zh/> 新的Y坐标 | <en/> New Y coordinate\n   */\n  private updatePointerPosition(pointerId: number, x: number, y: number) {\n    const index = this.pointerByTouch.findIndex((p) => p.pointerId === pointerId);\n    if (index >= 0) {\n      this.pointerByTouch[index] = { x, y, pointerId };\n    }\n  }\n\n  /**\n   * <zh/> 处理指针按下事件\n   *\n   * <en/> Handle pointer down event\n   * @param event - <zh/> 指针事件对象 | <en/> Pointer event object\n   * @remarks\n   * <zh/> 当检测到两个触摸点时记录初始距离\n   *\n   * <en/> Record initial distance when detecting two touch points\n   */\n  onPointerDown(event: IPointerEvent) {\n    const { x, y } = event.client || {};\n    if (x === undefined || y === undefined) return;\n    this.pointerByTouch.push({ x, y, pointerId: event.pointerId });\n\n    if (event.pointerType === 'touch' && this.pointerByTouch.length === 2) {\n      PinchHandler.isPinching = true;\n      const dx = this.pointerByTouch[0].x - this.pointerByTouch[1].x;\n      const dy = this.pointerByTouch[0].y - this.pointerByTouch[1].y;\n      this.initialDistance = Math.sqrt(dx * dx + dy * dy);\n      PinchHandler.callbacks.pinchstart.forEach((cb) => cb(event, { scale: 0 }));\n    }\n  }\n\n  /**\n   * <zh/> 处理指针移动事件\n   *\n   * <en/> Handle pointer move event\n   * @param event - <zh/> 指针事件对象 | <en/> Pointer event object\n   * @remarks\n   * <zh/> 当存在两个有效触摸点时计算缩放比例\n   *\n   * <en/> Calculate zoom ratio when two valid touch points exist\n   */\n  onPointerMove(event: IPointerEvent) {\n    if (this.pointerByTouch.length !== 2 || this.initialDistance === null) return;\n    const { x, y } = event.client || {};\n    if (x === undefined || y === undefined) return;\n    this.updatePointerPosition(event.pointerId, x, y);\n    const dx = this.pointerByTouch[0].x - this.pointerByTouch[1].x;\n    const dy = this.pointerByTouch[0].y - this.pointerByTouch[1].y;\n    const currentDistance = Math.sqrt(dx * dx + dy * dy);\n    const ratio = currentDistance / this.initialDistance;\n\n    PinchHandler.callbacks.pinchmove.forEach((cb) => cb(event, { scale: (ratio - 1) * 5 }));\n  }\n\n  /**\n   * <zh/> 处理指针抬起事件\n   *\n   * <en/> Handle pointer up event\n   * @param event\n   * @remarks\n   * <zh/> 重置触摸状态和初始距离\n   *\n   * <en/> Reset touch state and initial distance\n   */\n  onPointerUp(event: IPointerEvent) {\n    PinchHandler.callbacks.pinchend.forEach((cb) => cb(event, { scale: 0 }));\n    PinchHandler.isPinching = false;\n    this.initialDistance = null;\n    this.pointerByTouch = [];\n    PinchHandler.instance?.tryDestroy();\n  }\n\n  /**\n   * <zh/> 销毁捏合手势相关监听\n   *\n   * <en/> Destroy pinch gesture listeners\n   * @remarks\n   * <zh/> 移除指针按下、移动、抬起事件的监听\n   *\n   * <en/> Remove listeners for pointer down, move, and up events\n   */\n  public destroy() {\n    this.emitter.off(CommonEvent.POINTER_DOWN, this.onPointerDown);\n    this.emitter.off(CommonEvent.POINTER_MOVE, this.onPointerMove);\n    this.emitter.off(CommonEvent.POINTER_UP, this.onPointerUp);\n    PinchHandler.instance = null;\n  }\n\n  /**\n   * <zh/> 解绑指定阶段的手势回调\n   * <en/> Unregister gesture callback for specific phase\n   * @param phase - <zh/> 手势阶段：开始(pinchstart)/移动(pinchmove)/结束(pinchend) | <en/> Gesture phase: start/move/end\n   * @param callback - <zh/> 要解绑的回调函数 | <en/> Callback function to unregister\n   * @remarks\n   * <zh/> 从指定阶段的回调列表中移除特定回调，当所有回调都解绑后自动销毁事件监听\n   * <en/> Remove specific callback from the phase's callback list, auto-destroy event listeners when all callbacks are unregistered\n   */\n  public off(phase: PinchEvent, callback: PinchCallback) {\n    const index = PinchHandler.callbacks[phase].indexOf(callback);\n    if (index > -1) PinchHandler.callbacks[phase].splice(index, 1);\n    this.tryDestroy();\n  }\n\n  /**\n   * <zh/> 尝试销毁手势处理器\n   * <en/> Attempt to destroy the gesture handler\n   * @remarks\n   * <zh/> 当所有阶段（开始/移动/结束）的回调列表都为空时，执行实际销毁操作\n   * <en/> Perform actual destruction when all phase (pinchstart/pinchmove/pinchend) callback lists are empty\n   * <zh/> 自动解除事件监听并重置单例实例\n   * <en/> Automatically remove event listeners and reset singleton instance\n   */\n  private tryDestroy() {\n    if (Object.values(PinchHandler.callbacks).every((arr) => arr.length === 0)) {\n      this.destroy();\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,cAAc;AA0D1C;;;;;;;;;AASA,OAAM,MAAOC,YAAY;EA8BvBC,YACEC,OAAqB,EACbC,KAAiB,EACzBC,QAAuB;IADf,KAAAD,KAAK,GAALA,KAAK;IAxBf;;;;;IAKQ,KAAAE,cAAc,GAAmB,EAAE;IAE3C;;;;;IAKQ,KAAAC,eAAe,GAAkB,IAAI;IAe3C,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAIF,YAAY,CAACO,QAAQ,EAAE;MACzBP,YAAY,CAACQ,SAAS,CAAC,IAAI,CAACL,KAAK,CAAC,CAACM,IAAI,CAACL,QAAQ,CAAC;MACjD,OAAOJ,YAAY,CAACO,QAAQ;IAC9B;IACA,IAAI,CAACG,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,UAAU,EAAE;IACjBd,YAAY,CAACO,QAAQ,GAAG,IAAI;IAC5BP,YAAY,CAACQ,SAAS,CAAC,IAAI,CAACL,KAAK,CAAC,CAACM,IAAI,CAACL,QAAQ,CAAC;EACnD;EAEQU,UAAUA,CAAA;IAChB,MAAM;MAAEZ;IAAO,CAAE,GAAG,IAAI;IACxBA,OAAO,CAACa,EAAE,CAAChB,WAAW,CAACiB,YAAY,EAAE,IAAI,CAACN,aAAa,CAAC;IACxDR,OAAO,CAACa,EAAE,CAAChB,WAAW,CAACkB,YAAY,EAAE,IAAI,CAACL,aAAa,CAAC;IACxDV,OAAO,CAACa,EAAE,CAAChB,WAAW,CAACmB,UAAU,EAAE,IAAI,CAACL,WAAW,CAAC;EACtD;EAEA;;;;;;;;EAQQM,qBAAqBA,CAACC,SAAiB,EAAEC,CAAS,EAAEC,CAAS;IACnE,MAAMC,KAAK,GAAG,IAAI,CAAClB,cAAc,CAACmB,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAKA,SAAS,CAAC;IAC7E,IAAIG,KAAK,IAAI,CAAC,EAAE;MACd,IAAI,CAAClB,cAAc,CAACkB,KAAK,CAAC,GAAG;QAAEF,CAAC;QAAEC,CAAC;QAAEF;MAAS,CAAE;IAClD;EACF;EAEA;;;;;;;;;;EAUAV,aAAaA,CAACgB,KAAoB;IAChC,MAAM;MAAEL,CAAC;MAAEC;IAAC,CAAE,GAAGI,KAAK,CAACC,MAAM,IAAI,EAAE;IACnC,IAAIN,CAAC,KAAKO,SAAS,IAAIN,CAAC,KAAKM,SAAS,EAAE;IACxC,IAAI,CAACvB,cAAc,CAACI,IAAI,CAAC;MAAEY,CAAC;MAAEC,CAAC;MAAEF,SAAS,EAAEM,KAAK,CAACN;IAAS,CAAE,CAAC;IAE9D,IAAIM,KAAK,CAACG,WAAW,KAAK,OAAO,IAAI,IAAI,CAACxB,cAAc,CAACyB,MAAM,KAAK,CAAC,EAAE;MACrE9B,YAAY,CAAC+B,UAAU,GAAG,IAAI;MAC9B,MAAMC,EAAE,GAAG,IAAI,CAAC3B,cAAc,CAAC,CAAC,CAAC,CAACgB,CAAC,GAAG,IAAI,CAAChB,cAAc,CAAC,CAAC,CAAC,CAACgB,CAAC;MAC9D,MAAMY,EAAE,GAAG,IAAI,CAAC5B,cAAc,CAAC,CAAC,CAAC,CAACiB,CAAC,GAAG,IAAI,CAACjB,cAAc,CAAC,CAAC,CAAC,CAACiB,CAAC;MAC9D,IAAI,CAAChB,eAAe,GAAG4B,IAAI,CAACC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MACnDjC,YAAY,CAACQ,SAAS,CAAC4B,UAAU,CAACC,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACZ,KAAK,EAAE;QAAEa,KAAK,EAAE;MAAC,CAAE,CAAC,CAAC;IAC5E;EACF;EAEA;;;;;;;;;;EAUA3B,aAAaA,CAACc,KAAoB;IAChC,IAAI,IAAI,CAACrB,cAAc,CAACyB,MAAM,KAAK,CAAC,IAAI,IAAI,CAACxB,eAAe,KAAK,IAAI,EAAE;IACvE,MAAM;MAAEe,CAAC;MAAEC;IAAC,CAAE,GAAGI,KAAK,CAACC,MAAM,IAAI,EAAE;IACnC,IAAIN,CAAC,KAAKO,SAAS,IAAIN,CAAC,KAAKM,SAAS,EAAE;IACxC,IAAI,CAACT,qBAAqB,CAACO,KAAK,CAACN,SAAS,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACjD,MAAMU,EAAE,GAAG,IAAI,CAAC3B,cAAc,CAAC,CAAC,CAAC,CAACgB,CAAC,GAAG,IAAI,CAAChB,cAAc,CAAC,CAAC,CAAC,CAACgB,CAAC;IAC9D,MAAMY,EAAE,GAAG,IAAI,CAAC5B,cAAc,CAAC,CAAC,CAAC,CAACiB,CAAC,GAAG,IAAI,CAACjB,cAAc,CAAC,CAAC,CAAC,CAACiB,CAAC;IAC9D,MAAMkB,eAAe,GAAGN,IAAI,CAACC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACpD,MAAMQ,KAAK,GAAGD,eAAe,GAAG,IAAI,CAAClC,eAAe;IAEpDN,YAAY,CAACQ,SAAS,CAACkC,SAAS,CAACL,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACZ,KAAK,EAAE;MAAEa,KAAK,EAAE,CAACE,KAAK,GAAG,CAAC,IAAI;IAAC,CAAE,CAAC,CAAC;EACzF;EAEA;;;;;;;;;;EAUA5B,WAAWA,CAACa,KAAoB;;IAC9B1B,YAAY,CAACQ,SAAS,CAACmC,QAAQ,CAACN,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACZ,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAC,CAAE,CAAC,CAAC;IACxEvC,YAAY,CAAC+B,UAAU,GAAG,KAAK;IAC/B,IAAI,CAACzB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,cAAc,GAAG,EAAE;IACxB,CAAAuC,EAAA,GAAA5C,YAAY,CAACO,QAAQ,cAAAqC,EAAA,uBAAAA,EAAA,CAAEC,UAAU,EAAE;EACrC;EAEA;;;;;;;;;EASOC,OAAOA,CAAA;IACZ,IAAI,CAAC5C,OAAO,CAAC6C,GAAG,CAAChD,WAAW,CAACiB,YAAY,EAAE,IAAI,CAACN,aAAa,CAAC;IAC9D,IAAI,CAACR,OAAO,CAAC6C,GAAG,CAAChD,WAAW,CAACkB,YAAY,EAAE,IAAI,CAACL,aAAa,CAAC;IAC9D,IAAI,CAACV,OAAO,CAAC6C,GAAG,CAAChD,WAAW,CAACmB,UAAU,EAAE,IAAI,CAACL,WAAW,CAAC;IAC1Db,YAAY,CAACO,QAAQ,GAAG,IAAI;EAC9B;EAEA;;;;;;;;;EASOwC,GAAGA,CAAC5C,KAAiB,EAAEC,QAAuB;IACnD,MAAMmB,KAAK,GAAGvB,YAAY,CAACQ,SAAS,CAACL,KAAK,CAAC,CAAC6C,OAAO,CAAC5C,QAAQ,CAAC;IAC7D,IAAImB,KAAK,GAAG,CAAC,CAAC,EAAEvB,YAAY,CAACQ,SAAS,CAACL,KAAK,CAAC,CAAC8C,MAAM,CAAC1B,KAAK,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACsB,UAAU,EAAE;EACnB;EAEA;;;;;;;;;EASQA,UAAUA,CAAA;IAChB,IAAIK,MAAM,CAACC,MAAM,CAACnD,YAAY,CAACQ,SAAS,CAAC,CAAC4C,KAAK,CAAEC,GAAG,IAAKA,GAAG,CAACvB,MAAM,KAAK,CAAC,CAAC,EAAE;MAC1E,IAAI,CAACgB,OAAO,EAAE;IAChB;EACF;;AAlLA;;;;;AAKc9C,YAAA,CAAA+B,UAAU,GAAY,KAAK;AAiB1B/B,YAAA,CAAAO,QAAQ,GAAwB,IAAI;AACpCP,YAAA,CAAAQ,SAAS,GAIpB;EAAE4B,UAAU,EAAE,EAAE;EAAEM,SAAS,EAAE,EAAE;EAAEC,QAAQ,EAAE;AAAE,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}