{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    margin\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      // ========================== Pagination ==========================\n      [\"\".concat(componentCls, \"-pagination\").concat(antCls, \"-pagination\")]: {\n        margin: \"\".concat(unit(margin), \" 0\")\n      },\n      [\"\".concat(componentCls, \"-pagination\")]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;", "map": {"version": 3, "names": ["unit", "genPaginationStyle", "token", "componentCls", "antCls", "margin", "concat", "display", "flexWrap", "rowGap", "paddingXS", "flex", "justifyContent"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/pagination.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    margin\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Pagination ==========================\n      [`${componentCls}-pagination${antCls}-pagination`]: {\n        margin: `${unit(margin)} 0`\n      },\n      [`${componentCls}-pagination`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC;EACF,CAAC,GAAGH,KAAK;EACT,OAAO;IACL,IAAAI,MAAA,CAAIH,YAAY,gBAAa;MAC3B;MACA,IAAAG,MAAA,CAAIH,YAAY,iBAAAG,MAAA,CAAcF,MAAM,mBAAgB;QAClDC,MAAM,KAAAC,MAAA,CAAKN,IAAI,CAACK,MAAM,CAAC;MACzB,CAAC;MACD,IAAAC,MAAA,CAAIH,YAAY,mBAAgB;QAC9BI,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAEP,KAAK,CAACQ,SAAS;QACvB,KAAK,EAAE;UACLC,IAAI,EAAE;QACR,CAAC;QACD,QAAQ,EAAE;UACRC,cAAc,EAAE;QAClB,CAAC;QACD,UAAU,EAAE;UACVA,cAAc,EAAE;QAClB,CAAC;QACD,SAAS,EAAE;UACTA,cAAc,EAAE;QAClB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}