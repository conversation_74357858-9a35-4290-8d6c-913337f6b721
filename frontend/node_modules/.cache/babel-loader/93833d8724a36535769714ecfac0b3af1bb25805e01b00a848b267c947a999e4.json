{"ast": null, "code": "// ui\nexport * from './ui';\n// 方法\nexport * from './util';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/component/src/index.ts"], "sourcesContent": ["// ui\nexport * from './ui';\n\n// 方法\nexport * from './util';\n"], "mappings": "AAAA;AACA,cAAc,MAAM;AAEpB;AACA,cAAc,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}