{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { create } from './create';\nexport const tokens = {\n  colorBlack: '#fff',\n  colorWhite: '#000',\n  colorStroke: '#416180',\n  colorDefault: '#1783FF',\n  colorBackground: 'transparent',\n  category10: ['#1783FF', '#00C9C9', '#F0884D', '#D580FF', '#7863FF', '#60C42D', '#BD8F24', '#FF80CA', '#2491B3', '#17C76F'],\n  category20: ['#1783FF', '#00C9C9', '#F0884D', '#D580FF', '#7863FF', '#60C42D', '#BD8F24', '#FF80CA', '#2491B3', '#17C76F', '#AABA01', '#BC7CFC', '#237CBC', '#2DE379', '#CE8032', '#FF7AF4', '#545FD3', '#AFE410', '#D8C608', '#FFA1E0'],\n  padding1: 8,\n  padding2: 12,\n  padding3: 20,\n  alpha90: 0.9,\n  alpha65: 0.65,\n  alpha45: 0.45,\n  alpha25: 0.25,\n  alpha10: 0.25\n};\nconst defaults = create(tokens);\nexport const Dark = options => {\n  return deepMix({}, defaults, {\n    tooltip: {\n      crosshairsStroke: '#fff',\n      crosshairsLineWidth: 1,\n      crosshairsStrokeOpacity: 0.25,\n      css: {\n        '.g2-tooltip': {\n          background: '#1f1f1f',\n          opacity: 0.95\n        },\n        '.g2-tooltip-title': {\n          color: '#A6A6A6'\n        },\n        '.g2-tooltip-list-item-name-label': {\n          color: '#A6A6A6'\n        },\n        '.g2-tooltip-list-item-value': {\n          color: '#A6A6A6'\n        }\n      }\n    }\n  }, options);\n};", "map": {"version": 3, "names": ["deepMix", "create", "tokens", "colorBlack", "colorWhite", "colorStroke", "colorDefault", "colorBackground", "category10", "category20", "padding1", "padding2", "padding3", "alpha90", "alpha65", "alpha45", "alpha25", "alpha10", "defaults", "Dark", "options", "tooltip", "crosshairsStroke", "crosshairsLineWidth", "crosshairsStrokeOpacity", "css", "background", "opacity", "color"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/theme/dark.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { ThemeComponent as TC, Theme } from '../runtime';\nimport { create } from './create';\n\nexport type DarkOptions = Theme;\n\nexport const tokens = {\n  colorBlack: '#fff',\n  colorWhite: '#000',\n  colorStroke: '#416180',\n  colorDefault: '#1783FF',\n  colorBackground: 'transparent',\n  category10: [\n    '#1783FF',\n    '#00C9C9',\n    '#F0884D',\n    '#D580FF',\n    '#7863FF',\n    '#60C42D',\n    '#BD8F24',\n    '#FF80CA',\n    '#2491B3',\n    '#17C76F',\n  ],\n  category20: [\n    '#1783FF',\n    '#00C9C9',\n    '#F0884D',\n    '#D580FF',\n    '#7863FF',\n    '#60C42D',\n    '#BD8F24',\n    '#FF80CA',\n    '#2491B3',\n    '#17C76F',\n    '#AABA01',\n    '#BC7CFC',\n    '#237CBC',\n    '#2DE379',\n    '#CE8032',\n    '#FF7AF4',\n    '#545FD3',\n    '#AFE410',\n    '#D8C608',\n    '#FFA1E0',\n  ],\n  padding1: 8,\n  padding2: 12,\n  padding3: 20,\n  alpha90: 0.9,\n  alpha65: 0.65,\n  alpha45: 0.45,\n  alpha25: 0.25,\n  alpha10: 0.25,\n};\n\nconst defaults = create(tokens);\n\nexport const Dark: TC<DarkOptions> = (options) => {\n  return deepMix(\n    {},\n    defaults,\n    {\n      tooltip: {\n        crosshairsStroke: '#fff',\n        crosshairsLineWidth: 1,\n        crosshairsStrokeOpacity: 0.25,\n        css: {\n          '.g2-tooltip': {\n            background: '#1f1f1f',\n            opacity: 0.95,\n          },\n          '.g2-tooltip-title': {\n            color: '#A6A6A6',\n          },\n          '.g2-tooltip-list-item-name-label': {\n            color: '#A6A6A6',\n          },\n          '.g2-tooltip-list-item-value': {\n            color: '#A6A6A6',\n          },\n        },\n      },\n    },\n    options,\n  );\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,MAAM,QAAQ,UAAU;AAIjC,OAAO,MAAMC,MAAM,GAAG;EACpBC,UAAU,EAAE,MAAM;EAClBC,UAAU,EAAE,MAAM;EAClBC,WAAW,EAAE,SAAS;EACtBC,YAAY,EAAE,SAAS;EACvBC,eAAe,EAAE,aAAa;EAC9BC,UAAU,EAAE,CACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;EACDC,UAAU,EAAE,CACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;CACV;AAED,MAAMC,QAAQ,GAAGjB,MAAM,CAACC,MAAM,CAAC;AAE/B,OAAO,MAAMiB,IAAI,GAAqBC,OAAO,IAAI;EAC/C,OAAOpB,OAAO,CACZ,EAAE,EACFkB,QAAQ,EACR;IACEG,OAAO,EAAE;MACPC,gBAAgB,EAAE,MAAM;MACxBC,mBAAmB,EAAE,CAAC;MACtBC,uBAAuB,EAAE,IAAI;MAC7BC,GAAG,EAAE;QACH,aAAa,EAAE;UACbC,UAAU,EAAE,SAAS;UACrBC,OAAO,EAAE;SACV;QACD,mBAAmB,EAAE;UACnBC,KAAK,EAAE;SACR;QACD,kCAAkC,EAAE;UAClCA,KAAK,EAAE;SACR;QACD,6BAA6B,EAAE;UAC7BA,KAAK,EAAE;;;;GAId,EACDR,OAAO,CACR;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}