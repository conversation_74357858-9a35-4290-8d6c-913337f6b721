{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS,\n    expandIconMarginTop,\n    expandIconSize,\n    expandIconHalfInner,\n    expandIconScale,\n    calc\n  } = token;\n  const tableBorder = \"\".concat(unit(lineWidth), \" \").concat(lineType, \" \").concat(tableBorderColor);\n  const expandIconLineOffset = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-expand-icon-col\")]: {\n        width: tableExpandColumnWidth\n      },\n      [\"\".concat(componentCls, \"-row-expand-icon-cell\")]: {\n        textAlign: 'center',\n        [\"\".concat(componentCls, \"-row-expand-icon\")]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [\"\".concat(componentCls, \"-row-indent\")]: {\n        height: 1,\n        float: 'left'\n      },\n      [\"\".concat(componentCls, \"-row-expand-icon\")]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        width: expandIconSize,\n        height: expandIconSize,\n        color: 'inherit',\n        lineHeight: unit(expandIconSize),\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: \"scale(\".concat(expandIconScale, \")\"),\n        '&:focus, &:hover, &:active': {\n          borderColor: 'currentcolor'\n        },\n        '&::before, &::after': {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: \"transform \".concat(motionDurationSlow, \" ease-out\"),\n          content: '\"\"'\n        },\n        '&::before': {\n          top: expandIconHalfInner,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: expandIconHalfInner,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [\"\".concat(componentCls, \"-row-indent + \").concat(componentCls, \"-row-expand-icon\")]: {\n        marginTop: expandIconMarginTop,\n        marginInlineEnd: paddingXS\n      },\n      [\"tr\".concat(componentCls, \"-expanded-row\")]: {\n        '&, &:hover': {\n          '> th, > td': {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [\"\".concat(antCls, \"-descriptions-view\")]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: '100%'\n          }\n        }\n      },\n      // With fixed\n      [\"\".concat(componentCls, \"-expanded-row-fixed\")]: {\n        position: 'relative',\n        margin: \"\".concat(unit(calc(tablePaddingVertical).mul(-1).equal()), \" \").concat(unit(calc(tablePaddingHorizontal).mul(-1).equal())),\n        padding: \"\".concat(unit(tablePaddingVertical), \" \").concat(unit(tablePaddingHorizontal))\n      }\n    }\n  };\n};\nexport default genExpandStyle;", "map": {"version": 3, "names": ["unit", "operationUnit", "genExpandStyle", "token", "componentCls", "antCls", "motionDurationSlow", "lineWidth", "paddingXS", "lineType", "tableBorderColor", "tableExpandIconBg", "tableExpandColumnWidth", "borderRadius", "tablePaddingVertical", "tablePaddingHorizontal", "tableExpandedRowBg", "paddingXXS", "expandIconMarginTop", "expandIconSize", "expandIconHalfInner", "expandIconScale", "calc", "tableBorder", "concat", "expandIconLineOffset", "sub", "equal", "width", "textAlign", "display", "float", "verticalAlign", "height", "Object", "assign", "position", "color", "lineHeight", "background", "border", "transform", "borderColor", "transition", "content", "top", "insetInlineEnd", "insetInlineStart", "bottom", "visibility", "marginTop", "marginInlineEnd", "table", "flex", "margin", "mul", "padding"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/expand.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS,\n    expandIconMarginTop,\n    expandIconSize,\n    expandIconHalfInner,\n    expandIconScale,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        width: expandIconSize,\n        height: expandIconSize,\n        color: 'inherit',\n        lineHeight: unit(expandIconSize),\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${expandIconScale})`,\n        '&:focus, &:hover, &:active': {\n          borderColor: 'currentcolor'\n        },\n        '&::before, &::after': {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: expandIconHalfInner,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: expandIconHalfInner,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: expandIconMarginTop,\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          '> th, > td': {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: '100%'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(tablePaddingHorizontal).mul(-1).equal())}`,\n        padding: `${unit(tablePaddingVertical)} ${unit(tablePaddingHorizontal)}`\n      }\n    }\n  };\n};\nexport default genExpandStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,kBAAkB;IAClBC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,YAAY;IACZC,oBAAoB;IACpBC,sBAAsB;IACtBC,kBAAkB;IAClBC,UAAU;IACVC,mBAAmB;IACnBC,cAAc;IACdC,mBAAmB;IACnBC,eAAe;IACfC;EACF,CAAC,GAAGnB,KAAK;EACT,MAAMoB,WAAW,MAAAC,MAAA,CAAMxB,IAAI,CAACO,SAAS,CAAC,OAAAiB,MAAA,CAAIf,QAAQ,OAAAe,MAAA,CAAId,gBAAgB,CAAE;EACxE,MAAMe,oBAAoB,GAAGH,IAAI,CAACL,UAAU,CAAC,CAACS,GAAG,CAACnB,SAAS,CAAC,CAACoB,KAAK,CAAC,CAAC;EACpE,OAAO;IACL,IAAAH,MAAA,CAAIpB,YAAY,gBAAa;MAC3B,IAAAoB,MAAA,CAAIpB,YAAY,wBAAqB;QACnCwB,KAAK,EAAEhB;MACT,CAAC;MACD,IAAAY,MAAA,CAAIpB,YAAY,6BAA0B;QACxCyB,SAAS,EAAE,QAAQ;QACnB,IAAAL,MAAA,CAAIpB,YAAY,wBAAqB;UACnC0B,OAAO,EAAE,aAAa;UACtBC,KAAK,EAAE,MAAM;UACbC,aAAa,EAAE;QACjB;MACF,CAAC;MACD,IAAAR,MAAA,CAAIpB,YAAY,mBAAgB;QAC9B6B,MAAM,EAAE,CAAC;QACTF,KAAK,EAAE;MACT,CAAC;MACD,IAAAP,MAAA,CAAIpB,YAAY,wBAAqB8B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElC,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE;QAC1FiC,QAAQ,EAAE,UAAU;QACpBL,KAAK,EAAE,MAAM;QACbH,KAAK,EAAET,cAAc;QACrBc,MAAM,EAAEd,cAAc;QACtBkB,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAEtC,IAAI,CAACmB,cAAc,CAAC;QAChCoB,UAAU,EAAE5B,iBAAiB;QAC7B6B,MAAM,EAAEjB,WAAW;QACnBV,YAAY;QACZ4B,SAAS,WAAAjB,MAAA,CAAWH,eAAe,MAAG;QACtC,4BAA4B,EAAE;UAC5BqB,WAAW,EAAE;QACf,CAAC;QACD,qBAAqB,EAAE;UACrBN,QAAQ,EAAE,UAAU;UACpBG,UAAU,EAAE,cAAc;UAC1BI,UAAU,eAAAnB,MAAA,CAAelB,kBAAkB,cAAW;UACtDsC,OAAO,EAAE;QACX,CAAC;QACD,WAAW,EAAE;UACXC,GAAG,EAAEzB,mBAAmB;UACxB0B,cAAc,EAAErB,oBAAoB;UACpCsB,gBAAgB,EAAEtB,oBAAoB;UACtCQ,MAAM,EAAE1B;QACV,CAAC;QACD,UAAU,EAAE;UACVsC,GAAG,EAAEpB,oBAAoB;UACzBuB,MAAM,EAAEvB,oBAAoB;UAC5BsB,gBAAgB,EAAE3B,mBAAmB;UACrCQ,KAAK,EAAErB,SAAS;UAChBkC,SAAS,EAAE;QACb,CAAC;QACD;QACA,qBAAqB,EAAE;UACrBA,SAAS,EAAE;QACb,CAAC;QACD,oBAAoB,EAAE;UACpBA,SAAS,EAAE;QACb,CAAC;QACD,UAAU,EAAE;UACV,qBAAqB,EAAE;YACrBX,OAAO,EAAE,MAAM;YACfc,OAAO,EAAE;UACX,CAAC;UACDL,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACTS,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MACF,IAAAzB,MAAA,CAAIpB,YAAY,oBAAAoB,MAAA,CAAiBpB,YAAY,wBAAqB;QAChE8C,SAAS,EAAEhC,mBAAmB;QAC9BiC,eAAe,EAAE3C;MACnB,CAAC;MACD,MAAAgB,MAAA,CAAMpB,YAAY,qBAAkB;QAClC,YAAY,EAAE;UACZ,YAAY,EAAE;YACZmC,UAAU,EAAEvB;UACd;QACF,CAAC;QACD;QACA,IAAAQ,MAAA,CAAInB,MAAM,0BAAuB;UAC/ByB,OAAO,EAAE,MAAM;UACfsB,KAAK,EAAE;YACLC,IAAI,EAAE,MAAM;YACZzB,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD;MACA,IAAAJ,MAAA,CAAIpB,YAAY,2BAAwB;QACtCgC,QAAQ,EAAE,UAAU;QACpBkB,MAAM,KAAA9B,MAAA,CAAKxB,IAAI,CAACsB,IAAI,CAACR,oBAAoB,CAAC,CAACyC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC,CAAC,CAAC,OAAAH,MAAA,CAAIxB,IAAI,CAACsB,IAAI,CAACP,sBAAsB,CAAC,CAACwC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAE;QACnH6B,OAAO,KAAAhC,MAAA,CAAKxB,IAAI,CAACc,oBAAoB,CAAC,OAAAU,MAAA,CAAIxB,IAAI,CAACe,sBAAsB,CAAC;MACxE;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}