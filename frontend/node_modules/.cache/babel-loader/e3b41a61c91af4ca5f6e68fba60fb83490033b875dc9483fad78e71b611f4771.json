{"ast": null, "code": "const genSorterStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    fontSizeIcon,\n    headerIconColor,\n    headerIconHoverColor\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-thead th\").concat(componentCls, \"-column-has-sorters\")]: {\n        outline: 'none',\n        cursor: 'pointer',\n        // why left 0s? Avoid column header move with transition when left is changed\n        // https://github.com/ant-design/ant-design/issues/50588\n        transition: \"all \".concat(token.motionDurationSlow, \", left 0s\"),\n        '&:hover': {\n          background: token.tableHeaderSortHoverBg,\n          '&::before': {\n            backgroundColor: 'transparent !important'\n          }\n        },\n        '&:focus-visible': {\n          color: token.colorPrimary\n        },\n        // https://github.com/ant-design/ant-design/issues/30969\n        [\"\\n          &\".concat(componentCls, \"-cell-fix-left:hover,\\n          &\").concat(componentCls, \"-cell-fix-right:hover\\n        \")]: {\n          background: token.tableFixedHeaderSortActiveBg\n        }\n      },\n      [\"\".concat(componentCls, \"-thead th\").concat(componentCls, \"-column-sort\")]: {\n        background: token.tableHeaderSortBg,\n        '&::before': {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [\"td\".concat(componentCls, \"-column-sort\")]: {\n        background: token.tableBodySortBg\n      },\n      [\"\".concat(componentCls, \"-column-title\")]: {\n        position: 'relative',\n        zIndex: 1,\n        flex: 1,\n        minWidth: 0\n      },\n      [\"\".concat(componentCls, \"-column-sorters\")]: {\n        display: 'flex',\n        flex: 'auto',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          height: '100%',\n          content: '\"\"'\n        }\n      },\n      [\"\".concat(componentCls, \"-column-sorters-tooltip-target-sorter\")]: {\n        '&::after': {\n          content: 'none'\n        }\n      },\n      [\"\".concat(componentCls, \"-column-sorter\")]: {\n        marginInlineStart: marginXXS,\n        color: headerIconColor,\n        fontSize: 0,\n        transition: \"color \".concat(token.motionDurationSlow),\n        '&-inner': {\n          display: 'inline-flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        '&-up, &-down': {\n          fontSize: fontSizeIcon,\n          '&.active': {\n            color: token.colorPrimary\n          }\n        },\n        [\"\".concat(componentCls, \"-column-sorter-up + \").concat(componentCls, \"-column-sorter-down\")]: {\n          marginTop: '-0.3em'\n        }\n      },\n      [\"\".concat(componentCls, \"-column-sorters:hover \").concat(componentCls, \"-column-sorter\")]: {\n        color: headerIconHoverColor\n      }\n    }\n  };\n};\nexport default genSorterStyle;", "map": {"version": 3, "names": ["genSorterStyle", "token", "componentCls", "marginXXS", "fontSizeIcon", "headerIconColor", "headerIconHoverColor", "concat", "outline", "cursor", "transition", "motionDurationSlow", "background", "tableHeaderSortHoverBg", "backgroundColor", "color", "colorPrimary", "tableFixedHeaderSortActiveBg", "tableHeaderSortBg", "tableBodySortBg", "position", "zIndex", "flex", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "justifyContent", "inset", "width", "height", "content", "marginInlineStart", "fontSize", "flexDirection", "marginTop"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/sorter.js"], "sourcesContent": ["const genSorterStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    fontSizeIcon,\n    headerIconColor,\n    headerIconHoverColor\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-thead th${componentCls}-column-has-sorters`]: {\n        outline: 'none',\n        cursor: 'pointer',\n        // why left 0s? Avoid column header move with transition when left is changed\n        // https://github.com/ant-design/ant-design/issues/50588\n        transition: `all ${token.motionDurationSlow}, left 0s`,\n        '&:hover': {\n          background: token.tableHeaderSortHoverBg,\n          '&::before': {\n            backgroundColor: 'transparent !important'\n          }\n        },\n        '&:focus-visible': {\n          color: token.colorPrimary\n        },\n        // https://github.com/ant-design/ant-design/issues/30969\n        [`\n          &${componentCls}-cell-fix-left:hover,\n          &${componentCls}-cell-fix-right:hover\n        `]: {\n          background: token.tableFixedHeaderSortActiveBg\n        }\n      },\n      [`${componentCls}-thead th${componentCls}-column-sort`]: {\n        background: token.tableHeaderSortBg,\n        '&::before': {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`td${componentCls}-column-sort`]: {\n        background: token.tableBodySortBg\n      },\n      [`${componentCls}-column-title`]: {\n        position: 'relative',\n        zIndex: 1,\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-column-sorters`]: {\n        display: 'flex',\n        flex: 'auto',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          height: '100%',\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-column-sorters-tooltip-target-sorter`]: {\n        '&::after': {\n          content: 'none'\n        }\n      },\n      [`${componentCls}-column-sorter`]: {\n        marginInlineStart: marginXXS,\n        color: headerIconColor,\n        fontSize: 0,\n        transition: `color ${token.motionDurationSlow}`,\n        '&-inner': {\n          display: 'inline-flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        '&-up, &-down': {\n          fontSize: fontSizeIcon,\n          '&.active': {\n            color: token.colorPrimary\n          }\n        },\n        [`${componentCls}-column-sorter-up + ${componentCls}-column-sorter-down`]: {\n          marginTop: '-0.3em'\n        }\n      },\n      [`${componentCls}-column-sorters:hover ${componentCls}-column-sorter`]: {\n        color: headerIconHoverColor\n      }\n    }\n  };\n};\nexport default genSorterStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,eAAe;IACfC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,IAAAM,MAAA,CAAIL,YAAY,gBAAa;MAC3B,IAAAK,MAAA,CAAIL,YAAY,eAAAK,MAAA,CAAYL,YAAY,2BAAwB;QAC9DM,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,SAAS;QACjB;QACA;QACAC,UAAU,SAAAH,MAAA,CAASN,KAAK,CAACU,kBAAkB,cAAW;QACtD,SAAS,EAAE;UACTC,UAAU,EAAEX,KAAK,CAACY,sBAAsB;UACxC,WAAW,EAAE;YACXC,eAAe,EAAE;UACnB;QACF,CAAC;QACD,iBAAiB,EAAE;UACjBC,KAAK,EAAEd,KAAK,CAACe;QACf,CAAC;QACD;QACA,iBAAAT,MAAA,CACKL,YAAY,wCAAAK,MAAA,CACZL,YAAY,uCACb;UACFU,UAAU,EAAEX,KAAK,CAACgB;QACpB;MACF,CAAC;MACD,IAAAV,MAAA,CAAIL,YAAY,eAAAK,MAAA,CAAYL,YAAY,oBAAiB;QACvDU,UAAU,EAAEX,KAAK,CAACiB,iBAAiB;QACnC,WAAW,EAAE;UACXJ,eAAe,EAAE;QACnB;MACF,CAAC;MACD,MAAAP,MAAA,CAAML,YAAY,oBAAiB;QACjCU,UAAU,EAAEX,KAAK,CAACkB;MACpB,CAAC;MACD,IAAAZ,MAAA,CAAIL,YAAY,qBAAkB;QAChCkB,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC;MACD,IAAAhB,MAAA,CAAIL,YAAY,uBAAoB;QAClCsB,OAAO,EAAE,MAAM;QACfF,IAAI,EAAE,MAAM;QACZG,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/B,UAAU,EAAE;UACVN,QAAQ,EAAE,UAAU;UACpBO,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;QACX;MACF,CAAC;MACD,IAAAvB,MAAA,CAAIL,YAAY,6CAA0C;QACxD,UAAU,EAAE;UACV4B,OAAO,EAAE;QACX;MACF,CAAC;MACD,IAAAvB,MAAA,CAAIL,YAAY,sBAAmB;QACjC6B,iBAAiB,EAAE5B,SAAS;QAC5BY,KAAK,EAAEV,eAAe;QACtB2B,QAAQ,EAAE,CAAC;QACXtB,UAAU,WAAAH,MAAA,CAAWN,KAAK,CAACU,kBAAkB,CAAE;QAC/C,SAAS,EAAE;UACTa,OAAO,EAAE,aAAa;UACtBS,aAAa,EAAE,QAAQ;UACvBR,UAAU,EAAE;QACd,CAAC;QACD,cAAc,EAAE;UACdO,QAAQ,EAAE5B,YAAY;UACtB,UAAU,EAAE;YACVW,KAAK,EAAEd,KAAK,CAACe;UACf;QACF,CAAC;QACD,IAAAT,MAAA,CAAIL,YAAY,0BAAAK,MAAA,CAAuBL,YAAY,2BAAwB;UACzEgC,SAAS,EAAE;QACb;MACF,CAAC;MACD,IAAA3B,MAAA,CAAIL,YAAY,4BAAAK,MAAA,CAAyBL,YAAY,sBAAmB;QACtEa,KAAK,EAAET;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}