{"ast": null, "code": "export const NotificationPlacements = ['top', 'topLeft', 'topRight', 'bottom', 'bottomLeft', 'bottomRight'];", "map": {"version": 3, "names": ["NotificationPlacements"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/notification/interface.js"], "sourcesContent": ["export const NotificationPlacements = ['top', 'topLeft', 'topRight', 'bottom', 'bottomLeft', 'bottomRight'];"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}