{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/PermissionWrapper.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { Alert, Empty, Button } from 'antd';\nimport { LockOutlined, UserOutlined } from '@ant-design/icons';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// 权限常量定义\nexport const PERMISSIONS = {\n  // 用户管理权限\n  USER_VIEW: 'user:view',\n  USER_CREATE: 'user:create',\n  USER_EDIT: 'user:edit',\n  USER_DELETE: 'user:delete',\n  // 图书管理权限\n  BOOK_VIEW: 'book:view',\n  BOOK_CREATE: 'book:create',\n  BOOK_EDIT: 'book:edit',\n  BOOK_DELETE: 'book:delete',\n  // 借阅管理权限\n  BORROW_VIEW: 'borrow:view',\n  BORROW_APPROVE: 'borrow:approve',\n  BORROW_RETURN: 'borrow:return',\n  // 系统管理权限\n  SYSTEM_CONFIG: 'system:config',\n  SYSTEM_LOG: 'system:log',\n  SYSTEM_STATS: 'system:stats',\n  // 消息管理权限\n  MESSAGE_VIEW_ALL: 'message:view:all',\n  MESSAGE_REPLY: 'message:reply',\n  // 罚金管理权限\n  FINE_VIEW: 'fine:view',\n  FINE_MANAGE: 'fine:manage'\n};\n\n// 角色权限映射\nconst ROLE_PERMISSIONS = {\n  '管理员': [\n  // 管理员拥有所有权限\n  ...Object.values(PERMISSIONS)],\n  '用户': [\n  // 普通用户只有基本查看权限\n  PERMISSIONS.BOOK_VIEW, PERMISSIONS.BORROW_VIEW]\n};\nconst PermissionWrapper = ({\n  children,\n  permission,\n  role,\n  fallback = null,\n  showFallback = true,\n  fallbackType = 'empty',\n  fallbackMessage\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n\n  // 如果没有用户信息，不显示\n  if (!user) {\n    if (!showFallback) return null;\n    return fallback || /*#__PURE__*/_jsxDEV(Empty, {\n      image: /*#__PURE__*/_jsxDEV(LockOutlined, {\n        style: {\n          fontSize: 48,\n          color: '#bfbfbf'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 16\n      }, this),\n      description: \"\\u8BF7\\u5148\\u767B\\u5F55\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 检查角色权限\n  const hasRolePermission = () => {\n    if (!role) return true;\n    const requiredRoles = Array.isArray(role) ? role : [role];\n    return requiredRoles.includes(user.role);\n  };\n\n  // 检查具体权限\n  const hasSpecificPermission = () => {\n    if (!permission) return true;\n    const requiredPermissions = Array.isArray(permission) ? permission : [permission];\n    const userPermissions = ROLE_PERMISSIONS[user.role] || [];\n    return requiredPermissions.every(perm => userPermissions.includes(perm));\n  };\n\n  // 权限检查\n  if (!hasRolePermission() || !hasSpecificPermission()) {\n    if (!showFallback) return null;\n    if (fallback) return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: fallback\n    }, void 0, false);\n    const message = fallbackMessage || '您没有权限访问此功能';\n    switch (fallbackType) {\n      case 'alert':\n        return /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6743\\u9650\\u4E0D\\u8DB3\",\n          description: message,\n          type: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 19\n          }, this),\n          showIcon: true,\n          style: {\n            margin: '16px 0'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this);\n      case 'empty':\n        return /*#__PURE__*/_jsxDEV(Empty, {\n          image: /*#__PURE__*/_jsxDEV(LockOutlined, {\n            style: {\n              fontSize: 48,\n              color: '#bfbfbf'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 20\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 12,\n                color: '#8c8c8c'\n              },\n              children: [\"\\u5F53\\u524D\\u89D2\\u8272\\uFF1A\", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 42\n            }, this),\n            children: \"\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: fallback\n        }, void 0, false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// 权限检查 Hook\n_s(PermissionWrapper, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = PermissionWrapper;\nexport const usePermission = () => {\n  _s2();\n  const {\n    user\n  } = useAuth();\n  const hasPermission = permission => {\n    if (!user) return false;\n    const requiredPermissions = Array.isArray(permission) ? permission : [permission];\n    const userPermissions = ROLE_PERMISSIONS[user.role] || [];\n    return requiredPermissions.every(perm => userPermissions.includes(perm));\n  };\n  const hasRole = role => {\n    if (!user) return false;\n    const requiredRoles = Array.isArray(role) ? role : [role];\n    return requiredRoles.includes(user.role);\n  };\n  const isAdmin = () => hasRole('管理员');\n  const isUser = () => hasRole('用户');\n  return {\n    hasPermission,\n    hasRole,\n    isAdmin,\n    isUser,\n    user,\n    permissions: user ? ROLE_PERMISSIONS[user.role] || [] : []\n  };\n};\n_s2(usePermission, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\nexport default PermissionWrapper;\nvar _c;\n$RefreshReg$(_c, \"PermissionWrapper\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Empty", "<PERSON><PERSON>", "LockOutlined", "UserOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PERMISSIONS", "USER_VIEW", "USER_CREATE", "USER_EDIT", "USER_DELETE", "BOOK_VIEW", "BOOK_CREATE", "BOOK_EDIT", "BOOK_DELETE", "BORROW_VIEW", "BORROW_APPROVE", "BORROW_RETURN", "SYSTEM_CONFIG", "SYSTEM_LOG", "SYSTEM_STATS", "MESSAGE_VIEW_ALL", "MESSAGE_REPLY", "FINE_VIEW", "FINE_MANAGE", "ROLE_PERMISSIONS", "Object", "values", "PermissionWrapper", "children", "permission", "role", "fallback", "showFallback", "fallbackType", "fallbackMessage", "_s", "user", "image", "style", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "hasRolePermission", "requiredRoles", "Array", "isArray", "includes", "hasSpecificPermission", "requiredPermissions", "userPermissions", "every", "perm", "message", "type", "icon", "showIcon", "margin", "marginBottom", "_c", "usePermission", "_s2", "hasPermission", "hasRole", "isAdmin", "isUser", "permissions", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/PermissionWrapper.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Alert, Empty, Button } from 'antd';\nimport { LockOutlined, UserOutlined } from '@ant-design/icons';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface PermissionWrapperProps {\n  children: ReactNode;\n  permission?: string | string[];\n  role?: string | string[];\n  fallback?: ReactNode;\n  showFallback?: boolean;\n  fallbackType?: 'empty' | 'alert' | 'custom';\n  fallbackMessage?: string;\n}\n\n// 权限常量定义\nexport const PERMISSIONS = {\n  // 用户管理权限\n  USER_VIEW: 'user:view',\n  USER_CREATE: 'user:create',\n  USER_EDIT: 'user:edit',\n  USER_DELETE: 'user:delete',\n\n  // 图书管理权限\n  BOOK_VIEW: 'book:view',\n  BOOK_CREATE: 'book:create',\n  BOOK_EDIT: 'book:edit',\n  BOOK_DELETE: 'book:delete',\n\n  // 借阅管理权限\n  BORROW_VIEW: 'borrow:view',\n  BORROW_APPROVE: 'borrow:approve',\n  BORROW_RETURN: 'borrow:return',\n\n  // 系统管理权限\n  SYSTEM_CONFIG: 'system:config',\n  SYSTEM_LOG: 'system:log',\n  SYSTEM_STATS: 'system:stats',\n\n  // 消息管理权限\n  MESSAGE_VIEW_ALL: 'message:view:all',\n  MESSAGE_REPLY: 'message:reply',\n\n  // 罚金管理权限\n  FINE_VIEW: 'fine:view',\n  FINE_MANAGE: 'fine:manage',\n} as const;\n\n// 角色权限映射\nconst ROLE_PERMISSIONS: Record<string, string[]> = {\n  '管理员': [\n    // 管理员拥有所有权限\n    ...Object.values(PERMISSIONS)\n  ],\n  '用户': [\n    // 普通用户只有基本查看权限\n    PERMISSIONS.BOOK_VIEW,\n    PERMISSIONS.BORROW_VIEW,\n  ]\n};\n\nconst PermissionWrapper: React.FC<PermissionWrapperProps> = ({\n  children,\n  permission,\n  role,\n  fallback = null,\n  showFallback = true,\n  fallbackType = 'empty',\n  fallbackMessage,\n}): React.ReactElement | null => {\n  const { user } = useAuth();\n\n  // 如果没有用户信息，不显示\n  if (!user) {\n    if (!showFallback) return null;\n    return (fallback as React.ReactElement) || (\n      <Empty\n        image={<LockOutlined style={{ fontSize: 48, color: '#bfbfbf' }} />}\n        description=\"请先登录\"\n      />\n    );\n  }\n\n  // 检查角色权限\n  const hasRolePermission = () => {\n    if (!role) return true;\n\n    const requiredRoles = Array.isArray(role) ? role : [role];\n    return requiredRoles.includes(user.role);\n  };\n\n  // 检查具体权限\n  const hasSpecificPermission = () => {\n    if (!permission) return true;\n\n    const requiredPermissions = Array.isArray(permission) ? permission : [permission];\n    const userPermissions = ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [];\n\n    return requiredPermissions.every(perm => userPermissions.includes(perm));\n  };\n\n  // 权限检查\n  if (!hasRolePermission() || !hasSpecificPermission()) {\n    if (!showFallback) return null;\n\n    if (fallback) return <>{fallback}</>;\n\n    const message = fallbackMessage || '您没有权限访问此功能';\n\n    switch (fallbackType) {\n      case 'alert':\n        return (\n          <Alert\n            message=\"权限不足\"\n            description={message}\n            type=\"warning\"\n            icon={<LockOutlined />}\n            showIcon\n            style={{ margin: '16px 0' }}\n          />\n        );\n      case 'empty':\n        return (\n          <Empty\n            image={<LockOutlined style={{ fontSize: 48, color: '#bfbfbf' }} />}\n            description={\n              <div>\n                <div style={{ marginBottom: 8 }}>{message}</div>\n                <div style={{ fontSize: 12, color: '#8c8c8c' }}>\n                  当前角色：{user.role}\n                </div>\n              </div>\n            }\n          >\n            <Button type=\"primary\" icon={<UserOutlined />}>\n              联系管理员\n            </Button>\n          </Empty>\n        );\n      default:\n        return <>{fallback}</>;\n    }\n  }\n\n  return <>{children}</>;\n};\n\n// 权限检查 Hook\nexport const usePermission = () => {\n  const { user } = useAuth();\n\n  const hasPermission = (permission: string | string[]) => {\n    if (!user) return false;\n\n    const requiredPermissions = Array.isArray(permission) ? permission : [permission];\n    const userPermissions = ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [];\n\n    return requiredPermissions.every(perm => userPermissions.includes(perm));\n  };\n\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n\n    const requiredRoles = Array.isArray(role) ? role : [role];\n    return requiredRoles.includes(user.role);\n  };\n\n  const isAdmin = () => hasRole('管理员');\n  const isUser = () => hasRole('用户');\n\n  return {\n    hasPermission,\n    hasRole,\n    isAdmin,\n    isUser,\n    user,\n    permissions: user ? ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [] : []\n  };\n};\n\nexport default PermissionWrapper;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC3C,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYjD;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAE1B;EACAC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAE1B;EACAC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAE9B;EACAC,aAAa,EAAE,eAAe;EAC9BC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAE5B;EACAC,gBAAgB,EAAE,kBAAkB;EACpCC,aAAa,EAAE,eAAe;EAE9B;EACAC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE;AACf,CAAU;;AAEV;AACA,MAAMC,gBAA0C,GAAG;EACjD,KAAK,EAAE;EACL;EACA,GAAGC,MAAM,CAACC,MAAM,CAACrB,WAAW,CAAC,CAC9B;EACD,IAAI,EAAE;EACJ;EACAA,WAAW,CAACK,SAAS,EACrBL,WAAW,CAACS,WAAW;AAE3B,CAAC;AAED,MAAMa,iBAAmD,GAAGA,CAAC;EAC3DC,QAAQ;EACRC,UAAU;EACVC,IAAI;EACJC,QAAQ,GAAG,IAAI;EACfC,YAAY,GAAG,IAAI;EACnBC,YAAY,GAAG,OAAO;EACtBC;AACF,CAAC,KAAgC;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGpC,OAAO,CAAC,CAAC;;EAE1B;EACA,IAAI,CAACoC,IAAI,EAAE;IACT,IAAI,CAACJ,YAAY,EAAE,OAAO,IAAI;IAC9B,OAAQD,QAAQ,iBACd7B,OAAA,CAACN,KAAK;MACJyC,KAAK,eAAEnC,OAAA,CAACJ,YAAY;QAACwC,KAAK,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACnEC,WAAW,EAAC;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EACH;;EAEA;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAChB,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMiB,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACnB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IACzD,OAAOiB,aAAa,CAACG,QAAQ,CAACd,IAAI,CAACN,IAAI,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMqB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACtB,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMuB,mBAAmB,GAAGJ,KAAK,CAACC,OAAO,CAACpB,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;IACjF,MAAMwB,eAAe,GAAG7B,gBAAgB,CAACY,IAAI,CAACN,IAAI,CAAkC,IAAI,EAAE;IAE1F,OAAOsB,mBAAmB,CAACE,KAAK,CAACC,IAAI,IAAIF,eAAe,CAACH,QAAQ,CAACK,IAAI,CAAC,CAAC;EAC1E,CAAC;;EAED;EACA,IAAI,CAACT,iBAAiB,CAAC,CAAC,IAAI,CAACK,qBAAqB,CAAC,CAAC,EAAE;IACpD,IAAI,CAACnB,YAAY,EAAE,OAAO,IAAI;IAE9B,IAAID,QAAQ,EAAE,oBAAO7B,OAAA,CAAAE,SAAA;MAAAwB,QAAA,EAAGG;IAAQ,gBAAG,CAAC;IAEpC,MAAMyB,OAAO,GAAGtB,eAAe,IAAI,YAAY;IAE/C,QAAQD,YAAY;MAClB,KAAK,OAAO;QACV,oBACE/B,OAAA,CAACP,KAAK;UACJ6D,OAAO,EAAC,0BAAM;UACdX,WAAW,EAAEW,OAAQ;UACrBC,IAAI,EAAC,SAAS;UACdC,IAAI,eAAExD,OAAA,CAACJ,YAAY;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBe,QAAQ;UACRrB,KAAK,EAAE;YAAEsB,MAAM,EAAE;UAAS;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAEN,KAAK,OAAO;QACV,oBACE1C,OAAA,CAACN,KAAK;UACJyC,KAAK,eAAEnC,OAAA,CAACJ,YAAY;YAACwC,KAAK,EAAE;cAAEC,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnEC,WAAW,eACT3C,OAAA;YAAA0B,QAAA,gBACE1B,OAAA;cAAKoC,KAAK,EAAE;gBAAEuB,YAAY,EAAE;cAAE,CAAE;cAAAjC,QAAA,EAAE4B;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD1C,OAAA;cAAKoC,KAAK,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAZ,QAAA,GAAC,gCACzC,EAACQ,IAAI,CAACN,IAAI;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UAAAhB,QAAA,eAED1B,OAAA,CAACL,MAAM;YAAC4D,IAAI,EAAC,SAAS;YAACC,IAAI,eAAExD,OAAA,CAACH,YAAY;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAhB,QAAA,EAAC;UAE/C;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAEZ;QACE,oBAAO1C,OAAA,CAAAE,SAAA;UAAAwB,QAAA,EAAGG;QAAQ,gBAAG,CAAC;IAC1B;EACF;EAEA,oBAAO7B,OAAA,CAAAE,SAAA;IAAAwB,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAO,EAAA,CAtFMR,iBAAmD;EAAA,QAStC3B,OAAO;AAAA;AAAA8D,EAAA,GATpBnC,iBAAmD;AAuFzD,OAAO,MAAMoC,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAE5B;EAAK,CAAC,GAAGpC,OAAO,CAAC,CAAC;EAE1B,MAAMiE,aAAa,GAAIpC,UAA6B,IAAK;IACvD,IAAI,CAACO,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAMgB,mBAAmB,GAAGJ,KAAK,CAACC,OAAO,CAACpB,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;IACjF,MAAMwB,eAAe,GAAG7B,gBAAgB,CAACY,IAAI,CAACN,IAAI,CAAkC,IAAI,EAAE;IAE1F,OAAOsB,mBAAmB,CAACE,KAAK,CAACC,IAAI,IAAIF,eAAe,CAACH,QAAQ,CAACK,IAAI,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMW,OAAO,GAAIpC,IAAuB,IAAK;IAC3C,IAAI,CAACM,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAMW,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACnB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IACzD,OAAOiB,aAAa,CAACG,QAAQ,CAACd,IAAI,CAACN,IAAI,CAAC;EAC1C,CAAC;EAED,MAAMqC,OAAO,GAAGA,CAAA,KAAMD,OAAO,CAAC,KAAK,CAAC;EACpC,MAAME,MAAM,GAAGA,CAAA,KAAMF,OAAO,CAAC,IAAI,CAAC;EAElC,OAAO;IACLD,aAAa;IACbC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNhC,IAAI;IACJiC,WAAW,EAAEjC,IAAI,GAAGZ,gBAAgB,CAACY,IAAI,CAACN,IAAI,CAAkC,IAAI,EAAE,GAAG;EAC3F,CAAC;AACH,CAAC;AAACkC,GAAA,CA9BWD,aAAa;EAAA,QACP/D,OAAO;AAAA;AA+B1B,eAAe2B,iBAAiB;AAAC,IAAAmC,EAAA;AAAAQ,YAAA,CAAAR,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}