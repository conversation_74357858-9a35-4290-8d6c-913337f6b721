{"ast": null, "code": "/*!\n * @antv/g-plugin-dragndrop\n * @description A G plugin for Drag n Drop implemented with PointerEvents\n * @version 2.0.35\n * @date 5/9/2025, 8:20:14 AM\n * <AUTHOR>\n * @docs https://g.antv.antgroup.com/\n */\nimport _objectSpread from '@babel/runtime/helpers/objectSpread2';\nimport _classCallCheck from '@babel/runtime/helpers/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/createClass';\nimport _callSuper from '@babel/runtime/helpers/callSuper';\nimport _inherits from '@babel/runtime/helpers/inherits';\nimport { AbstractRendererPlugin } from '@antv/g-lite';\nimport _regeneratorRuntime from '@babel/runtime/helpers/regeneratorRuntime';\nimport _asyncToGenerator from '@babel/runtime/helpers/asyncToGenerator';\nimport { distanceSquareRoot } from '@antv/util';\nvar DragndropPlugin = /*#__PURE__*/function () {\n  function DragndropPlugin(dragndropPluginOptions) {\n    _classCallCheck(this, DragndropPlugin);\n    this.dragndropPluginOptions = dragndropPluginOptions;\n  }\n  return _createClass(DragndropPlugin, [{\n    key: \"apply\",\n    value: function apply(context) {\n      var _this = this;\n      var renderingService = context.renderingService,\n        renderingContext = context.renderingContext;\n      var document = renderingContext.root.ownerDocument;\n\n      // TODO: should we add an option like `draggable` to Canvas\n      var canvas = document.defaultView;\n      var handlePointerdown = function handlePointerdown(event) {\n        var target = event.target;\n        var isDocument = target === document;\n        var draggableEventTarget = isDocument && _this.dragndropPluginOptions.isDocumentDraggable ? document : target.closest && target.closest('[draggable=true]');\n\n        // `draggable` may be set on ancestor nodes:\n        // @see https://github.com/antvis/G/issues/1088\n        if (draggableEventTarget) {\n          // delay triggering dragstart event\n          var dragstartTriggered = false;\n          var dragstartTimeStamp = event.timeStamp;\n          var dragstartClientCoordinates = [event.clientX, event.clientY];\n          var currentDroppable = null;\n          var lastDragClientCoordinates = [event.clientX, event.clientY];\n          // @ts-ignore\n          // eslint-disable-next-line no-inner-declarations\n          var handlePointermove = /*#__PURE__*/function () {\n            var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(event) {\n              var timeElapsed, distanceMoved, point, elementsBelow, elementBelow, droppableBelow;\n              return _regeneratorRuntime().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    if (dragstartTriggered) {\n                      _context.next = 8;\n                      break;\n                    }\n                    timeElapsed = event.timeStamp - dragstartTimeStamp;\n                    distanceMoved = distanceSquareRoot([event.clientX, event.clientY], dragstartClientCoordinates); // check thresholds\n                    if (!(timeElapsed <= _this.dragndropPluginOptions.dragstartTimeThreshold || distanceMoved <= _this.dragndropPluginOptions.dragstartDistanceThreshold)) {\n                      _context.next = 5;\n                      break;\n                    }\n                    return _context.abrupt(\"return\");\n                  case 5:\n                    // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragstart_event\n                    event.type = 'dragstart';\n                    draggableEventTarget.dispatchEvent(event);\n                    dragstartTriggered = true;\n                  case 8:\n                    // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/drag_event\n                    event.type = 'drag';\n                    // @ts-ignore\n                    event.dx = event.clientX - lastDragClientCoordinates[0];\n                    // @ts-ignore\n                    event.dy = event.clientY - lastDragClientCoordinates[1];\n                    draggableEventTarget.dispatchEvent(event);\n                    lastDragClientCoordinates = [event.clientX, event.clientY];\n                    if (isDocument) {\n                      _context.next = 21;\n                      break;\n                    }\n                    point = _this.dragndropPluginOptions.overlap === 'pointer' ? [event.canvasX, event.canvasY] : target.getBounds().center;\n                    _context.next = 17;\n                    return document.elementsFromPoint(point[0], point[1]);\n                  case 17:\n                    elementsBelow = _context.sent;\n                    // prevent from picking the dragging element\n                    elementBelow = elementsBelow[elementsBelow.indexOf(target) + 1];\n                    droppableBelow = (elementBelow === null || elementBelow === void 0 ? void 0 : elementBelow.closest('[droppable=true]')) || (_this.dragndropPluginOptions.isDocumentDroppable ? document : null);\n                    if (currentDroppable !== droppableBelow) {\n                      if (currentDroppable) {\n                        // null when we were not over a droppable before this event\n                        // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragleave_event\n                        event.type = 'dragleave';\n                        event.target = currentDroppable;\n                        currentDroppable.dispatchEvent(event);\n                      }\n                      if (droppableBelow) {\n                        // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragleave_event\n                        event.type = 'dragenter';\n                        event.target = droppableBelow;\n                        droppableBelow.dispatchEvent(event);\n                      }\n                      currentDroppable = droppableBelow;\n                      if (currentDroppable) {\n                        // null if we're not coming over a droppable now\n                        // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragover_event\n                        event.type = 'dragover';\n                        event.target = currentDroppable;\n                        currentDroppable.dispatchEvent(event);\n                      }\n                    }\n                  case 21:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            }));\n            return function handlePointermove(_x) {\n              return _ref.apply(this, arguments);\n            };\n          }();\n          canvas.addEventListener('pointermove', handlePointermove);\n          var stopDragging = function stopDragging(originalPointerUpEvent) {\n            if (dragstartTriggered) {\n              // prevent click event being triggerd\n              // @see https://github.com/antvis/G/issues/1091\n              originalPointerUpEvent.detail = {\n                preventClick: true\n              };\n\n              // clone event first\n              var _event = originalPointerUpEvent.clone();\n\n              // drop should fire before dragend\n              // @see https://javascript.tutorialink.com/is-there-a-defined-ordering-between-dragend-and-drop-events/\n\n              if (currentDroppable) {\n                // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/drop_event\n                _event.type = 'drop';\n                _event.target = currentDroppable;\n                currentDroppable.dispatchEvent(_event);\n              }\n\n              // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragend_event\n              _event.type = 'dragend';\n              draggableEventTarget.dispatchEvent(_event);\n              dragstartTriggered = false;\n            }\n            canvas.removeEventListener('pointermove', handlePointermove);\n          };\n          target.addEventListener('pointerup', stopDragging, {\n            once: true\n          });\n          target.addEventListener('pointerupoutside', stopDragging, {\n            once: true\n          });\n        }\n      };\n      renderingService.hooks.init.tap(DragndropPlugin.tag, function () {\n        canvas.addEventListener('pointerdown', handlePointerdown);\n      });\n      renderingService.hooks.destroy.tap(DragndropPlugin.tag, function () {\n        canvas.removeEventListener('pointerdown', handlePointerdown);\n      });\n    }\n  }]);\n}();\nDragndropPlugin.tag = 'Dragndrop';\nvar Plugin = /*#__PURE__*/function (_AbstractRendererPlug) {\n  function Plugin() {\n    var _this;\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, Plugin);\n    _this = _callSuper(this, Plugin);\n    _this.name = 'dragndrop';\n    _this.options = options;\n    return _this;\n  }\n  _inherits(Plugin, _AbstractRendererPlug);\n  return _createClass(Plugin, [{\n    key: \"init\",\n    value: function init() {\n      this.addRenderingPlugin(new DragndropPlugin(_objectSpread({\n        overlap: 'pointer',\n        isDocumentDraggable: false,\n        isDocumentDroppable: false,\n        dragstartDistanceThreshold: 0,\n        dragstartTimeThreshold: 0\n      }, this.options)));\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.removeAllRenderingPlugins();\n    }\n  }, {\n    key: \"setOptions\",\n    value: function setOptions(options) {\n      Object.assign(this.plugins[0].dragndropPluginOptions, options);\n    }\n  }]);\n}(AbstractRendererPlugin);\nexport { Plugin };", "map": {"version": 3, "names": ["DragndropPlugin", "dragndropPluginOptions", "_classCallCheck", "_createClass", "key", "value", "apply", "context", "_this", "renderingService", "renderingContext", "document", "root", "ownerDocument", "canvas", "defaultView", "handlePointerdown", "event", "target", "isDocument", "draggableEventTarget", "isDocumentDraggable", "closest", "dragstartTriggered", "dragstartTimeStamp", "timeStamp", "dragstartClientCoordinates", "clientX", "clientY", "currentDroppable", "lastDragClientCoordinates", "handlePointermove", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "timeElapsed", "distanceMoved", "point", "elementsBelow", "elementBelow", "drop<PERSON><PERSON><PERSON><PERSON>", "wrap", "_callee$", "_context", "prev", "next", "distanceSquareRoot", "dragstartTimeThreshold", "dragstartDistanceThreshold", "abrupt", "type", "dispatchEvent", "dx", "dy", "overlap", "canvasX", "canvasY", "getBounds", "center", "elementsFromPoint", "sent", "indexOf", "isDocumentDroppable", "stop", "_x", "arguments", "addEventListener", "stopDragging", "originalPointerUpEvent", "detail", "preventClick", "_event", "clone", "removeEventListener", "once", "hooks", "init", "tap", "tag", "destroy", "Plugin", "_AbstractRendererPlug", "options", "length", "undefined", "_callSuper", "name", "_inherits", "addRenderingPlugin", "_objectSpread", "removeAllRenderingPlugins", "setOptions", "Object", "assign", "plugins", "AbstractRendererPlugin"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g-plugin-dragndrop/src/DragndropPlugin.ts", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g-plugin-dragndrop/src/index.ts"], "sourcesContent": ["import type {\n  DisplayObject,\n  FederatedPointerEvent,\n  IDocument,\n  RenderingPlugin,\n  RenderingPluginContext,\n} from '@antv/g-lite';\nimport { distanceSquareRoot } from '@antv/util';\nimport type { DragndropPluginOptions } from './interfaces';\n\nexport class DragndropPlugin implements RenderingPlugin {\n  static tag = 'Dragndrop';\n\n  constructor(public dragndropPluginOptions: DragndropPluginOptions) {}\n\n  apply(context: RenderingPluginContext) {\n    const { renderingService, renderingContext } = context;\n    const document = renderingContext.root.ownerDocument;\n\n    // TODO: should we add an option like `draggable` to Canvas\n    const canvas = document.defaultView;\n\n    const handlePointerdown = (event: FederatedPointerEvent) => {\n      const target = event.target as DisplayObject;\n      const isDocument = (target as unknown as IDocument) === document;\n\n      const draggableEventTarget =\n        isDocument && this.dragndropPluginOptions.isDocumentDraggable\n          ? document\n          : target.closest && target.closest('[draggable=true]');\n\n      // `draggable` may be set on ancestor nodes:\n      // @see https://github.com/antvis/G/issues/1088\n      if (draggableEventTarget) {\n        // delay triggering dragstart event\n        let dragstartTriggered = false;\n        const dragstartTimeStamp = event.timeStamp;\n        const dragstartClientCoordinates: [number, number] = [\n          event.clientX,\n          event.clientY,\n        ];\n\n        let currentDroppable = null;\n        let lastDragClientCoordinates = [event.clientX, event.clientY];\n        // @ts-ignore\n        // eslint-disable-next-line no-inner-declarations\n        const handlePointermove = async (event: FederatedPointerEvent) => {\n          if (!dragstartTriggered) {\n            const timeElapsed = event.timeStamp - dragstartTimeStamp;\n            const distanceMoved = distanceSquareRoot(\n              [event.clientX, event.clientY],\n              dragstartClientCoordinates,\n            );\n            // check thresholds\n            if (\n              timeElapsed <=\n                this.dragndropPluginOptions.dragstartTimeThreshold ||\n              distanceMoved <=\n                this.dragndropPluginOptions.dragstartDistanceThreshold\n            ) {\n              return;\n            }\n\n            // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragstart_event\n            event.type = 'dragstart';\n\n            draggableEventTarget.dispatchEvent(event);\n            dragstartTriggered = true;\n          }\n\n          // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/drag_event\n          event.type = 'drag';\n          // @ts-ignore\n          event.dx = event.clientX - lastDragClientCoordinates[0];\n          // @ts-ignore\n          event.dy = event.clientY - lastDragClientCoordinates[1];\n          draggableEventTarget.dispatchEvent(event);\n          lastDragClientCoordinates = [event.clientX, event.clientY];\n\n          if (!isDocument) {\n            const point =\n              this.dragndropPluginOptions.overlap === 'pointer'\n                ? [event.canvasX, event.canvasY]\n                : target.getBounds().center;\n            const elementsBelow = await document.elementsFromPoint(\n              point[0],\n              point[1],\n            );\n\n            // prevent from picking the dragging element\n            const elementBelow =\n              elementsBelow[elementsBelow.indexOf(target) + 1];\n\n            const droppableBelow =\n              elementBelow?.closest('[droppable=true]') ||\n              (this.dragndropPluginOptions.isDocumentDroppable\n                ? document\n                : null);\n            if (currentDroppable !== droppableBelow) {\n              if (currentDroppable) {\n                // null when we were not over a droppable before this event\n                // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragleave_event\n                event.type = 'dragleave';\n                event.target = currentDroppable;\n                currentDroppable.dispatchEvent(event);\n              }\n\n              if (droppableBelow) {\n                // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragleave_event\n                event.type = 'dragenter';\n                event.target = droppableBelow;\n                droppableBelow.dispatchEvent(event);\n              }\n\n              currentDroppable = droppableBelow;\n              if (currentDroppable) {\n                // null if we're not coming over a droppable now\n                // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragover_event\n                event.type = 'dragover';\n                event.target = currentDroppable;\n                currentDroppable.dispatchEvent(event);\n              }\n            }\n          }\n        };\n\n        canvas.addEventListener('pointermove', handlePointermove);\n\n        const stopDragging = function (\n          originalPointerUpEvent: FederatedPointerEvent,\n        ) {\n          if (dragstartTriggered) {\n            // prevent click event being triggerd\n            // @see https://github.com/antvis/G/issues/1091\n            originalPointerUpEvent.detail = {\n              preventClick: true,\n            };\n\n            // clone event first\n            const event = originalPointerUpEvent.clone();\n\n            // drop should fire before dragend\n            // @see https://javascript.tutorialink.com/is-there-a-defined-ordering-between-dragend-and-drop-events/\n\n            if (currentDroppable) {\n              // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/drop_event\n              event.type = 'drop';\n              event.target = currentDroppable;\n              currentDroppable.dispatchEvent(event);\n            }\n\n            // @see https://developer.mozilla.org/zh-CN/docs/Web/API/Document/dragend_event\n            event.type = 'dragend';\n            draggableEventTarget.dispatchEvent(event);\n\n            dragstartTriggered = false;\n          }\n\n          canvas.removeEventListener('pointermove', handlePointermove);\n        };\n\n        target.addEventListener('pointerup', stopDragging, { once: true });\n        target.addEventListener('pointerupoutside', stopDragging, {\n          once: true,\n        });\n      }\n    };\n\n    renderingService.hooks.init.tap(DragndropPlugin.tag, () => {\n      canvas.addEventListener('pointerdown', handlePointerdown);\n    });\n\n    renderingService.hooks.destroy.tap(DragndropPlugin.tag, () => {\n      canvas.removeEventListener('pointerdown', handlePointerdown);\n    });\n  }\n}\n", "import { AbstractRendererPlugin } from '@antv/g-lite';\nimport { DragndropPlugin } from './DragndropPlugin';\nimport type { DragndropPluginOptions } from './interfaces';\n\nexport class Plugin extends AbstractRendererPlugin {\n  name = 'dragndrop';\n\n  constructor(private options: Partial<DragndropPluginOptions> = {}) {\n    super();\n  }\n\n  init(): void {\n    this.addRenderingPlugin(\n      new DragndropPlugin({\n        overlap: 'pointer',\n        isDocumentDraggable: false,\n        isDocumentDroppable: false,\n        dragstartDistanceThreshold: 0,\n        dragstartTimeThreshold: 0,\n        ...this.options,\n      }),\n    );\n  }\n  destroy(): void {\n    this.removeAllRenderingPlugins();\n  }\n  setOptions(options: Partial<DragndropPluginOptions>): void {\n    Object.assign(\n      (this.plugins[0] as DragndropPlugin).dragndropPluginOptions,\n      options,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAUA,IAAaA,eAAe;EAG1B,SAAAA,gBAAmBC,sBAA8C,EAAE;IAAAC,eAAA,OAAAF,eAAA;IAAA,IAAhD,CAAAC,sBAA8C,GAA9CA,sBAA8C;EAAG;EAAC,OAAAE,YAAA,CAAAH,eAAA;IAAAI,GAAA;IAAAC,KAAA,EAErE,SAAAC,KAAKA,CAACC,OAA+B,EAAE;MAAA,IAAAC,KAAA;MACrC,IAAQC,gBAAgB,GAAuBF,OAAO,CAA9CE,gBAAgB;QAAEC,gBAAgB,GAAKH,OAAO,CAA5BG,gBAAgB;MAC1C,IAAMC,QAAQ,GAAGD,gBAAgB,CAACE,IAAI,CAACC,aAAa;;MAEpD;MACA,IAAMC,MAAM,GAAGH,QAAQ,CAACI,WAAW;MAEnC,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAA4B,EAAK;QAC1D,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAuB;QAC5C,IAAMC,UAAU,GAAID,MAAM,KAA8BP,QAAQ;QAEhE,IAAMS,oBAAoB,GACxBD,UAAU,IAAIX,KAAI,CAACP,sBAAsB,CAACoB,mBAAmB,GACzDV,QAAQ,GACRO,MAAM,CAACI,OAAO,IAAIJ,MAAM,CAACI,OAAO,CAAC,kBAAkB,CAAC;;QAE1D;QACA;QACA,IAAIF,oBAAoB,EAAE;UACxB;UACA,IAAIG,kBAAkB,GAAG,KAAK;UAC9B,IAAMC,kBAAkB,GAAGP,KAAK,CAACQ,SAAS;UAC1C,IAAMC,0BAA4C,GAAG,CACnDT,KAAK,CAACU,OAAO,EACbV,KAAK,CAACW,OAAO,CACd;UAED,IAAIC,gBAAgB,GAAG,IAAI;UAC3B,IAAIC,yBAAyB,GAAG,CAACb,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACW,OAAO,CAAC;UAC9D;UACA;UACA,IAAMG,iBAAiB;YAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAG,SAAAC,QAAOnB,KAA4B;cAAA,IAAAoB,WAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,cAAA;cAAA,OAAAR,mBAAA,GAAAS,IAAA,UAAAC,SAAAC,QAAA;gBAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;kBAAA;oBAAA,IACtDxB,kBAAkB;sBAAAsB,QAAA,CAAAE,IAAA;sBAAA;oBAAA;oBACfV,WAAW,GAAGpB,KAAK,CAACQ,SAAS,GAAGD,kBAAkB;oBAClDc,aAAa,GAAGU,kBAAkB,CACtC,CAAC/B,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACW,OAAO,CAAC,EAC9BF,0BACF,CAAC,CACD;oBAAA,MAEEW,WAAW,IACT7B,KAAI,CAACP,sBAAsB,CAACgD,sBAAsB,IACpDX,aAAa,IACX9B,KAAI,CAACP,sBAAsB,CAACiD,0BAA0B;sBAAAL,QAAA,CAAAE,IAAA;sBAAA;oBAAA;oBAAA,OAAAF,QAAA,CAAAM,MAAA;kBAAA;oBAK1D;oBACAlC,KAAK,CAACmC,IAAI,GAAG,WAAW;oBAExBhC,oBAAoB,CAACiC,aAAa,CAACpC,KAAK,CAAC;oBACzCM,kBAAkB,GAAG,IAAI;kBAAC;oBAG5B;oBACAN,KAAK,CAACmC,IAAI,GAAG,MAAM;oBACnB;oBACAnC,KAAK,CAACqC,EAAE,GAAGrC,KAAK,CAACU,OAAO,GAAGG,yBAAyB,CAAC,CAAC,CAAC;oBACvD;oBACAb,KAAK,CAACsC,EAAE,GAAGtC,KAAK,CAACW,OAAO,GAAGE,yBAAyB,CAAC,CAAC,CAAC;oBACvDV,oBAAoB,CAACiC,aAAa,CAACpC,KAAK,CAAC;oBACzCa,yBAAyB,GAAG,CAACb,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACW,OAAO,CAAC;oBAAC,IAEtDT,UAAU;sBAAA0B,QAAA,CAAAE,IAAA;sBAAA;oBAAA;oBACPR,KAAK,GACT/B,KAAI,CAACP,sBAAsB,CAACuD,OAAO,KAAK,SAAS,GAC7C,CAACvC,KAAK,CAACwC,OAAO,EAAExC,KAAK,CAACyC,OAAO,CAAC,GAC9BxC,MAAM,CAACyC,SAAS,EAAE,CAACC,MAAM;oBAAAf,QAAA,CAAAE,IAAA;oBAAA,OACHpC,QAAQ,CAACkD,iBAAiB,CACpDtB,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CACT,CAAC;kBAAA;oBAHKC,aAAa,GAAAK,QAAA,CAAAiB,IAAA;oBAKnB;oBACMrB,YAAY,GAChBD,aAAa,CAACA,aAAa,CAACuB,OAAO,CAAC7C,MAAM,CAAC,GAAG,CAAC,CAAC;oBAE5CwB,cAAc,GAClB,CAAAD,YAAY,aAAZA,YAAY,KAAZ,kBAAAA,YAAY,CAAEnB,OAAO,CAAC,kBAAkB,CAAC,MACxCd,KAAI,CAACP,sBAAsB,CAAC+D,mBAAmB,GAC5CrD,QAAQ,GACR,IAAI,CAAC;oBACX,IAAIkB,gBAAgB,KAAKa,cAAc,EAAE;sBACvC,IAAIb,gBAAgB,EAAE;wBACpB;wBACA;wBACAZ,KAAK,CAACmC,IAAI,GAAG,WAAW;wBACxBnC,KAAK,CAACC,MAAM,GAAGW,gBAAgB;wBAC/BA,gBAAgB,CAACwB,aAAa,CAACpC,KAAK,CAAC;sBACvC;sBAEA,IAAIyB,cAAc,EAAE;wBAClB;wBACAzB,KAAK,CAACmC,IAAI,GAAG,WAAW;wBACxBnC,KAAK,CAACC,MAAM,GAAGwB,cAAc;wBAC7BA,cAAc,CAACW,aAAa,CAACpC,KAAK,CAAC;sBACrC;sBAEAY,gBAAgB,GAAGa,cAAc;sBACjC,IAAIb,gBAAgB,EAAE;wBACpB;wBACA;wBACAZ,KAAK,CAACmC,IAAI,GAAG,UAAU;wBACvBnC,KAAK,CAACC,MAAM,GAAGW,gBAAgB;wBAC/BA,gBAAgB,CAACwB,aAAa,CAACpC,KAAK,CAAC;sBACvC;oBACF;kBAAC;kBAAA;oBAAA,OAAA4B,QAAA,CAAAoB,IAAA;gBAAA;cAAA,GAAA7B,OAAA;aAEJ;YAAA,OA9EK,SAAAL,iBAAiBA,CAAAmC,EAAA;cAAA,OAAAlC,IAAA,CAAA1B,KAAA,OAAA6D,SAAA;YAAA;WA8EtB;UAEDrD,MAAM,CAACsD,gBAAgB,CAAC,aAAa,EAAErC,iBAAiB,CAAC;UAEzD,IAAMsC,YAAY,GAAG,SAAfA,YAAYA,CAChBC,sBAA6C,EAC7C;YACA,IAAI/C,kBAAkB,EAAE;cACtB;cACA;cACA+C,sBAAsB,CAACC,MAAM,GAAG;gBAC9BC,YAAY,EAAE;eACf;;cAED;cACA,IAAMC,MAAK,GAAGH,sBAAsB,CAACI,KAAK,EAAE;;cAE5C;cACA;;cAEA,IAAI7C,gBAAgB,EAAE;gBACpB;gBACA4C,MAAK,CAACrB,IAAI,GAAG,MAAM;gBACnBqB,MAAK,CAACvD,MAAM,GAAGW,gBAAgB;gBAC/BA,gBAAgB,CAACwB,aAAa,CAACoB,MAAK,CAAC;cACvC;;cAEA;cACAA,MAAK,CAACrB,IAAI,GAAG,SAAS;cACtBhC,oBAAoB,CAACiC,aAAa,CAACoB,MAAK,CAAC;cAEzClD,kBAAkB,GAAG,KAAK;YAC5B;YAEAT,MAAM,CAAC6D,mBAAmB,CAAC,aAAa,EAAE5C,iBAAiB,CAAC;WAC7D;UAEDb,MAAM,CAACkD,gBAAgB,CAAC,WAAW,EAAEC,YAAY,EAAE;YAAEO,IAAI,EAAE;UAAK,CAAC,CAAC;UAClE1D,MAAM,CAACkD,gBAAgB,CAAC,kBAAkB,EAAEC,YAAY,EAAE;YACxDO,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;OACD;MAEDnE,gBAAgB,CAACoE,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC/E,eAAe,CAACgF,GAAG,EAAE,YAAM;QACzDlE,MAAM,CAACsD,gBAAgB,CAAC,aAAa,EAAEpD,iBAAiB,CAAC;MAC3D,CAAC,CAAC;MAEFP,gBAAgB,CAACoE,KAAK,CAACI,OAAO,CAACF,GAAG,CAAC/E,eAAe,CAACgF,GAAG,EAAE,YAAM;QAC5DlE,MAAM,CAAC6D,mBAAmB,CAAC,aAAa,EAAE3D,iBAAiB,CAAC;MAC9D,CAAC,CAAC;IACJ;EAAC;AAAA;AArKUhB,eAAe,CACnBgF,GAAG,GAAG,WAAW;ACPb,IAAAE,MAAM,0BAAAC,qBAAA;EAGjB,SAAAD,OAAA,EAAmE;IAAA,IAAA1E,KAAA;IAAA,IAA/C4E,OAAwC,GAAAjB,SAAA,CAAAkB,MAAA,QAAAlB,SAAA,QAAAmB,SAAA,GAAAnB,SAAA,MAAG,EAAE;IAAAjE,eAAA,OAAAgF,MAAA;IAC/D1E,KAAA,GAAA+E,UAAA,OAAAL,MAAA;IAAQ1E,KAAA,CAHVgF,IAAI,GAAG,WAAW;IAAAhF,KAAA,CAEE4E,OAAwC,GAAxCA,OAAwC;IAAA,OAAA5E,KAAA;EAE5D;EAACiF,SAAA,CAAAP,MAAA,EAAAC,qBAAA;EAAA,OAAAhF,YAAA,CAAA+E,MAAA;IAAA9E,GAAA;IAAAC,KAAA,EAED,SAAAyE,IAAIA,CAAA,EAAS;MACX,IAAI,CAACY,kBAAkB,CACrB,IAAI1F,eAAe,CAAA2F,aAAA;QACjBnC,OAAO,EAAE,SAAS;QAClBnC,mBAAmB,EAAE,KAAK;QAC1B2C,mBAAmB,EAAE,KAAK;QAC1Bd,0BAA0B,EAAE,CAAC;QAC7BD,sBAAsB,EAAE;MAAC,GACtB,IAAI,CAACmC,OAAO,CAChB,CACH,CAAC;IACH;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EACD,SAAA4E,OAAOA,CAAA,EAAS;MACd,IAAI,CAACW,yBAAyB,EAAE;IAClC;EAAC;IAAAxF,GAAA;IAAAC,KAAA,EACD,SAAAwF,UAAUA,CAACT,OAAwC,EAAQ;MACzDU,MAAM,CAACC,MAAM,CACV,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAqB/F,sBAAsB,EAC3DmF,OACF,CAAC;IACH;EAAC;AAAA,EA3ByBa,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}