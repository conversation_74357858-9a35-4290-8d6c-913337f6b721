{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHsbInputPrefixCls = \"\".concat(prefixCls, \"-hsb-input\");\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const hsbValue = value || internalValue;\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => \"\".concat(getRoundNumber(step || 0), \"%\"),\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => \"\".concat(getRoundNumber(step || 0), \"%\"),\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "getRoundNumber", "ColorSteppers", "ColorHsbInput", "_ref", "prefixCls", "value", "onChange", "colorHsbInputPrefixCls", "concat", "internalValue", "setInternalValue", "hsbValue", "handleHsbChange", "step", "type", "hsb", "toHsb", "genColor", "createElement", "className", "max", "min", "Number", "h", "formatter", "toString", "s", "b"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/color-picker/components/ColorHsbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const hsbValue = value || internalValue;\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,cAAc,QAAQ,SAAS;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGC,IAAA,IAIhB;EAAA,IAJiB;IACrBC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,sBAAsB,MAAAC,MAAA,CAAMJ,SAAS,eAAY;EACvD,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,MAAMC,aAAa,CAACM,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMM,QAAQ,GAAGN,KAAK,IAAII,aAAa;EACvC,MAAMG,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGA,IAAI,KAAK,GAAG,GAAGD,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,IAAI,GAAG;IACnD,MAAMI,QAAQ,GAAGlB,aAAa,CAACgB,GAAG,CAAC;IACnCL,gBAAgB,CAACO,QAAQ,CAAC;IAC1BX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAapB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEZ;EACb,CAAC,EAAE,aAAaV,KAAK,CAACqB,aAAa,CAACjB,aAAa,EAAE;IACjDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjCnB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCiB,SAAS,EAAEX,IAAI,IAAIb,cAAc,CAACa,IAAI,IAAI,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC;IACvDnB,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACqB,aAAa,CAACjB,aAAa,EAAE;IAClDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACU,CAAC,CAAC,GAAG,GAAG;IACvCtB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCiB,SAAS,EAAEX,IAAI,OAAAL,MAAA,CAAOR,cAAc,CAACa,IAAI,IAAI,CAAC,CAAC,MAAG;IAClDP,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACqB,aAAa,CAACjB,aAAa,EAAE;IAClDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACW,CAAC,CAAC,GAAG,GAAG;IACvCvB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCiB,SAAS,EAAEX,IAAI,OAAAL,MAAA,CAAOR,cAAc,CAACa,IAAI,IAAI,CAAC,CAAC,MAAG;IAClDP,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}