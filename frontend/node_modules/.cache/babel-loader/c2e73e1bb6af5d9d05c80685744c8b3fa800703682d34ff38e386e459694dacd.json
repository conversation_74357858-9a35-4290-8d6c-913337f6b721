{"ast": null, "code": "import { __assign, __extends, __read, __rest, __spreadArray } from \"tslib\";\nimport { fadeOut, onAnimateFinished, transition } from '../../animation';\nimport { Component } from '../../core';\nimport { classNames, distance, getCallbackValue, getPrimitiveAttributes, select } from '../../util';\nvar CLASS_NAMES = classNames({\n  lineGroup: 'line-group',\n  line: 'line',\n  regionGroup: 'region-group',\n  region: 'region'\n}, 'grid');\nfunction getStraightPath(points) {\n  return points.reduce(function (acc, curr, idx) {\n    acc.push(__spreadArray([idx === 0 ? 'M' : 'L'], __read(curr), false));\n    return acc;\n  }, []);\n}\nfunction getSurroundPath(points, attr, reversed) {\n  var _a = attr.connect,\n    connect = _a === void 0 ? 'line' : _a,\n    center = attr.center;\n  if (connect === 'line') return getStraightPath(points);\n  if (!center) return [];\n  var radius = distance(points[0], center);\n  var sweepFlag = reversed ? 0 : 1;\n  return points.reduce(function (r, p, idx) {\n    if (idx === 0) r.push(__spreadArray(['M'], __read(p), false));else r.push(__spreadArray(['A', radius, radius, 0, 0, sweepFlag], __read(p), false));\n    return r;\n  }, []);\n}\nfunction getLinePath(points, cfg, reversed) {\n  if (cfg.type === 'surround') return getSurroundPath(points, cfg, reversed);\n  return getStraightPath(points);\n}\nfunction connectPaths(from, to, cfg) {\n  var type = cfg.type,\n    connect = cfg.connect,\n    center = cfg.center,\n    closed = cfg.closed;\n  var closeFlag = closed ? [['Z']] : [];\n  var _a = __read([getLinePath(from, cfg), getLinePath(to.slice().reverse(), cfg, true)], 2),\n    path1 = _a[0],\n    path2 = _a[1];\n  var _b = __read([from[0], to.slice(-1)[0]], 2),\n    startOfFrom = _b[0],\n    endOfTo = _b[1];\n  var createPath = function (insertA, insertB) {\n    return [path1, insertA, path2, insertB, closeFlag].flat();\n  };\n  if (connect === 'line' || type === 'surround') {\n    return createPath([__spreadArray(['L'], __read(endOfTo), false)], [__spreadArray(['L'], __read(startOfFrom), false)]);\n  }\n  if (!center) throw new Error('Arc grid need to specified center');\n  var _c = __read([distance(endOfTo, center), distance(startOfFrom, center)], 2),\n    raduis1 = _c[0],\n    radius2 = _c[1];\n  return createPath([__spreadArray(['A', raduis1, raduis1, 0, 0, 1], __read(endOfTo), false), __spreadArray(['L'], __read(endOfTo), false)], [__spreadArray(['A', radius2, radius2, 0, 0, 0], __read(startOfFrom), false), __spreadArray(['L'], __read(startOfFrom), false)]);\n}\nfunction renderGridLine(container, data, attr, style) {\n  var animate = attr.animate,\n    isBillboard = attr.isBillboard;\n  var lines = data.map(function (item, idx) {\n    return {\n      id: item.id || \"grid-line-\".concat(idx),\n      d: getLinePath(item.points, attr)\n    };\n  });\n  return container.selectAll(CLASS_NAMES.line.class).data(lines, function (d) {\n    return d.id;\n  }).join(function (enter) {\n    return enter.append('path').each(function (datum, index) {\n      var lineStyle = getCallbackValue(getPrimitiveAttributes(__assign({\n        d: datum.d\n      }, style)), [datum, index, lines]);\n      this.attr(__assign({\n        class: CLASS_NAMES.line.name,\n        stroke: '#D9D9D9',\n        lineWidth: 1,\n        lineDash: [4, 4],\n        isBillboard: isBillboard\n      }, lineStyle));\n    });\n  }, function (update) {\n    return update.transition(function (datum, index) {\n      var lineStyle = getCallbackValue(getPrimitiveAttributes(__assign({\n        d: datum.d\n      }, style)), [datum, index, lines]);\n      return transition(this, lineStyle, animate.update);\n    });\n  }, function (exit) {\n    return exit.transition(function () {\n      var _this = this;\n      var animation = fadeOut(this, animate.exit);\n      onAnimateFinished(animation, function () {\n        return _this.remove();\n      });\n      return animation;\n    });\n  }).transitions();\n}\nfunction renderAlternateRegion(container, data, style) {\n  var animate = style.animate,\n    connect = style.connect,\n    areaFill = style.areaFill;\n  if (data.length < 2 || !areaFill || !connect) return [];\n  var colors = Array.isArray(areaFill) ? areaFill : [areaFill, 'transparent'];\n  var getColor = function (idx) {\n    return colors[idx % colors.length];\n  };\n  var regions = [];\n  for (var idx = 0; idx < data.length - 1; idx++) {\n    var _a = __read([data[idx].points, data[idx + 1].points], 2),\n      prev = _a[0],\n      curr = _a[1];\n    var path = connectPaths(prev, curr, style);\n    regions.push({\n      d: path,\n      fill: getColor(idx)\n    });\n  }\n  return container.selectAll(CLASS_NAMES.region.class).data(regions, function (_, i) {\n    return i;\n  }).join(function (enter) {\n    return enter.append('path').each(function (datum, index) {\n      var regionStyle = getCallbackValue(datum, [datum, index, regions]);\n      this.attr(regionStyle);\n    }).attr('className', CLASS_NAMES.region.name);\n  }, function (update) {\n    return update.transition(function (datum, index) {\n      var regionStyle = getCallbackValue(datum, [datum, index, regions]);\n      return transition(this, regionStyle, animate.update);\n    });\n  }, function (exit) {\n    return exit.transition(function () {\n      var _this = this;\n      var animation = fadeOut(this, animate.exit);\n      onAnimateFinished(animation, function () {\n        return _this.remove();\n      });\n      return animation;\n    });\n  }).transitions();\n}\nfunction getData(attr) {\n  var _a = attr.data,\n    data = _a === void 0 ? [] : _a,\n    closed = attr.closed;\n  if (!closed) return data;\n  return data.map(function (datum) {\n    var points = datum.points;\n    var _a = __read(points, 1),\n      start = _a[0];\n    return __assign(__assign({}, datum), {\n      points: __spreadArray(__spreadArray([], __read(points), false), [start], false)\n    });\n  });\n}\nvar Grid = /** @class */function (_super) {\n  __extends(Grid, _super);\n  function Grid() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Grid.prototype.render = function (attributes, container) {\n    // @ts-ignore do no passBy className\n    var type = attributes.type,\n      center = attributes.center,\n      areaFill = attributes.areaFill,\n      closed = attributes.closed,\n      style = __rest(attributes, [\"type\", \"center\", \"areaFill\", \"closed\"]);\n    var data = getData(attributes);\n    var lineGroup = select(container).maybeAppendByClassName(CLASS_NAMES.lineGroup, 'g');\n    var regionGroup = select(container).maybeAppendByClassName(CLASS_NAMES.regionGroup, 'g');\n    var lineTransitions = renderGridLine(lineGroup, data, attributes, style);\n    var reigionTransitions = renderAlternateRegion(regionGroup, data, attributes);\n    return __spreadArray(__spreadArray([], __read(lineTransitions), false), __read(reigionTransitions), false);\n  };\n  return Grid;\n}(Component);\nexport { Grid };", "map": {"version": 3, "names": ["fadeOut", "onAnimateFinished", "transition", "Component", "classNames", "distance", "getCallbackValue", "getPrimitiveAttributes", "select", "CLASS_NAMES", "lineGroup", "line", "regionGroup", "region", "getStraightPath", "points", "reduce", "acc", "curr", "idx", "push", "__spread<PERSON><PERSON>y", "__read", "getSurroundPath", "attr", "reversed", "_a", "connect", "center", "radius", "sweepFlag", "r", "p", "get<PERSON>inePath", "cfg", "type", "connectPaths", "from", "to", "closed", "closeFlag", "slice", "reverse", "path1", "path2", "_b", "startOfFrom", "endOfTo", "createPath", "insertA", "insertB", "flat", "Error", "_c", "raduis1", "radius2", "renderGridLine", "container", "data", "style", "animate", "isBillboard", "lines", "map", "item", "id", "concat", "d", "selectAll", "class", "join", "enter", "append", "each", "datum", "index", "lineStyle", "__assign", "name", "stroke", "lineWidth", "lineDash", "update", "exit", "_this", "animation", "remove", "transitions", "renderAlternateRegion", "areaFill", "length", "colors", "Array", "isArray", "getColor", "regions", "prev", "path", "fill", "_", "i", "regionStyle", "getData", "start", "Grid", "_super", "__extends", "prototype", "render", "attributes", "__rest", "maybeAppendByClassName", "lineTransitions", "reigionTransitions"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/component/src/ui/grid/index.ts"], "sourcesContent": ["import { fadeOut, onAnimateFinished, transition } from '../../animation';\nimport { Component } from '../../core';\nimport type { Group } from '../../shapes';\nimport type { Point } from '../../types';\nimport type { PathCommand, Selection } from '../../util';\nimport { classNames, distance, getCallbackValue, getPrimitiveAttributes, select } from '../../util';\nimport type { GridOptions, GridStyle, GridStyleProps } from './types';\n\nexport type { GridOptions, GridStyleProps };\n\nconst CLASS_NAMES = classNames(\n  {\n    lineGroup: 'line-group',\n    line: 'line',\n    regionGroup: 'region-group',\n    region: 'region',\n  },\n  'grid'\n);\n\nfunction getStraightPath(points: Point[]) {\n  return points.reduce((acc, curr, idx) => {\n    acc.push([idx === 0 ? 'M' : 'L', ...curr]);\n    return acc;\n  }, [] as PathCommand[]);\n}\n\nfunction getSurroundPath(points: Point[], attr: GridStyleProps, reversed?: boolean) {\n  const { connect = 'line', center } = attr;\n  if (connect === 'line') return getStraightPath(points);\n  if (!center) return [];\n  const radius = distance(points[0], center);\n  const sweepFlag = reversed ? 0 : 1;\n  return points.reduce((r, p, idx) => {\n    if (idx === 0) r.push(['M', ...p]);\n    else r.push(['A', radius, radius, 0, 0, sweepFlag, ...p]);\n    return r;\n  }, [] as PathCommand[]);\n}\n\nfunction getLinePath(points: Point[], cfg: GridStyleProps, reversed?: boolean) {\n  if (cfg.type === 'surround') return getSurroundPath(points, cfg, reversed);\n  return getStraightPath(points);\n}\n\nfunction connectPaths(from: Point[], to: Point[], cfg: GridStyleProps) {\n  const { type, connect, center, closed } = cfg;\n  const closeFlag: PathCommand[] = closed ? [['Z']] : [];\n  const [path1, path2] = [getLinePath(from, cfg), getLinePath(to.slice().reverse(), cfg, true)];\n  const [startOfFrom, endOfTo] = [from[0], to.slice(-1)[0]];\n  const createPath = (insertA: PathCommand[], insertB: PathCommand[]): PathCommand[] =>\n    [path1, insertA, path2, insertB, closeFlag].flat();\n\n  if (connect === 'line' || type === 'surround') {\n    return createPath([['L', ...endOfTo]], [['L', ...startOfFrom]]);\n  }\n  if (!center) throw new Error('Arc grid need to specified center');\n\n  const [raduis1, radius2] = [distance(endOfTo, center), distance(startOfFrom, center)];\n  return createPath(\n    [\n      ['A', raduis1, raduis1, 0, 0, 1, ...endOfTo],\n      ['L', ...endOfTo],\n    ],\n    [\n      ['A', radius2, radius2, 0, 0, 0, ...startOfFrom],\n      ['L', ...startOfFrom],\n    ]\n  );\n}\n\nfunction renderGridLine(\n  container: Selection<Group>,\n  data: GridStyleProps['data'],\n  attr: GridStyleProps,\n  style: GridStyle\n) {\n  const { animate, isBillboard } = attr;\n  const lines = data.map((item, idx) => {\n    return {\n      id: item.id || `grid-line-${idx}`,\n      d: getLinePath(item.points, attr),\n    };\n  });\n  return container\n    .selectAll(CLASS_NAMES.line.class)\n    .data(lines, (d) => d.id)\n    .join(\n      (enter) =>\n        enter.append('path').each(function (datum, index) {\n          const lineStyle = getCallbackValue(\n            getPrimitiveAttributes({\n              d: datum.d,\n              ...style,\n            }),\n            [datum, index, lines]\n          );\n          this.attr({\n            class: CLASS_NAMES.line.name,\n            stroke: '#D9D9D9',\n            lineWidth: 1,\n            lineDash: [4, 4],\n            isBillboard,\n            ...lineStyle,\n          });\n        }),\n      (update) =>\n        update.transition(function (datum, index) {\n          const lineStyle = getCallbackValue(\n            getPrimitiveAttributes({\n              d: datum.d,\n              ...style,\n            }),\n            [datum, index, lines]\n          );\n          return transition(this, lineStyle, animate.update);\n        }),\n      (exit) =>\n        exit.transition(function () {\n          const animation = fadeOut(this, animate.exit);\n          onAnimateFinished(animation, () => this.remove());\n          return animation;\n        })\n    )\n    .transitions();\n}\n\nfunction renderAlternateRegion(container: Selection<Group>, data: GridStyleProps['data'], style: GridStyleProps) {\n  const { animate, connect, areaFill } = style;\n  if (data.length < 2 || !areaFill || !connect) return [];\n  const colors: string[] = Array.isArray(areaFill) ? areaFill : [areaFill, 'transparent'];\n  const getColor = (idx: number) => colors[idx % colors.length];\n  const regions: any[] = [];\n  for (let idx = 0; idx < data.length - 1; idx++) {\n    const [prev, curr] = [data[idx].points, data[idx + 1].points];\n    const path = connectPaths(prev, curr, style);\n    regions.push({ d: path, fill: getColor(idx) });\n  }\n\n  return container\n    .selectAll(CLASS_NAMES.region.class)\n    .data(regions, (_, i) => i)\n    .join(\n      (enter) =>\n        enter\n          .append('path')\n          .each(function (datum, index) {\n            const regionStyle = getCallbackValue(datum, [datum, index, regions]);\n            this.attr(regionStyle);\n          })\n          .attr('className', CLASS_NAMES.region.name),\n      (update) =>\n        update.transition(function (datum, index) {\n          const regionStyle = getCallbackValue(datum, [datum, index, regions]);\n          return transition(this, regionStyle, animate.update);\n        }),\n      (exit) =>\n        exit.transition(function () {\n          const animation = fadeOut(this, animate.exit);\n          onAnimateFinished(animation, () => this.remove());\n          return animation;\n        })\n    )\n    .transitions();\n}\n\nfunction getData(attr: GridStyleProps) {\n  const { data = [], closed } = attr;\n  if (!closed) return data;\n  return data.map((datum) => {\n    const { points } = datum;\n    const [start] = points;\n    return { ...datum, points: [...points, start] };\n  });\n}\n\nexport class Grid extends Component<GridStyleProps> {\n  render(attributes: GridStyleProps, container: Group) {\n    // @ts-ignore do no passBy className\n    const { type, center, areaFill, closed, ...style } = attributes;\n    const data = getData(attributes);\n    const lineGroup = select(container).maybeAppendByClassName(CLASS_NAMES.lineGroup, 'g');\n    const regionGroup = select(container).maybeAppendByClassName(CLASS_NAMES.regionGroup, 'g');\n    const lineTransitions = renderGridLine(lineGroup, data, attributes, style);\n    const reigionTransitions = renderAlternateRegion(regionGroup, data, attributes);\n    return [...lineTransitions, ...reigionTransitions];\n  }\n}\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,iBAAiB;AACxE,SAASC,SAAS,QAAQ,YAAY;AAItC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,MAAM,QAAQ,YAAY;AAKnG,IAAMC,WAAW,GAAGL,UAAU,CAC5B;EACEM,SAAS,EAAE,YAAY;EACvBC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE;CACT,EACD,MAAM,CACP;AAED,SAASC,eAAeA,CAACC,MAAe;EACtC,OAAOA,MAAM,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAEC,GAAG;IAClCF,GAAG,CAACG,IAAI,CAAAC,aAAA,EAAEF,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAAG,MAAA,CAAKJ,IAAI,UAAE;IAC1C,OAAOD,GAAG;EACZ,CAAC,EAAE,EAAmB,CAAC;AACzB;AAEA,SAASM,eAAeA,CAACR,MAAe,EAAES,IAAoB,EAAEC,QAAkB;EACxE,IAAAC,EAAA,GAA6BF,IAAI,CAAAG,OAAjB;IAAhBA,OAAO,GAAAD,EAAA,cAAG,MAAM,GAAAA,EAAA;IAAEE,MAAM,GAAKJ,IAAI,CAAAI,MAAT;EAChC,IAAID,OAAO,KAAK,MAAM,EAAE,OAAOb,eAAe,CAACC,MAAM,CAAC;EACtD,IAAI,CAACa,MAAM,EAAE,OAAO,EAAE;EACtB,IAAMC,MAAM,GAAGxB,QAAQ,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEa,MAAM,CAAC;EAC1C,IAAME,SAAS,GAAGL,QAAQ,GAAG,CAAC,GAAG,CAAC;EAClC,OAAOV,MAAM,CAACC,MAAM,CAAC,UAACe,CAAC,EAAEC,CAAC,EAAEb,GAAG;IAC7B,IAAIA,GAAG,KAAK,CAAC,EAAEY,CAAC,CAACX,IAAI,CAAAC,aAAA,EAAE,GAAG,GAAAC,MAAA,CAAKU,CAAC,UAAE,CAAC,KAC9BD,CAAC,CAACX,IAAI,CAAAC,aAAA,EAAE,GAAG,EAAEQ,MAAM,EAAEA,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEC,SAAS,GAAAR,MAAA,CAAKU,CAAC,UAAE;IACzD,OAAOD,CAAC;EACV,CAAC,EAAE,EAAmB,CAAC;AACzB;AAEA,SAASE,WAAWA,CAAClB,MAAe,EAAEmB,GAAmB,EAAET,QAAkB;EAC3E,IAAIS,GAAG,CAACC,IAAI,KAAK,UAAU,EAAE,OAAOZ,eAAe,CAACR,MAAM,EAAEmB,GAAG,EAAET,QAAQ,CAAC;EAC1E,OAAOX,eAAe,CAACC,MAAM,CAAC;AAChC;AAEA,SAASqB,YAAYA,CAACC,IAAa,EAAEC,EAAW,EAAEJ,GAAmB;EAC3D,IAAAC,IAAI,GAA8BD,GAAG,CAAAC,IAAjC;IAAER,OAAO,GAAqBO,GAAG,CAAAP,OAAxB;IAAEC,MAAM,GAAaM,GAAG,CAAAN,MAAhB;IAAEW,MAAM,GAAKL,GAAG,CAAAK,MAAR;EACrC,IAAMC,SAAS,GAAkBD,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;EAChD,IAAAb,EAAA,GAAAJ,MAAA,CAAiB,CAACW,WAAW,CAACI,IAAI,EAAEH,GAAG,CAAC,EAAED,WAAW,CAACK,EAAE,CAACG,KAAK,EAAE,CAACC,OAAO,EAAE,EAAER,GAAG,EAAE,IAAI,CAAC,CAAC;IAAtFS,KAAK,GAAAjB,EAAA;IAAEkB,KAAK,GAAAlB,EAAA,GAA0E;EACvF,IAAAmB,EAAA,GAAAvB,MAAA,CAAyB,CAACe,IAAI,CAAC,CAAC,CAAC,EAAEC,EAAE,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAlDK,WAAW,GAAAD,EAAA;IAAEE,OAAO,GAAAF,EAAA,GAA8B;EACzD,IAAMG,UAAU,GAAG,SAAAA,CAACC,OAAsB,EAAEC,OAAsB;IAChE,QAACP,KAAK,EAAEM,OAAO,EAAEL,KAAK,EAAEM,OAAO,EAAEV,SAAS,CAAC,CAACW,IAAI,EAAE;EAAlD,CAAkD;EAEpD,IAAIxB,OAAO,KAAK,MAAM,IAAIQ,IAAI,KAAK,UAAU,EAAE;IAC7C,OAAOa,UAAU,CAAC,CAAA3B,aAAA,EAAE,GAAG,GAAAC,MAAA,CAAKyB,OAAO,UAAE,EAAE,CAAA1B,aAAA,EAAE,GAAG,GAAAC,MAAA,CAAKwB,WAAW,UAAE,CAAC;EACjE;EACA,IAAI,CAAClB,MAAM,EAAE,MAAM,IAAIwB,KAAK,CAAC,mCAAmC,CAAC;EAE3D,IAAAC,EAAA,GAAA/B,MAAA,CAAqB,CAACjB,QAAQ,CAAC0C,OAAO,EAAEnB,MAAM,CAAC,EAAEvB,QAAQ,CAACyC,WAAW,EAAElB,MAAM,CAAC,CAAC;IAA9E0B,OAAO,GAAAD,EAAA;IAAEE,OAAO,GAAAF,EAAA,GAA8D;EACrF,OAAOL,UAAU,CACf,C,eACG,GAAG,EAAEM,OAAO,EAAEA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAAhC,MAAA,CAAKyB,OAAO,W,eAC1C,GAAG,GAAAzB,MAAA,CAAKyB,OAAO,UACjB,EACD,C,eACG,GAAG,EAAEQ,OAAO,EAAEA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAAjC,MAAA,CAAKwB,WAAW,W,eAC9C,GAAG,GAAAxB,MAAA,CAAKwB,WAAW,UACrB,CACF;AACH;AAEA,SAASU,cAAcA,CACrBC,SAA2B,EAC3BC,IAA4B,EAC5BlC,IAAoB,EACpBmC,KAAgB;EAER,IAAAC,OAAO,GAAkBpC,IAAI,CAAAoC,OAAtB;IAAEC,WAAW,GAAKrC,IAAI,CAAAqC,WAAT;EAC5B,IAAMC,KAAK,GAAGJ,IAAI,CAACK,GAAG,CAAC,UAACC,IAAI,EAAE7C,GAAG;IAC/B,OAAO;MACL8C,EAAE,EAAED,IAAI,CAACC,EAAE,IAAI,aAAAC,MAAA,CAAa/C,GAAG,CAAE;MACjCgD,CAAC,EAAElC,WAAW,CAAC+B,IAAI,CAACjD,MAAM,EAAES,IAAI;KACjC;EACH,CAAC,CAAC;EACF,OAAOiC,SAAS,CACbW,SAAS,CAAC3D,WAAW,CAACE,IAAI,CAAC0D,KAAK,CAAC,CACjCX,IAAI,CAACI,KAAK,EAAE,UAACK,CAAC;IAAK,OAAAA,CAAC,CAACF,EAAE;EAAJ,CAAI,CAAC,CACxBK,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CAACC,MAAM,CAAC,MAAM,CAAC,CAACC,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK;MAC9C,IAAMC,SAAS,GAAGtE,gBAAgB,CAChCC,sBAAsB,CAAAsE,QAAA;QACpBV,CAAC,EAAEO,KAAK,CAACP;MAAC,GACPR,KAAK,EACR,EACF,CAACe,KAAK,EAAEC,KAAK,EAAEb,KAAK,CAAC,CACtB;MACD,IAAI,CAACtC,IAAI,CAAAqD,QAAA;QACPR,KAAK,EAAE5D,WAAW,CAACE,IAAI,CAACmE,IAAI;QAC5BC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAChBpB,WAAW,EAAAA;MAAA,GACRe,SAAS,EACZ;IACJ,CAAC,CAAC;EAhBF,CAgBE,EACJ,UAACM,MAAM;IACL,OAAAA,MAAM,CAAChF,UAAU,CAAC,UAAUwE,KAAK,EAAEC,KAAK;MACtC,IAAMC,SAAS,GAAGtE,gBAAgB,CAChCC,sBAAsB,CAAAsE,QAAA;QACpBV,CAAC,EAAEO,KAAK,CAACP;MAAC,GACPR,KAAK,EACR,EACF,CAACe,KAAK,EAAEC,KAAK,EAAEb,KAAK,CAAC,CACtB;MACD,OAAO5D,UAAU,CAAC,IAAI,EAAE0E,SAAS,EAAEhB,OAAO,CAACsB,MAAM,CAAC;IACpD,CAAC,CAAC;EATF,CASE,EACJ,UAACC,IAAI;IACH,OAAAA,IAAI,CAACjF,UAAU,CAAC;MAAA,IAAAkF,KAAA;MACd,IAAMC,SAAS,GAAGrF,OAAO,CAAC,IAAI,EAAE4D,OAAO,CAACuB,IAAI,CAAC;MAC7ClF,iBAAiB,CAACoF,SAAS,EAAE;QAAM,OAAAD,KAAI,CAACE,MAAM,EAAE;MAAb,CAAa,CAAC;MACjD,OAAOD,SAAS;IAClB,CAAC,CAAC;EAJF,CAIE,CACL,CACAE,WAAW,EAAE;AAClB;AAEA,SAASC,qBAAqBA,CAAC/B,SAA2B,EAAEC,IAA4B,EAAEC,KAAqB;EACrG,IAAAC,OAAO,GAAwBD,KAAK,CAAAC,OAA7B;IAAEjC,OAAO,GAAegC,KAAK,CAAAhC,OAApB;IAAE8D,QAAQ,GAAK9B,KAAK,CAAA8B,QAAV;EAClC,IAAI/B,IAAI,CAACgC,MAAM,GAAG,CAAC,IAAI,CAACD,QAAQ,IAAI,CAAC9D,OAAO,EAAE,OAAO,EAAE;EACvD,IAAMgE,MAAM,GAAaC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,EAAE,aAAa,CAAC;EACvF,IAAMK,QAAQ,GAAG,SAAAA,CAAC3E,GAAW;IAAK,OAAAwE,MAAM,CAACxE,GAAG,GAAGwE,MAAM,CAACD,MAAM,CAAC;EAA3B,CAA2B;EAC7D,IAAMK,OAAO,GAAU,EAAE;EACzB,KAAK,IAAI5E,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGuC,IAAI,CAACgC,MAAM,GAAG,CAAC,EAAEvE,GAAG,EAAE,EAAE;IACxC,IAAAO,EAAA,GAAAJ,MAAA,CAAe,CAACoC,IAAI,CAACvC,GAAG,CAAC,CAACJ,MAAM,EAAE2C,IAAI,CAACvC,GAAG,GAAG,CAAC,CAAC,CAACJ,MAAM,CAAC;MAAtDiF,IAAI,GAAAtE,EAAA;MAAER,IAAI,GAAAQ,EAAA,GAA4C;IAC7D,IAAMuE,IAAI,GAAG7D,YAAY,CAAC4D,IAAI,EAAE9E,IAAI,EAAEyC,KAAK,CAAC;IAC5CoC,OAAO,CAAC3E,IAAI,CAAC;MAAE+C,CAAC,EAAE8B,IAAI;MAAEC,IAAI,EAAEJ,QAAQ,CAAC3E,GAAG;IAAC,CAAE,CAAC;EAChD;EAEA,OAAOsC,SAAS,CACbW,SAAS,CAAC3D,WAAW,CAACI,MAAM,CAACwD,KAAK,CAAC,CACnCX,IAAI,CAACqC,OAAO,EAAE,UAACI,CAAC,EAAEC,CAAC;IAAK,OAAAA,CAAC;EAAD,CAAC,CAAC,CAC1B9B,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK;MAC1B,IAAM0B,WAAW,GAAG/F,gBAAgB,CAACoE,KAAK,EAAE,CAACA,KAAK,EAAEC,KAAK,EAAEoB,OAAO,CAAC,CAAC;MACpE,IAAI,CAACvE,IAAI,CAAC6E,WAAW,CAAC;IACxB,CAAC,CAAC,CACD7E,IAAI,CAAC,WAAW,EAAEf,WAAW,CAACI,MAAM,CAACiE,IAAI,CAAC;EAN7C,CAM6C,EAC/C,UAACI,MAAM;IACL,OAAAA,MAAM,CAAChF,UAAU,CAAC,UAAUwE,KAAK,EAAEC,KAAK;MACtC,IAAM0B,WAAW,GAAG/F,gBAAgB,CAACoE,KAAK,EAAE,CAACA,KAAK,EAAEC,KAAK,EAAEoB,OAAO,CAAC,CAAC;MACpE,OAAO7F,UAAU,CAAC,IAAI,EAAEmG,WAAW,EAAEzC,OAAO,CAACsB,MAAM,CAAC;IACtD,CAAC,CAAC;EAHF,CAGE,EACJ,UAACC,IAAI;IACH,OAAAA,IAAI,CAACjF,UAAU,CAAC;MAAA,IAAAkF,KAAA;MACd,IAAMC,SAAS,GAAGrF,OAAO,CAAC,IAAI,EAAE4D,OAAO,CAACuB,IAAI,CAAC;MAC7ClF,iBAAiB,CAACoF,SAAS,EAAE;QAAM,OAAAD,KAAI,CAACE,MAAM,EAAE;MAAb,CAAa,CAAC;MACjD,OAAOD,SAAS;IAClB,CAAC,CAAC;EAJF,CAIE,CACL,CACAE,WAAW,EAAE;AAClB;AAEA,SAASe,OAAOA,CAAC9E,IAAoB;EAC3B,IAAAE,EAAA,GAAsBF,IAAI,CAAAkC,IAAjB;IAATA,IAAI,GAAAhC,EAAA,cAAG,EAAE,GAAAA,EAAA;IAAEa,MAAM,GAAKf,IAAI,CAAAe,MAAT;EACzB,IAAI,CAACA,MAAM,EAAE,OAAOmB,IAAI;EACxB,OAAOA,IAAI,CAACK,GAAG,CAAC,UAACW,KAAK;IACZ,IAAA3D,MAAM,GAAK2D,KAAK,CAAA3D,MAAV;IACR,IAAAW,EAAA,GAAAJ,MAAA,CAAUP,MAAM;MAAfwF,KAAK,GAAA7E,EAAA,GAAU;IACtB,OAAAmD,QAAA,CAAAA,QAAA,KAAYH,KAAK;MAAE3D,MAAM,EAAAM,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAMP,MAAM,YAAEwF,KAAK;IAAA;EAC9C,CAAC,CAAC;AACJ;AAEA,IAAAC,IAAA,0BAAAC,MAAA;EAA0BC,SAAA,CAAAF,IAAA,EAAAC,MAAA;EAA1B,SAAAD,KAAA;;EAWA;EAVEA,IAAA,CAAAG,SAAA,CAAAC,MAAM,GAAN,UAAOC,UAA0B,EAAEpD,SAAgB;IACjD;IACQ,IAAAtB,IAAI,GAAyC0E,UAAU,CAAA1E,IAAnD;MAAEP,MAAM,GAAiCiF,UAAU,CAAAjF,MAA3C;MAAE6D,QAAQ,GAAuBoB,UAAU,CAAApB,QAAjC;MAAElD,MAAM,GAAesE,UAAU,CAAAtE,MAAzB;MAAKoB,KAAK,GAAAmD,MAAA,CAAKD,UAAU,EAAzD,wCAA4C,CAAF;IAChD,IAAMnD,IAAI,GAAG4C,OAAO,CAACO,UAAU,CAAC;IAChC,IAAMnG,SAAS,GAAGF,MAAM,CAACiD,SAAS,CAAC,CAACsD,sBAAsB,CAACtG,WAAW,CAACC,SAAS,EAAE,GAAG,CAAC;IACtF,IAAME,WAAW,GAAGJ,MAAM,CAACiD,SAAS,CAAC,CAACsD,sBAAsB,CAACtG,WAAW,CAACG,WAAW,EAAE,GAAG,CAAC;IAC1F,IAAMoG,eAAe,GAAGxD,cAAc,CAAC9C,SAAS,EAAEgD,IAAI,EAAEmD,UAAU,EAAElD,KAAK,CAAC;IAC1E,IAAMsD,kBAAkB,GAAGzB,qBAAqB,CAAC5E,WAAW,EAAE8C,IAAI,EAAEmD,UAAU,CAAC;IAC/E,OAAAxF,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAW0F,eAAe,WAAA1F,MAAA,CAAK2F,kBAAkB;EACnD,CAAC;EACH,OAAAT,IAAC;AAAD,CAAC,CAXyBrG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}