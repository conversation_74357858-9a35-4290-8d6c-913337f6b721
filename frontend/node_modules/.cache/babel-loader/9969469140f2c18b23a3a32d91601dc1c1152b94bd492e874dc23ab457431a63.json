{"ast": null, "code": "import { Group } from './group';\n/**\n * The GroupX transform group data by x channel, and aggregate.\n */\nexport const GroupX = (options = {}) => {\n  return Group(Object.assign(Object.assign({}, options), {\n    channels: ['x', 'color', 'series']\n  }));\n};\nGroupX.props = {};", "map": {"version": 3, "names": ["Group", "GroupX", "options", "Object", "assign", "channels", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/groupX.ts"], "sourcesContent": ["import { TransformComponent as TC } from '../runtime';\nimport { GroupXTransform } from '../spec';\nimport { Group } from './group';\n\nexport type GroupXOptions = Omit<GroupXTransform, 'type'>;\n\n/**\n * The GroupX transform group data by x channel, and aggregate.\n */\nexport const GroupX: TC<GroupXOptions> = (options = {}) => {\n  return Group({ ...options, channels: ['x', 'color', 'series'] });\n};\n\nGroupX.props = {};\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ,SAAS;AAI/B;;;AAGA,OAAO,MAAMC,MAAM,GAAsBA,CAACC,OAAO,GAAG,EAAE,KAAI;EACxD,OAAOF,KAAK,CAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMF,OAAO;IAAEG,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ;EAAC,GAAG;AAClE,CAAC;AAEDJ,MAAM,CAACK,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}