{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { column, isObject } from './utils/helper';\n/**\n * Add a default encode for rangeX\n * when data is just an array\n */\nexport const MaybeDefaultX = () => {\n  return (I, mark) => {\n    const {\n      data\n    } = mark;\n    if (Array.isArray(data) && (data.every(Array.isArray) || !data.some(isObject))) {\n      const extractX = (data, index) => Array.isArray(data[0]) ? data.map(item => item[index]) : [data[index]];\n      return [I, deepMix({}, mark, {\n        encode: {\n          x: column(extractX(data, 0)),\n          x1: column(extractX(data, 1))\n        }\n      })];\n    }\n    return [I, mark];\n  };\n};\nMaybeDefaultX.props = {};", "map": {"version": 3, "names": ["deepMix", "column", "isObject", "MaybeDefaultX", "I", "mark", "data", "Array", "isArray", "every", "some", "extractX", "index", "map", "item", "encode", "x", "x1", "props"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/transform/maybeDefaultX.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { TransformComponent as TC } from '../runtime';\nimport { column, isObject } from './utils/helper';\n\nexport type MaybeDefaultXOptions = Record<string, never>;\n\n/**\n * Add a default encode for rangeX\n * when data is just an array\n */\nexport const MaybeDefaultX: TC<MaybeDefaultXOptions> = () => {\n  return (I, mark) => {\n    const { data } = mark;\n    if (\n      Array.isArray(data) &&\n      (data.every(Array.isArray) || !data.some(isObject))\n    ) {\n      const extractX = (data, index: number) =>\n        Array.isArray(data[0])\n          ? data.map((item) => item[index])\n          : [data[index]];\n      return [\n        I,\n        deepMix({}, mark, {\n          encode: {\n            x: column(extractX(data, 0)),\n            x1: column(extractX(data, 1)),\n          },\n        }),\n      ];\n    }\n    return [I, mark];\n  };\n};\n\nMaybeDefaultX.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAIjD;;;;AAIA,OAAO,MAAMC,aAAa,GAA6BA,CAAA,KAAK;EAC1D,OAAO,CAACC,CAAC,EAAEC,IAAI,KAAI;IACjB,MAAM;MAAEC;IAAI,CAAE,GAAGD,IAAI;IACrB,IACEE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,KAClBA,IAAI,CAACG,KAAK,CAACF,KAAK,CAACC,OAAO,CAAC,IAAI,CAACF,IAAI,CAACI,IAAI,CAACR,QAAQ,CAAC,CAAC,EACnD;MACA,MAAMS,QAAQ,GAAGA,CAACL,IAAI,EAAEM,KAAa,KACnCL,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,GAClBA,IAAI,CAACO,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACF,KAAK,CAAC,CAAC,GAC/B,CAACN,IAAI,CAACM,KAAK,CAAC,CAAC;MACnB,OAAO,CACLR,CAAC,EACDJ,OAAO,CAAC,EAAE,EAAEK,IAAI,EAAE;QAChBU,MAAM,EAAE;UACNC,CAAC,EAAEf,MAAM,CAACU,QAAQ,CAACL,IAAI,EAAE,CAAC,CAAC,CAAC;UAC5BW,EAAE,EAAEhB,MAAM,CAACU,QAAQ,CAACL,IAAI,EAAE,CAAC,CAAC;;OAE/B,CAAC,CACH;;IAEH,OAAO,CAACF,CAAC,EAAEC,IAAI,CAAC;EAClB,CAAC;AACH,CAAC;AAEDF,aAAa,CAACe,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}