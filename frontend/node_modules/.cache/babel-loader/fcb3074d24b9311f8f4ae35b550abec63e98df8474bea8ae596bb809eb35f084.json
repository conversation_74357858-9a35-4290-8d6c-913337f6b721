{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classnames from 'classnames';\n// ========================= ClassNames =========================\nexport function mergeClassNames(schema) {\n  const mergedSchema = schema || {};\n  for (var _len = arguments.length, classNames = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    classNames[_key - 1] = arguments[_key];\n  }\n  return classNames.reduce((acc, cur) => {\n    // Loop keys of the current classNames\n    Object.keys(cur || {}).forEach(key => {\n      const keySchema = mergedSchema[key];\n      const curVal = cur[key];\n      if (keySchema && typeof keySchema === 'object') {\n        if (curVal && typeof curVal === 'object') {\n          // Loop fill\n          acc[key] = mergeClassNames(keySchema, acc[key], curVal);\n        } else {\n          // Covert string to object structure\n          const {\n            _default: defaultField\n          } = keySchema;\n          acc[key] = acc[key] || {};\n          acc[key][defaultField] = classnames(acc[key][defaultField], curVal);\n        }\n      } else {\n        // Flatten fill\n        acc[key] = classnames(acc[key], curVal);\n      }\n    });\n    return acc;\n  }, {});\n}\nfunction useSemanticClassNames(schema) {\n  for (var _len2 = arguments.length, classNames = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    classNames[_key2 - 1] = arguments[_key2];\n  }\n  return React.useMemo(() => mergeClassNames.apply(void 0, [schema].concat(classNames)), [classNames]);\n}\n// =========================== Styles ===========================\nfunction useSemanticStyles() {\n  for (var _len3 = arguments.length, styles = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    styles[_key3] = arguments[_key3];\n  }\n  return React.useMemo(() => {\n    return styles.reduce(function (acc) {\n      let cur = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      Object.keys(cur).forEach(key => {\n        acc[key] = Object.assign(Object.assign({}, acc[key]), cur[key]);\n      });\n      return acc;\n    }, {});\n  }, [styles]);\n}\n// =========================== Export ===========================\nfunction fillObjectBySchema(obj, schema) {\n  const newObj = Object.assign({}, obj);\n  Object.keys(schema).forEach(key => {\n    if (key !== '_default') {\n      const nestSchema = schema[key];\n      const nextValue = newObj[key] || {};\n      newObj[key] = nestSchema ? fillObjectBySchema(nextValue, nestSchema) : nextValue;\n    }\n  });\n  return newObj;\n}\n/**\n * Merge classNames and styles from multiple sources.\n * When `schema` is provided, it will **must** provide the nest object structure.\n */\nexport default function useMergeSemantic(classNamesList, stylesList, schema) {\n  const mergedClassNames = useSemanticClassNames.apply(void 0, [schema].concat(_toConsumableArray(classNamesList)));\n  const mergedStyles = useSemanticStyles.apply(void 0, _toConsumableArray(stylesList));\n  return React.useMemo(() => {\n    return [fillObjectBySchema(mergedClassNames, schema), fillObjectBySchema(mergedStyles, schema)];\n  }, [mergedClassNames, mergedStyles]);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classnames", "mergeClassNames", "schema", "mergedSchema", "_len", "arguments", "length", "classNames", "Array", "_key", "reduce", "acc", "cur", "Object", "keys", "for<PERSON>ach", "key", "keySchema", "curVal", "_default", "defaultField", "useSemanticClassNames", "_len2", "_key2", "useMemo", "apply", "concat", "useSemanticStyles", "_len3", "styles", "_key3", "undefined", "assign", "fillObjectBySchema", "obj", "newObj", "nestSchema", "nextValue", "useMergeSemantic", "classNamesList", "stylesList", "mergedClassNames", "mergedStyles"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/hooks/useMergeSemantic/index.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classnames from 'classnames';\n// ========================= ClassNames =========================\nexport function mergeClassNames(schema, ...classNames) {\n  const mergedSchema = schema || {};\n  return classNames.reduce((acc, cur) => {\n    // Loop keys of the current classNames\n    Object.keys(cur || {}).forEach(key => {\n      const keySchema = mergedSchema[key];\n      const curVal = cur[key];\n      if (keySchema && typeof keySchema === 'object') {\n        if (curVal && typeof curVal === 'object') {\n          // Loop fill\n          acc[key] = mergeClassNames(keySchema, acc[key], curVal);\n        } else {\n          // Covert string to object structure\n          const {\n            _default: defaultField\n          } = keySchema;\n          acc[key] = acc[key] || {};\n          acc[key][defaultField] = classnames(acc[key][defaultField], curVal);\n        }\n      } else {\n        // Flatten fill\n        acc[key] = classnames(acc[key], curVal);\n      }\n    });\n    return acc;\n  }, {});\n}\nfunction useSemanticClassNames(schema, ...classNames) {\n  return React.useMemo(() => mergeClassNames.apply(void 0, [schema].concat(classNames)), [classNames]);\n}\n// =========================== Styles ===========================\nfunction useSemanticStyles(...styles) {\n  return React.useMemo(() => {\n    return styles.reduce((acc, cur = {}) => {\n      Object.keys(cur).forEach(key => {\n        acc[key] = Object.assign(Object.assign({}, acc[key]), cur[key]);\n      });\n      return acc;\n    }, {});\n  }, [styles]);\n}\n// =========================== Export ===========================\nfunction fillObjectBySchema(obj, schema) {\n  const newObj = Object.assign({}, obj);\n  Object.keys(schema).forEach(key => {\n    if (key !== '_default') {\n      const nestSchema = schema[key];\n      const nextValue = newObj[key] || {};\n      newObj[key] = nestSchema ? fillObjectBySchema(nextValue, nestSchema) : nextValue;\n    }\n  });\n  return newObj;\n}\n/**\n * Merge classNames and styles from multiple sources.\n * When `schema` is provided, it will **must** provide the nest object structure.\n */\nexport default function useMergeSemantic(classNamesList, stylesList, schema) {\n  const mergedClassNames = useSemanticClassNames.apply(void 0, [schema].concat(_toConsumableArray(classNamesList)));\n  const mergedStyles = useSemanticStyles.apply(void 0, _toConsumableArray(stylesList));\n  return React.useMemo(() => {\n    return [fillObjectBySchema(mergedClassNames, schema), fillObjectBySchema(mergedStyles, schema)];\n  }, [mergedClassNames, mergedStyles]);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAiB;EACrD,MAAMC,YAAY,GAAGD,MAAM,IAAI,CAAC,CAAC;EAAC,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADOC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEnD,OAAOF,UAAU,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IACrC;IACAC,MAAM,CAACC,IAAI,CAACF,GAAG,IAAI,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACpC,MAAMC,SAAS,GAAGd,YAAY,CAACa,GAAG,CAAC;MACnC,MAAME,MAAM,GAAGN,GAAG,CAACI,GAAG,CAAC;MACvB,IAAIC,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QAC9C,IAAIC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACxC;UACAP,GAAG,CAACK,GAAG,CAAC,GAAGf,eAAe,CAACgB,SAAS,EAAEN,GAAG,CAACK,GAAG,CAAC,EAAEE,MAAM,CAAC;QACzD,CAAC,MAAM;UACL;UACA,MAAM;YACJC,QAAQ,EAAEC;UACZ,CAAC,GAAGH,SAAS;UACbN,GAAG,CAACK,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,IAAI,CAAC,CAAC;UACzBL,GAAG,CAACK,GAAG,CAAC,CAACI,YAAY,CAAC,GAAGpB,UAAU,CAACW,GAAG,CAACK,GAAG,CAAC,CAACI,YAAY,CAAC,EAAEF,MAAM,CAAC;QACrE;MACF,CAAC,MAAM;QACL;QACAP,GAAG,CAACK,GAAG,CAAC,GAAGhB,UAAU,CAACW,GAAG,CAACK,GAAG,CAAC,EAAEE,MAAM,CAAC;MACzC;IACF,CAAC,CAAC;IACF,OAAOP,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASU,qBAAqBA,CAACnB,MAAM,EAAiB;EAAA,SAAAoB,KAAA,GAAAjB,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAc,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAVhB,UAAU,CAAAgB,KAAA,QAAAlB,SAAA,CAAAkB,KAAA;EAAA;EAClD,OAAOxB,KAAK,CAACyB,OAAO,CAAC,MAAMvB,eAAe,CAACwB,KAAK,CAAC,KAAK,CAAC,EAAE,CAACvB,MAAM,CAAC,CAACwB,MAAM,CAACnB,UAAU,CAAC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;AACtG;AACA;AACA,SAASoB,iBAAiBA,CAAA,EAAY;EAAA,SAAAC,KAAA,GAAAvB,SAAA,CAAAC,MAAA,EAARuB,MAAM,OAAArB,KAAA,CAAAoB,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAND,MAAM,CAAAC,KAAA,IAAAzB,SAAA,CAAAyB,KAAA;EAAA;EAClC,OAAO/B,KAAK,CAACyB,OAAO,CAAC,MAAM;IACzB,OAAOK,MAAM,CAACnB,MAAM,CAAC,UAACC,GAAG,EAAe;MAAA,IAAbC,GAAG,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0B,SAAA,GAAA1B,SAAA,MAAG,CAAC,CAAC;MACjCQ,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;QAC9BL,GAAG,CAACK,GAAG,CAAC,GAAGH,MAAM,CAACmB,MAAM,CAACnB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAErB,GAAG,CAACK,GAAG,CAAC,CAAC,EAAEJ,GAAG,CAACI,GAAG,CAAC,CAAC;MACjE,CAAC,CAAC;MACF,OAAOL,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAACkB,MAAM,CAAC,CAAC;AACd;AACA;AACA,SAASI,kBAAkBA,CAACC,GAAG,EAAEhC,MAAM,EAAE;EACvC,MAAMiC,MAAM,GAAGtB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;EACrCrB,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;IACjC,IAAIA,GAAG,KAAK,UAAU,EAAE;MACtB,MAAMoB,UAAU,GAAGlC,MAAM,CAACc,GAAG,CAAC;MAC9B,MAAMqB,SAAS,GAAGF,MAAM,CAACnB,GAAG,CAAC,IAAI,CAAC,CAAC;MACnCmB,MAAM,CAACnB,GAAG,CAAC,GAAGoB,UAAU,GAAGH,kBAAkB,CAACI,SAAS,EAAED,UAAU,CAAC,GAAGC,SAAS;IAClF;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AACA;AACA;AACA;AACA;AACA,eAAe,SAASG,gBAAgBA,CAACC,cAAc,EAAEC,UAAU,EAAEtC,MAAM,EAAE;EAC3E,MAAMuC,gBAAgB,GAAGpB,qBAAqB,CAACI,KAAK,CAAC,KAAK,CAAC,EAAE,CAACvB,MAAM,CAAC,CAACwB,MAAM,CAAC5B,kBAAkB,CAACyC,cAAc,CAAC,CAAC,CAAC;EACjH,MAAMG,YAAY,GAAGf,iBAAiB,CAACF,KAAK,CAAC,KAAK,CAAC,EAAE3B,kBAAkB,CAAC0C,UAAU,CAAC,CAAC;EACpF,OAAOzC,KAAK,CAACyB,OAAO,CAAC,MAAM;IACzB,OAAO,CAACS,kBAAkB,CAACQ,gBAAgB,EAAEvC,MAAM,CAAC,EAAE+B,kBAAkB,CAACS,YAAY,EAAExC,MAAM,CAAC,CAAC;EACjG,CAAC,EAAE,CAACuC,gBAAgB,EAAEC,YAAY,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}