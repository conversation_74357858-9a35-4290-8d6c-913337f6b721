{"ast": null, "code": "import { flow, transformOptions } from '../../utils';\nimport { mark } from '../../adaptor';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n  return flow(mark, transformOptions)(params);\n}", "map": {"version": 3, "names": ["flow", "transformOptions", "mark", "adaptor", "params"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@ant-design/plots/es/core/plots/tiny-area/adaptor.js"], "sourcesContent": ["import { flow, transformOptions } from '../../utils';\nimport { mark } from '../../adaptor';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n    return flow(mark, transformOptions)(params);\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,gBAAgB,QAAQ,aAAa;AACpD,SAASC,IAAI,QAAQ,eAAe;AACpC;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAE;EAC5B,OAAOJ,IAAI,CAACE,IAAI,EAAED,gBAAgB,CAAC,CAACG,MAAM,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}