{"ast": null, "code": "/**\n * A specialized version of `_.indexOf` which performs strict equality\n * comparisons of values, i.e. `===`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction strictIndexOf(array, value, fromIndex) {\n  var index = fromIndex - 1,\n    length = array.length;\n  while (++index < length) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return -1;\n}\nmodule.exports = strictIndexOf;", "map": {"version": 3, "names": ["strictIndexOf", "array", "value", "fromIndex", "index", "length", "module", "exports"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/lodash/_strictIndexOf.js"], "sourcesContent": ["/**\n * A specialized version of `_.indexOf` which performs strict equality\n * comparisons of values, i.e. `===`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction strictIndexOf(array, value, fromIndex) {\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = strictIndexOf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC9C,IAAIC,KAAK,GAAGD,SAAS,GAAG,CAAC;IACrBE,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIJ,KAAK,CAACG,KAAK,CAAC,KAAKF,KAAK,EAAE;MAC1B,OAAOE,KAAK;IACd;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEAE,MAAM,CAACC,OAAO,GAAGP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}