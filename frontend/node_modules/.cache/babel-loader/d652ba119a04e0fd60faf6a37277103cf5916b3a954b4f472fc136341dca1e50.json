{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar pattern = function pattern(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default pattern;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "pattern", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@rc-component/async-validator/es/validator/pattern.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar pattern = function pattern(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default pattern;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrE,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,EAAE,QAAQ,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACnD,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACpD,IAAI,CAACN,YAAY,CAACG,KAAK,EAAE,QAAQ,CAAC,EAAE;MAClCJ,KAAK,CAACE,OAAO,CAACC,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACrD;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}