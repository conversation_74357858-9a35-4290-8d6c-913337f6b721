{"ast": null, "code": "var baseGet = require('./_baseGet'),\n  baseSet = require('./_baseSet'),\n  castPath = require('./_castPath');\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n    length = paths.length,\n    result = {};\n  while (++index < length) {\n    var path = paths[index],\n      value = baseGet(object, path);\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\nmodule.exports = basePickBy;", "map": {"version": 3, "names": ["baseGet", "require", "baseSet", "<PERSON><PERSON><PERSON>", "basePickBy", "object", "paths", "predicate", "index", "length", "result", "path", "value", "module", "exports"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/lodash/_basePickBy.js"], "sourcesContent": ["var baseGet = require('./_baseGet'),\n    baseSet = require('./_baseSet'),\n    castPath = require('./_castPath');\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = basePickBy;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;EAC/BC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;EAC/BE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,MAAM,GAAG,CAAC,CAAC;EAEf,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,IAAI,GAAGL,KAAK,CAACE,KAAK,CAAC;MACnBI,KAAK,GAAGZ,OAAO,CAACK,MAAM,EAAEM,IAAI,CAAC;IAEjC,IAAIJ,SAAS,CAACK,KAAK,EAAED,IAAI,CAAC,EAAE;MAC1BT,OAAO,CAACQ,MAAM,EAAEP,QAAQ,CAACQ,IAAI,EAAEN,MAAM,CAAC,EAAEO,KAAK,CAAC;IAChD;EACF;EACA,OAAOF,MAAM;AACf;AAEAG,MAAM,CAACC,OAAO,GAAGV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}