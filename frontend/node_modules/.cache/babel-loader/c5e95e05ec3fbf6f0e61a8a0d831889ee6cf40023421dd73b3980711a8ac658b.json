{"ast": null, "code": "/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nconst resolveConflicts = (entries, cg) => {\n  var _a, _b, _c;\n  const mappedEntries = {};\n  entries === null || entries === void 0 ? void 0 : entries.forEach((entry, i) => {\n    mappedEntries[entry.v] = {\n      i,\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v]\n    };\n    const tmp = mappedEntries[entry.v];\n    if (entry.barycenter !== undefined) {\n      tmp.barycenter = entry.barycenter;\n      tmp.weight = entry.weight;\n    }\n  });\n  (_a = cg.getAllEdges()) === null || _a === void 0 ? void 0 : _a.forEach(e => {\n    const entryV = mappedEntries[e.source];\n    const entryW = mappedEntries[e.target];\n    if (entryV !== undefined && entryW !== undefined) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.target]);\n    }\n  });\n  const sourceSet = (_c = (_b = Object.values(mappedEntries)).filter) === null || _c === void 0 ? void 0 : _c.call(_b, entry => !entry.indegree);\n  return doResolveConflicts(sourceSet);\n};\nconst doResolveConflicts = sourceSet => {\n  var _a, _b;\n  const entries = [];\n  const handleIn = vEntry => {\n    return uEntry => {\n      if (uEntry.merged) return;\n      if (uEntry.barycenter === undefined || vEntry.barycenter === undefined || uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  };\n  const handleOut = vEntry => {\n    return wEntry => {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  };\n  while (sourceSet === null || sourceSet === void 0 ? void 0 : sourceSet.length) {\n    const entry = sourceSet.pop();\n    entries.push(entry);\n    (_a = entry['in'].reverse()) === null || _a === void 0 ? void 0 : _a.forEach(e => handleIn(entry)(e));\n    (_b = entry.out) === null || _b === void 0 ? void 0 : _b.forEach(e => handleOut(entry)(e));\n  }\n  const filtered = entries.filter(entry => !entry.merged);\n  const keys = ['vs', 'i', 'barycenter', 'weight'];\n  return filtered.map(entry => {\n    const picked = {};\n    keys === null || keys === void 0 ? void 0 : keys.forEach(key => {\n      if (entry[key] === undefined) return;\n      picked[key] = entry[key];\n    });\n    return picked;\n  });\n};\nconst mergeEntries = (target, source) => {\n  var _a;\n  let sum = 0;\n  let weight = 0;\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n  target.vs = (_a = source.vs) === null || _a === void 0 ? void 0 : _a.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n};\nexport default resolveConflicts;", "map": {"version": 3, "names": ["resolveConflicts", "entries", "cg", "mappedEntries", "for<PERSON>ach", "entry", "i", "v", "indegree", "in", "out", "vs", "tmp", "barycenter", "undefined", "weight", "_a", "getAllEdges", "e", "entryV", "source", "entryW", "target", "push", "sourceSet", "_c", "_b", "Object", "values", "filter", "call", "doResolveConflicts", "handleIn", "vEntry", "uEntry", "merged", "mergeEntries", "handleOut", "wEntry", "length", "pop", "reverse", "filtered", "keys", "map", "picked", "key", "sum", "concat", "Math", "min"], "sources": ["../../../src/antv-dagre/order/resolve-conflicts.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,MAAMA,gBAAgB,GAAGA,CACvBC,OAIG,EACHC,EAAS,KACP;;EACF,MAAMC,aAAa,GAAkC,EAAE;EACvDF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,CAAC,CAACC,KAAK,EAAEC,CAAS,KAAI;IACpCH,aAAa,CAACE,KAAK,CAACE,CAAC,CAAC,GAAG;MACvBD,CAAC;MACDE,QAAQ,EAAE,CAAC;MACXC,EAAE,EAAE,EAAE;MACNC,GAAG,EAAE,EAAE;MACPC,EAAE,EAAE,CAACN,KAAK,CAACE,CAAC;KACb;IACD,MAAMK,GAAG,GAAGT,aAAa,CAACE,KAAK,CAACE,CAAC,CAAC;IAClC,IAAIF,KAAK,CAACQ,UAAU,KAAKC,SAAS,EAAE;MAClCF,GAAG,CAACC,UAAU,GAAGR,KAAK,CAACQ,UAAU;MACjCD,GAAG,CAACG,MAAM,GAAGV,KAAK,CAACU,MAAM;;EAE7B,CAAC,CAAC;EAEF,CAAAC,EAAA,GAAAd,EAAE,CAACe,WAAW,EAAE,cAAAD,EAAA,uBAAAA,EAAA,CAAEZ,OAAO,CAAEc,CAAC,IAAI;IAC9B,MAAMC,MAAM,GAAGhB,aAAa,CAACe,CAAC,CAACE,MAAM,CAAC;IACtC,MAAMC,MAAM,GAAGlB,aAAa,CAACe,CAAC,CAACI,MAAM,CAAC;IACtC,IAAIH,MAAM,KAAKL,SAAS,IAAIO,MAAM,KAAKP,SAAS,EAAE;MAChDO,MAAM,CAACb,QAAS,EAAE;MAClBW,MAAM,CAACT,GAAI,CAACa,IAAI,CAACpB,aAAa,CAACe,CAAC,CAACI,MAAM,CAAC,CAAC;;EAE7C,CAAC,CAAC;EAEF,MAAME,SAAS,GAAG,CAAAC,EAAA,IAAAC,EAAA,GAAAC,MAAM,CAACC,MAAM,CAACzB,aAAa,CAAC,EAAC0B,MAAM,cAAAJ,EAAA,uBAAAA,EAAA,CAAAK,IAAA,CAAAJ,EAAA,EAClDrB,KAAoB,IAAK,CAACA,KAAK,CAACG,QAAQ,CAC1C;EAED,OAAOuB,kBAAkB,CAACP,SAAS,CAAC;AACtC,CAAC;AAED,MAAMO,kBAAkB,GAAIP,SAA0B,IAAI;;EACxD,MAAMvB,OAAO,GAAG,EAAE;EAElB,MAAM+B,QAAQ,GAAIC,MAAqB,IAAI;IACzC,OAAQC,MAAqB,IAAI;MAC/B,IAAIA,MAAM,CAACC,MAAM,EAAE;MACnB,IACED,MAAM,CAACrB,UAAU,KAAKC,SAAS,IAC/BmB,MAAM,CAACpB,UAAU,KAAKC,SAAS,IAC/BoB,MAAM,CAACrB,UAAU,IAAIoB,MAAM,CAACpB,UAAU,EACtC;QACAuB,YAAY,CAACH,MAAM,EAAEC,MAAM,CAAC;;IAEhC,CAAC;EACH,CAAC;EAED,MAAMG,SAAS,GAAIJ,MAAqB,IAAI;IAC1C,OAAQK,MAAqB,IAAI;MAC/BA,MAAM,CAAC,IAAI,CAAE,CAACf,IAAI,CAACU,MAAM,CAAC;MAC1B,IAAI,EAAEK,MAAM,CAAC9B,QAAS,KAAK,CAAC,EAAE;QAC5BgB,SAAS,CAACD,IAAI,CAACe,MAAM,CAAC;;IAE1B,CAAC;EACH,CAAC;EAED,OAAOd,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEe,MAAM,EAAE;IACxB,MAAMlC,KAAK,GAAGmB,SAAS,CAACgB,GAAG,EAAG;IAC9BvC,OAAO,CAACsB,IAAI,CAAClB,KAAK,CAAC;IACnB,CAAAW,EAAA,GAAAX,KAAK,CAAC,IAAI,CAAE,CAACoC,OAAO,EAAE,cAAAzB,EAAA,uBAAAA,EAAA,CAAEZ,OAAO,CAAEc,CAAgB,IAAKc,QAAQ,CAAC3B,KAAK,CAAC,CAACa,CAAC,CAAC,CAAC;IACzE,CAAAQ,EAAA,GAAArB,KAAK,CAACK,GAAG,cAAAgB,EAAA,uBAAAA,EAAA,CAAEtB,OAAO,CAAEc,CAAgB,IAAKmB,SAAS,CAAChC,KAAK,CAAC,CAACa,CAAC,CAAC,CAAC;;EAG/D,MAAMwB,QAAQ,GAAGzC,OAAO,CAAC4B,MAAM,CAAExB,KAAK,IAAK,CAACA,KAAK,CAAC8B,MAAM,CAAC;EACzD,MAAMQ,IAAI,GAA6C,CACrD,IAAI,EACJ,GAAG,EACH,YAAY,EACZ,QAAQ,CACT;EACD,OAAOD,QAAQ,CAACE,GAAG,CAAEvC,KAAK,IAAI;IAC5B,MAAMwC,MAAM,GAAwB,EAAE;IACtCF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEvC,OAAO,CAAE0C,GAAG,IAAI;MACpB,IAAIzC,KAAK,CAACyC,GAAG,CAAC,KAAKhC,SAAS,EAAE;MAC9B+B,MAAM,CAACC,GAAG,CAAC,GAAGzC,KAAK,CAACyC,GAAG,CAAC;IAC1B,CAAC,CAAC;IACF,OAAOD,MAAuB;EAChC,CAAC,CAAC;AACJ,CAAC;AAED,MAAMT,YAAY,GAAGA,CAACd,MAAqB,EAAEF,MAAqB,KAAI;;EACpE,IAAI2B,GAAG,GAAG,CAAC;EACX,IAAIhC,MAAM,GAAG,CAAC;EAEd,IAAIO,MAAM,CAACP,MAAM,EAAE;IACjBgC,GAAG,IAAIzB,MAAM,CAACT,UAAW,GAAGS,MAAM,CAACP,MAAM;IACzCA,MAAM,IAAIO,MAAM,CAACP,MAAM;;EAGzB,IAAIK,MAAM,CAACL,MAAM,EAAE;IACjBgC,GAAG,IAAI3B,MAAM,CAACP,UAAW,GAAGO,MAAM,CAACL,MAAM;IACzCA,MAAM,IAAIK,MAAM,CAACL,MAAM;;EAGzBO,MAAM,CAACX,EAAE,GAAG,CAAAK,EAAA,GAAAI,MAAM,CAACT,EAAE,cAAAK,EAAA,uBAAAA,EAAA,CAAEgC,MAAM,CAAC1B,MAAM,CAACX,EAAE,CAAC;EACxCW,MAAM,CAACT,UAAU,GAAGkC,GAAG,GAAGhC,MAAM;EAChCO,MAAM,CAACP,MAAM,GAAGA,MAAM;EACtBO,MAAM,CAAChB,CAAC,GAAG2C,IAAI,CAACC,GAAG,CAAC9B,MAAM,CAACd,CAAC,EAAEgB,MAAM,CAAChB,CAAC,CAAC;EACvCc,MAAM,CAACe,MAAM,GAAG,IAAI;AACtB,CAAC;AAED,eAAenC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}