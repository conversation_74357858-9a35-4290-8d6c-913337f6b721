{"ast": null, "code": "import request from '../utils/request';\nimport { md5 } from 'js-md5';\nexport const authService = {\n  // 用户登录\n  async login(params) {\n    var _response$user, _response$user2;\n    const {\n      username,\n      password,\n      role\n    } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n    let response;\n    let loginUrl;\n    let loginData;\n    if (role === '管理员') {\n      // 管理员登录 - 使用统一认证接口\n      loginUrl = '/api/auth/login';\n      loginData = {\n        username,\n        password: encryptedPassword,\n        role: role\n      };\n    } else {\n      // 普通用户登录\n      loginUrl = '/yonghu/login';\n      loginData = {\n        yonghuming: username,\n        mima: encryptedPassword\n      };\n    }\n    response = await request.post(loginUrl, loginData);\n    return {\n      token: response.token,\n      user: {\n        id: response.userId || ((_response$user = response.user) === null || _response$user === void 0 ? void 0 : _response$user.id) || 1,\n        username: username,\n        role: role || '用户',\n        name: ((_response$user2 = response.user) === null || _response$user2 === void 0 ? void 0 : _response$user2.username) || username // 使用返回的用户信息\n      }\n    };\n  },\n  // 用户注册（普通用户）\n  async register(params) {\n    const {\n      username,\n      password,\n      name,\n      phone\n    } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,\n      // 用户名\n      mima: encryptedPassword,\n      // 密码\n      xingming: name,\n      // 姓名\n      shouji: phone || '' // 手机号\n    });\n  },\n  // 获取当前用户信息\n  async getCurrentUser() {\n    return await request.get('/api/users/current');\n  },\n  // 更新用户信息\n  async updateProfile(params) {\n    await request.put('/api/users/update', params);\n  },\n  // 修改密码\n  async changePassword(params) {\n    const {\n      oldPassword,\n      newPassword\n    } = params;\n    await request.put('/api/users/change-password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword)\n    });\n  },\n  // 退出登录\n  async logout() {\n    await request.post('/api/auth/logout');\n  }\n};", "map": {"version": 3, "names": ["request", "md5", "authService", "login", "params", "_response$user", "_response$user2", "username", "password", "role", "encryptedPassword", "response", "loginUrl", "loginData", "yonghuming", "mima", "post", "token", "user", "id", "userId", "name", "register", "phone", "xing<PERSON>", "<PERSON><PERSON><PERSON>", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "oldPassword", "newPassword", "logout"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { LoginParams, LoginResponse, User } from '../types';\nimport { md5 } from 'js-md5';\n\nexport const authService = {\n  // 用户登录\n  async login(params: LoginParams): Promise<LoginResponse> {\n    const { username, password, role } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n\n    let response;\n    let loginUrl;\n    let loginData;\n\n    if (role === '管理员') {\n      // 管理员登录 - 使用统一认证接口\n      loginUrl = '/api/auth/login';\n      loginData = {\n        username,\n        password: encryptedPassword,\n        role: role,\n      };\n    } else {\n      // 普通用户登录\n      loginUrl = '/yonghu/login';\n      loginData = {\n        yonghuming: username,\n        mima: encryptedPassword,\n      };\n    }\n\n    response = await request.post(loginUrl, loginData);\n\n    return {\n      token: response.token,\n      user: {\n        id: response.userId || response.user?.id || 1,\n        username: username,\n        role: role || '用户',\n        name: response.user?.username || username, // 使用返回的用户信息\n      },\n    };\n  },\n\n  // 用户注册（普通用户）\n  async register(params: {\n    username: string;\n    password: string;\n    name: string;\n    phone?: string;\n    email?: string;\n    role?: string;\n  }): Promise<void> {\n    const { username, password, name, phone } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,  // 用户名\n      mima: encryptedPassword,  // 密码\n      xingming: name,  // 姓名\n      shouji: phone || '',  // 手机号\n    });\n  },\n\n  // 获取当前用户信息\n  async getCurrentUser(): Promise<User> {\n    return await request.get('/api/users/current');\n  },\n\n  // 更新用户信息\n  async updateProfile(params: Partial<User>): Promise<void> {\n    await request.put('/api/users/update', params);\n  },\n\n  // 修改密码\n  async changePassword(params: {\n    oldPassword: string;\n    newPassword: string;\n  }): Promise<void> {\n    const { oldPassword, newPassword } = params;\n\n    await request.put('/api/users/change-password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword),\n    });\n  },\n\n  // 退出登录\n  async logout(): Promise<void> {\n    await request.post('/api/auth/logout');\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,GAAG,QAAQ,QAAQ;AAE5B,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,KAAKA,CAACC,MAAmB,EAA0B;IAAA,IAAAC,cAAA,EAAAC,eAAA;IACvD,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAK,CAAC,GAAGL,MAAM;;IAE3C;IACA,MAAMM,iBAAiB,GAAGT,GAAG,CAACO,QAAQ,CAAC;IAEvC,IAAIG,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IAEb,IAAIJ,IAAI,KAAK,KAAK,EAAE;MAClB;MACAG,QAAQ,GAAG,iBAAiB;MAC5BC,SAAS,GAAG;QACVN,QAAQ;QACRC,QAAQ,EAAEE,iBAAiB;QAC3BD,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,MAAM;MACL;MACAG,QAAQ,GAAG,eAAe;MAC1BC,SAAS,GAAG;QACVC,UAAU,EAAEP,QAAQ;QACpBQ,IAAI,EAAEL;MACR,CAAC;IACH;IAEAC,QAAQ,GAAG,MAAMX,OAAO,CAACgB,IAAI,CAACJ,QAAQ,EAAEC,SAAS,CAAC;IAElD,OAAO;MACLI,KAAK,EAAEN,QAAQ,CAACM,KAAK;MACrBC,IAAI,EAAE;QACJC,EAAE,EAAER,QAAQ,CAACS,MAAM,MAAAf,cAAA,GAAIM,QAAQ,CAACO,IAAI,cAAAb,cAAA,uBAAbA,cAAA,CAAec,EAAE,KAAI,CAAC;QAC7CZ,QAAQ,EAAEA,QAAQ;QAClBE,IAAI,EAAEA,IAAI,IAAI,IAAI;QAClBY,IAAI,EAAE,EAAAf,eAAA,GAAAK,QAAQ,CAACO,IAAI,cAAAZ,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,KAAIA,QAAQ,CAAE;MAC7C;IACF,CAAC;EACH,CAAC;EAED;EACA,MAAMe,QAAQA,CAAClB,MAOd,EAAiB;IAChB,MAAM;MAAEG,QAAQ;MAAEC,QAAQ;MAAEa,IAAI;MAAEE;IAAM,CAAC,GAAGnB,MAAM;IAClD,MAAMM,iBAAiB,GAAGT,GAAG,CAACO,QAAQ,CAAC;;IAEvC;IACA,MAAMR,OAAO,CAACgB,IAAI,CAAC,kBAAkB,EAAE;MACrCF,UAAU,EAAEP,QAAQ;MAAG;MACvBQ,IAAI,EAAEL,iBAAiB;MAAG;MAC1Bc,QAAQ,EAAEH,IAAI;MAAG;MACjBI,MAAM,EAAEF,KAAK,IAAI,EAAE,CAAG;IACxB,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMG,cAAcA,CAAA,EAAkB;IACpC,OAAO,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,oBAAoB,CAAC;EAChD,CAAC;EAED;EACA,MAAMC,aAAaA,CAACxB,MAAqB,EAAiB;IACxD,MAAMJ,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,EAAEzB,MAAM,CAAC;EAChD,CAAC;EAED;EACA,MAAM0B,cAAcA,CAAC1B,MAGpB,EAAiB;IAChB,MAAM;MAAE2B,WAAW;MAAEC;IAAY,CAAC,GAAG5B,MAAM;IAE3C,MAAMJ,OAAO,CAAC6B,GAAG,CAAC,4BAA4B,EAAE;MAC9CE,WAAW,EAAE9B,GAAG,CAAC8B,WAAW,CAAC;MAC7BC,WAAW,EAAE/B,GAAG,CAAC+B,WAAW;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,MAAMjC,OAAO,CAACgB,IAAI,CAAC,kBAAkB,CAAC;EACxC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}