{"ast": null, "code": "var baseProperty = require('./_baseProperty');\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\nmodule.exports = asciiSize;", "map": {"version": 3, "names": ["baseProperty", "require", "asciiSize", "module", "exports"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/lodash/_asciiSize.js"], "sourcesContent": ["var baseProperty = require('./_baseProperty');\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nmodule.exports = asciiSize;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGF,YAAY,CAAC,QAAQ,CAAC;AAEtCG,MAAM,CAACC,OAAO,GAAGF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}