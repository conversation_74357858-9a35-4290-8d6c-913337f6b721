{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/runtime/types/component.ts"], "sourcesContent": ["import { Coordinate, Transformation } from '@antv/coord';\nimport EventEmitter from '@antv/event-emitter';\nimport {\n  DisplayObject,\n  IAnimation as GAnimation,\n  IDocument,\n  Canvas,\n} from '@antv/g';\nimport {\n  G2Theme,\n  G2ViewInstance,\n  GuideComponentOrientation,\n  GuideComponentPosition,\n  IndexedValue,\n  Vector2,\n  G2MarkState,\n  GuideComponentPlane,\n  Layout,\n} from './common';\nimport { DataComponent } from './data';\nimport { Encode, EncodeComponent } from './encode';\nimport { Mark, MarkComponent } from './mark';\nimport { G2ViewTree, G2Library, G2Mark } from './options';\nimport { Transform, TransformComponent } from './transform';\n\nexport type G2ComponentNamespaces =\n  | 'coordinate'\n  | 'encode'\n  | 'mark'\n  | 'palette'\n  | 'scale'\n  | 'shape'\n  | 'theme'\n  | 'transform'\n  | 'component'\n  | 'animation'\n  | 'action'\n  | 'interaction'\n  | 'composition'\n  | 'data'\n  | 'labelTransform';\n\nexport type G2Component =\n  | EncodeComponent\n  | ScaleComponent\n  | CoordinateComponent\n  | PaletteComponent\n  | MarkComponent\n  | ShapeComponent\n  | ThemeComponent\n  | GuideComponentComponent\n  | AnimationComponent\n  | InteractionComponent\n  | CompositionComponent\n  | TransformComponent\n  | DataComponent\n  | LabelTransformComponent;\n\nexport type G2ComponentValue =\n  | Transform\n  | Encode\n  | Scale\n  | CoordinateTransform\n  | Palette\n  | Mark\n  | Shape\n  | Theme\n  | GuideComponent\n  | Animation\n  | Interaction\n  | Composition\n  | LabelTransform;\n\nexport type G2BaseComponent<\n  R = any,\n  O = Record<string, unknown>,\n  P = Record<string, unknown>,\n  C = Record<string, unknown>,\n> = {\n  (options?: O, context?: C): R;\n  props?: P;\n};\n\nexport type InferredEncode = {\n  type?: string;\n  [key: string | symbol]: any;\n};\nexport type InferredStatistic = {\n  type?: string | ((...args: any[]) => any);\n  [key: string | symbol]: any;\n};\nexport type InferValue = {\n  encode?: InferredEncode;\n  transform?: (\n    indexedValue: IndexedValue,\n    statistic: InferredStatistic[],\n  ) => InferredStatistic[];\n};\n\nexport type Scale = {\n  map: (x: any) => any;\n  invert: (x: any) => any;\n  getTicks?: () => any[];\n  getBandWidth?: (d?: any) => number;\n  getFormatter?: () => (x: any) => string;\n  getOptions: () => Record<string, any>;\n  update(options: Record<string, any>): void;\n  clone: () => Scale;\n};\nexport type ScaleComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Scale,\n  O\n>;\n\nexport type CoordinateTransform = Transformation[];\nexport type CoordinateProps = {\n  transform?: boolean;\n};\nexport type CoordinateComponent<O = Record<string, unknown>> = G2BaseComponent<\n  CoordinateTransform,\n  O,\n  CoordinateProps\n>;\n\nexport type Palette = string[];\nexport type PaletteComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Palette,\n  O\n>;\n\nexport type Shape = (\n  points: Vector2[],\n  value: {\n    color?: string;\n    index?: number;\n    [key: string]: any;\n  },\n  defaults?: Record<string, any>,\n  point2d?: Vector2[][],\n) => DisplayObject;\nexport type ShapeProps = {\n  defaultMarker?: string;\n  defaultEnterAnimation?: string;\n  defaultUpdateAnimation?: string;\n  defaultExitAnimation?: string;\n};\nexport type ShapeContext = {\n  document: IDocument;\n  coordinate: Coordinate;\n  [key: string]: any; // TODO\n};\nexport type ShapeComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Shape,\n  O,\n  ShapeProps,\n  ShapeContext\n>;\n\nexport type Theme = G2Theme;\nexport type ThemeComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Theme,\n  O\n>;\n\nexport type GuideComponentContext = {\n  coordinate: Coordinate;\n  library: G2Library;\n  markState: Map<G2Mark, G2MarkState>;\n  scales: Scale[];\n  scale: Record<string, Scale>;\n  theme: G2Theme;\n  value: Record<string, any>;\n};\n\nexport type GuideComponent = (context: GuideComponentContext) => DisplayObject;\n\nexport type GuideComponentProps = {\n  defaultPosition?: GuideComponentPosition;\n  defaultPlane?: GuideComponentPlane;\n  defaultOrientation?: GuideComponentOrientation;\n  defaultSize?: number;\n  defaultOrder?: number;\n  defaultPadding?: [number, number];\n  defaultCrossPadding?: [number, number];\n  [key: string]: any;\n};\n\nexport type GuideComponentComponent<O = Record<string, unknown>> =\n  G2BaseComponent<GuideComponent, O, GuideComponentProps>;\n\nexport type Animation = (\n  from: DisplayObject[],\n  to: DisplayObject[],\n  defaults: Record<string, any>,\n) => GAnimation | GAnimation[];\n\nexport type AnimationContext = {\n  coordinate: Coordinate;\n  [key: string]: any; // TODO\n};\n\nexport type AnimationProps = Record<string, unknown>;\n\nexport type AnimationComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Animation,\n  O,\n  AnimationProps,\n  AnimationContext\n>;\n\nexport type Interaction = (\n  target: G2ViewInstance,\n  viewInstances: G2ViewInstance[],\n  emitter?: EventEmitter,\n) => void;\nexport type InteractionComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Interaction,\n  O\n>;\n\nexport type Composition = (\n  children: G2ViewTree,\n) =>\n  | G2ViewTree[]\n  | (() => Generator<G2ViewTree, void, void>)\n  | Promise<G2ViewTree[]>;\nexport type CompositionComponent<O = Record<string, unknown>> = G2BaseComponent<\n  Composition,\n  O\n>;\n\nexport type LabelTransform = (\n  labels: DisplayObject[],\n  context: {\n    coordinate: Coordinate;\n    canvas: Canvas;\n    layout: Layout;\n  },\n) => DisplayObject[];\nexport type LabelTransformComponent<O = Record<string, unknown>> =\n  G2BaseComponent<LabelTransform, O>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}