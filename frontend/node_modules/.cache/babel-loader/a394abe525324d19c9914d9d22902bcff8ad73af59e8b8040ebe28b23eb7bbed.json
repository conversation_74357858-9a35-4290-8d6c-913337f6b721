{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/spec/scale.ts"], "sourcesContent": ["import {\n  LinearOptions,\n  OrdinalOptions,\n  IdentityOptions,\n  BandOptions,\n  PointOptions,\n  TimeOptions,\n  LogOptions,\n  PowOptions,\n  ThresholdOptions,\n  QuantileOptions,\n  QuantizeOptions,\n  SqrtOptions,\n  SequentialOptions,\n  ConstantOptions,\n  Linear,\n  Log,\n  Pow,\n  Sqrt,\n  Time,\n} from '@antv/scale';\nimport { ScaleComponent } from '../runtime';\nimport { Palette } from './palette';\n\nexport type Scale =\n  | LinearScale\n  | OrdinalScale\n  | IdentityScale\n  | BandScale\n  | PointScale\n  | TimeScale\n  | LogScale\n  | PowScale\n  | SqrtScale\n  | ThresholdScale\n  | QuantizeScale\n  | QuantileScale\n  | SequentialScale\n  | CustomScale\n  | ConstantScale;\n\nexport type ScaleTypes =\n  | 'linear'\n  | 'ordinal'\n  | 'identity'\n  | 'band'\n  | 'point'\n  | 'time'\n  | 'log'\n  | 'pow'\n  | 'sqrt'\n  | 'threshold'\n  | 'quantize'\n  | 'quantile'\n  | 'sequential'\n  | 'constant'\n  | ScaleComponent;\n\ntype QuantitativeScale = Linear | Log | Pow | Sqrt | Time;\n\nexport type BaseScale<T extends ScaleTypes, O> = {\n  type?: T;\n  palette?: Palette['type'] | string;\n  rangeMax?: number;\n  rangeMin?: number;\n  domainMax?: number;\n  domainMin?: number;\n  key?: string;\n  facet?: boolean;\n  independent?: boolean;\n  zero?: boolean;\n  offset?: (t: number) => number;\n  relations?: [any, any][];\n  groupTransform?: (scales: QuantitativeScale[]) => void;\n} & O;\n\nexport type LinearScale = BaseScale<'linear', LinearOptions>;\n\nexport type OrdinalScale = BaseScale<'ordinal', OrdinalOptions>;\n\nexport type IdentityScale = BaseScale<'identity', IdentityOptions>;\n\nexport type BandScale = BaseScale<'band', BandOptions>;\n\nexport type PointScale = BaseScale<'point', PointOptions>;\n\nexport type TimeScale = BaseScale<'time', TimeOptions>;\n\nexport type LogScale = BaseScale<'log', LogOptions>;\n\nexport type PowScale = BaseScale<'pow', PowOptions>;\n\nexport type SqrtScale = BaseScale<'sqrt', SqrtOptions>;\n\nexport type ThresholdScale = BaseScale<'threshold', ThresholdOptions>;\n\nexport type QuantileScale = BaseScale<'quantile', QuantileOptions>;\n\nexport type QuantizeScale = BaseScale<'quantize', QuantizeOptions>;\n\nexport type SequentialScale = BaseScale<'sequential', SequentialOptions>;\n\nexport type ConstantScale = BaseScale<'constant', ConstantOptions>;\n\nexport type CustomScale = BaseScale<ScaleComponent, { [key: string]: any }>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}