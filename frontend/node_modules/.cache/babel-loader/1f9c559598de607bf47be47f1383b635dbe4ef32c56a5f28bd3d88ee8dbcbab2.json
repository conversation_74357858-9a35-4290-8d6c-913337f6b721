{"ast": null, "code": "import Cell from \"./Cell\";\nimport Row from \"./Row\";\n/**\n * Syntactic sugar. Do not support HOC.\n */\nfunction Summary(_ref) {\n  var children = _ref.children;\n  return children;\n}\nSummary.Row = Row;\nSummary.Cell = Cell;\nexport default Summary;", "map": {"version": 3, "names": ["Cell", "Row", "Summary", "_ref", "children"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-table/es/Footer/Summary.js"], "sourcesContent": ["import Cell from \"./Cell\";\nimport Row from \"./Row\";\n/**\n * Syntactic sugar. Do not support HOC.\n */\nfunction Summary(_ref) {\n  var children = _ref.children;\n  return children;\n}\nSummary.Row = Row;\nSummary.Cell = Cell;\nexport default Summary;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,GAAG,MAAM,OAAO;AACvB;AACA;AACA;AACA,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB;AACAF,OAAO,CAACD,GAAG,GAAGA,GAAG;AACjBC,OAAO,CAACF,IAAI,GAAGA,IAAI;AACnB,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}