{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Modal, Form, InputNumber, DatePicker, message, Popconfirm, Card, Tag, Input, Tooltip, Alert, Select } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, CheckOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { returnService } from '../services/returnService';\nimport { borrowService } from '../services/borrowService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst ReturnManagement = () => {\n  _s();\n  const [returns, setReturns] = useState([]);\n  const [availableBorrows, setAvailableBorrows] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingReturn, setEditingReturn] = useState(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [searchParams, setSearchParams] = useState({\n    userName: '',\n    bookName: '',\n    status: undefined\n  });\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchReturns();\n    fetchAvailableBorrows();\n  }, [pagination.current, pagination.pageSize]);\n  const fetchReturns = async () => {\n    setLoading(true);\n    try {\n      const params = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams\n      };\n      const response = await returnService.getReturns(params);\n      setReturns(response.records);\n      setPagination(prev => ({\n        ...prev,\n        total: response.total\n      }));\n    } catch (error) {\n      message.error('获取归还记录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchAvailableBorrows = async () => {\n    try {\n      // 获取已借出但未归还的借阅记录\n      const response = await borrowService.getBorrows({\n        current: 1,\n        size: 100,\n        status: '已借出'\n      });\n      setAvailableBorrows((response === null || response === void 0 ? void 0 : response.records) || []);\n    } catch (error) {\n      console.error('获取可归还图书失败:', error);\n      message.error('获取可归还图书失败');\n      setAvailableBorrows([]);\n    }\n  };\n  const handleAdd = () => {\n    setEditingReturn(null);\n    setModalVisible(true);\n    form.resetFields();\n    // 设置默认归还时间为当前时间\n    form.setFieldsValue({\n      returnTime: dayjs()\n    });\n  };\n  const handleEdit = record => {\n    setEditingReturn(record);\n    setModalVisible(true);\n    form.setFieldsValue({\n      ...record,\n      returnTime: record.returnTime ? dayjs(record.returnTime) : null\n    });\n  };\n  const handleDelete = async id => {\n    try {\n      await returnService.deleteReturn(id);\n      message.success('删除成功');\n      fetchReturns();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleApprove = async id => {\n    try {\n      await returnService.approveReturn(id);\n      message.success('审核通过');\n      fetchReturns();\n      fetchAvailableBorrows(); // 刷新可借阅列表\n    } catch (error) {\n      message.error('审核失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$returnTime;\n      const submitData = {\n        ...values,\n        returnTime: (_values$returnTime = values.returnTime) === null || _values$returnTime === void 0 ? void 0 : _values$returnTime.format('YYYY-MM-DD HH:mm:ss'),\n        status: editingReturn ? editingReturn.status : '待审核'\n      };\n      if (editingReturn) {\n        await returnService.updateReturn({\n          ...editingReturn,\n          ...submitData\n        });\n        message.success('更新成功');\n      } else {\n        await returnService.createReturn(submitData);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchReturns();\n      fetchAvailableBorrows();\n    } catch (error) {\n      message.error(editingReturn ? '更新失败' : '创建失败');\n    }\n  };\n  const handleSearch = () => {\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    fetchReturns();\n  };\n  const handleReset = () => {\n    setSearchParams({\n      userName: '',\n      bookName: '',\n      status: undefined\n    });\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    setTimeout(fetchReturns, 100);\n  };\n  const calculateFine = (borrowId, returnTime) => {\n    const borrow = (availableBorrows || []).find(b => b.id === borrowId);\n    if (!borrow) return 0;\n    const expectedReturn = dayjs(borrow.expectedReturnTime);\n    const actualReturn = dayjs(returnTime);\n    if (actualReturn.isAfter(expectedReturn)) {\n      const overdueDays = actualReturn.diff(expectedReturn, 'day');\n      return overdueDays * 1; // 每天1元罚金\n    }\n    return 0;\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case '待审核':\n        return 'orange';\n      case '已归还':\n        return 'green';\n      case '已拒绝':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '用户姓名',\n    dataIndex: 'userName',\n    key: 'userName',\n    width: 120\n  }, {\n    title: '图书名称',\n    dataIndex: 'bookName',\n    key: 'bookName',\n    ellipsis: true\n  }, {\n    title: '归还时间',\n    dataIndex: 'returnTime',\n    key: 'returnTime',\n    width: 150,\n    render: time => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'\n  }, {\n    title: '罚金',\n    dataIndex: 'fine',\n    key: 'fine',\n    width: 100,\n    render: fine => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: fine > 0 ? '#ff4d4f' : '#52c41a'\n      },\n      children: [\"\\xA5\", fine || 0]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [record.status === '待审核' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5BA1\\u6838\\u901A\\u8FC7\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 23\n          }, this),\n          onClick: () => handleApprove(record.id),\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u6761\\u5F52\\u8FD8\\u8BB0\\u5F55\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u5F52\\u8FD8\\u7BA1\\u7406\\u8BF4\\u660E\",\n      description: \"\\u7BA1\\u7406\\u56FE\\u4E66\\u5F52\\u8FD8\\u8BB0\\u5F55\\uFF0C\\u5305\\u62EC\\u5BA1\\u6838\\u5F52\\u8FD8\\u7533\\u8BF7\\u3001\\u8BA1\\u7B97\\u7F5A\\u91D1\\u7B49\\u3002\\u903E\\u671F\\u5F52\\u8FD8\\u7684\\u56FE\\u4E66\\u4F1A\\u81EA\\u52A8\\u8BA1\\u7B97\\u7F5A\\u91D1\\uFF08\\u6BCF\\u59291\\u5143\\uFF09\\u3002\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 16,\n        padding: 16,\n        background: '#fafafa',\n        borderRadius: 6\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u7528\\u6237\\u59D3\\u540D\",\n          value: searchParams.userName,\n          onChange: e => setSearchParams(prev => ({\n            ...prev,\n            userName: e.target.value\n          })),\n          style: {\n            width: 200\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u56FE\\u4E66\\u540D\\u79F0\",\n          value: searchParams.bookName,\n          onChange: e => setSearchParams(prev => ({\n            ...prev,\n            bookName: e.target.value\n          })),\n          style: {\n            width: 200\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n          value: searchParams.status,\n          onChange: value => setSearchParams(prev => ({\n            ...prev,\n            status: value\n          })),\n          style: {\n            width: 200\n          },\n          allowClear: true,\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"\\u5F85\\u5BA1\\u6838\",\n            children: \"\\u5F85\\u5BA1\\u6838\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"\\u5DF2\\u5F52\\u8FD8\",\n            children: \"\\u5DF2\\u5F52\\u8FD8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"\\u5DF2\\u62D2\\u7EDD\",\n            children: \"\\u5DF2\\u62D2\\u7EDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 40\n          }, this),\n          onClick: handleSearch,\n          children: \"\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleReset,\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u65B0\\u589E\\u5F52\\u8FD8\\u8BB0\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: returns,\n      loading: loading,\n      pagination: {\n        ...pagination,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: total => `共 ${total} 条记录`,\n        onChange: (page, pageSize) => {\n          setPagination(prev => ({\n            ...prev,\n            current: page,\n            pageSize: pageSize || 10\n          }));\n        }\n      },\n      rowKey: \"id\",\n      scroll: {\n        x: 1200\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingReturn ? '编辑归还记录' : '新增归还记录',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        onValuesChange: changedValues => {\n          if (changedValues.borrowId || changedValues.returnTime) {\n            const borrowId = form.getFieldValue('borrowId');\n            const returnTime = form.getFieldValue('returnTime');\n            if (borrowId && returnTime) {\n              const fine = calculateFine(borrowId, returnTime.format('YYYY-MM-DD HH:mm:ss'));\n              form.setFieldsValue({\n                fine\n              });\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"borrowId\",\n          label: \"\\u9009\\u62E9\\u501F\\u9605\\u8BB0\\u5F55\",\n          rules: [{\n            required: true,\n            message: '请选择借阅记录'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u5F52\\u8FD8\\u7684\\u501F\\u9605\\u8BB0\\u5F55\",\n            showSearch: true,\n            optionFilterProp: \"children\",\n            children: (availableBorrows || []).map(borrow => /*#__PURE__*/_jsxDEV(Option, {\n              value: borrow.id,\n              children: [borrow.userName, \" - \", borrow.bookName, \" (\\u501F\\u9605\\u65F6\\u95F4: \", dayjs(borrow.borrowTime).format('YYYY-MM-DD'), \")\"]\n            }, borrow.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"returnTime\",\n          label: \"\\u5F52\\u8FD8\\u65F6\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择归还时间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(DatePicker, {\n            showTime: true,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F52\\u8FD8\\u65F6\\u95F4\",\n            style: {\n              width: '100%'\n            },\n            format: \"YYYY-MM-DD HH:mm:ss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"fine\",\n          label: \"\\u7F5A\\u91D1\\u91D1\\u989D\",\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            placeholder: \"\\u7CFB\\u7EDF\\u81EA\\u52A8\\u8BA1\\u7B97\",\n            min: 0,\n            precision: 2,\n            style: {\n              width: '100%'\n            },\n            addonBefore: \"\\xA5\",\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n_s(ReturnManagement, \"gtv4jU8lEWloA9AG7bW8hJo65ms=\", false, function () {\n  return [Form.useForm];\n});\n_c = ReturnManagement;\nexport default ReturnManagement;\nvar _c;\n$RefreshReg$(_c, \"ReturnManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Modal", "Form", "InputNumber", "DatePicker", "message", "Popconfirm", "Card", "Tag", "Input", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Select", "PlusOutlined", "EditOutlined", "DeleteOutlined", "SearchOutlined", "CheckOutlined", "dayjs", "returnService", "borrowService", "jsxDEV", "_jsxDEV", "Option", "ReturnManagement", "_s", "returns", "setReturns", "availableBorrows", "setAvailableBorrows", "loading", "setLoading", "modalVisible", "setModalVisible", "editingReturn", "setEditingReturn", "pagination", "setPagination", "current", "pageSize", "total", "searchParams", "setSearchParams", "userName", "bookName", "status", "undefined", "form", "useForm", "fetchReturns", "fetchAvailableBorrows", "params", "size", "response", "getReturns", "records", "prev", "error", "getBorrows", "console", "handleAdd", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnTime", "handleEdit", "record", "handleDelete", "id", "deleteReturn", "success", "handleApprove", "approveReturn", "handleSubmit", "values", "_values$returnTime", "submitData", "format", "updateReturn", "createReturn", "handleSearch", "handleReset", "setTimeout", "calculateFine", "borrowId", "borrow", "find", "b", "expectedReturn", "expectedReturnTime", "actualReturn", "isAfter", "overdueDays", "diff", "getStatusColor", "columns", "title", "dataIndex", "key", "width", "ellipsis", "render", "time", "fine", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "description", "showIcon", "marginBottom", "padding", "background", "borderRadius", "wrap", "placeholder", "value", "onChange", "e", "target", "allowClear", "dataSource", "showSizeChanger", "showQuickJumper", "showTotal", "page", "<PERSON><PERSON><PERSON>", "scroll", "x", "open", "onCancel", "onOk", "submit", "layout", "onFinish", "onValuesChange", "changedValues", "getFieldValue", "<PERSON><PERSON>", "name", "label", "rules", "required", "showSearch", "optionFilterProp", "map", "borrowTime", "showTime", "min", "precision", "addonBefore", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Modal,\n  Form,\n  InputNumber,\n  DatePicker,\n  message,\n  Popconfirm,\n  Card,\n  Tag,\n  Input,\n  Tooltip,\n  Alert,\n  Select,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  CheckOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { BookReturn, BookBorrow, PageParams } from '../types';\nimport { returnService } from '../services/returnService';\nimport { borrowService } from '../services/borrowService';\n\nconst { Option } = Select;\n\nconst ReturnManagement: React.FC = () => {\n  const [returns, setReturns] = useState<BookReturn[]>([]);\n  const [availableBorrows, setAvailableBorrows] = useState<BookBorrow[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingReturn, setEditingReturn] = useState<BookReturn | null>(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [searchParams, setSearchParams] = useState({\n    userName: '',\n    bookName: '',\n    status: undefined as string | undefined,\n  });\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchReturns();\n    fetchAvailableBorrows();\n  }, [pagination.current, pagination.pageSize]);\n\n  const fetchReturns = async () => {\n    setLoading(true);\n    try {\n      const params: PageParams = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams,\n      };\n      const response = await returnService.getReturns(params);\n      setReturns(response.records);\n      setPagination(prev => ({\n        ...prev,\n        total: response.total,\n      }));\n    } catch (error) {\n      message.error('获取归还记录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchAvailableBorrows = async () => {\n    try {\n      // 获取已借出但未归还的借阅记录\n      const response = await borrowService.getBorrows({\n        current: 1,\n        size: 100,\n        status: '已借出'\n      });\n      setAvailableBorrows(response?.records || []);\n    } catch (error) {\n      console.error('获取可归还图书失败:', error);\n      message.error('获取可归还图书失败');\n      setAvailableBorrows([]);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingReturn(null);\n    setModalVisible(true);\n    form.resetFields();\n    // 设置默认归还时间为当前时间\n    form.setFieldsValue({\n      returnTime: dayjs(),\n    });\n  };\n\n  const handleEdit = (record: BookReturn) => {\n    setEditingReturn(record);\n    setModalVisible(true);\n    form.setFieldsValue({\n      ...record,\n      returnTime: record.returnTime ? dayjs(record.returnTime) : null,\n    });\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await returnService.deleteReturn(id);\n      message.success('删除成功');\n      fetchReturns();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleApprove = async (id: number) => {\n    try {\n      await returnService.approveReturn(id);\n      message.success('审核通过');\n      fetchReturns();\n      fetchAvailableBorrows(); // 刷新可借阅列表\n    } catch (error) {\n      message.error('审核失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const submitData = {\n        ...values,\n        returnTime: values.returnTime?.format('YYYY-MM-DD HH:mm:ss'),\n        status: editingReturn ? editingReturn.status : '待审核',\n      };\n\n      if (editingReturn) {\n        await returnService.updateReturn({ ...editingReturn, ...submitData });\n        message.success('更新成功');\n      } else {\n        await returnService.createReturn(submitData);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchReturns();\n      fetchAvailableBorrows();\n    } catch (error) {\n      message.error(editingReturn ? '更新失败' : '创建失败');\n    }\n  };\n\n  const handleSearch = () => {\n    setPagination(prev => ({ ...prev, current: 1 }));\n    fetchReturns();\n  };\n\n  const handleReset = () => {\n    setSearchParams({\n      userName: '',\n      bookName: '',\n      status: undefined,\n    });\n    setPagination(prev => ({ ...prev, current: 1 }));\n    setTimeout(fetchReturns, 100);\n  };\n\n  const calculateFine = (borrowId: number, returnTime: string) => {\n    const borrow = (availableBorrows || []).find(b => b.id === borrowId);\n    if (!borrow) return 0;\n\n    const expectedReturn = dayjs(borrow.expectedReturnTime);\n    const actualReturn = dayjs(returnTime);\n\n    if (actualReturn.isAfter(expectedReturn)) {\n      const overdueDays = actualReturn.diff(expectedReturn, 'day');\n      return overdueDays * 1; // 每天1元罚金\n    }\n    return 0;\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case '待审核':\n        return 'orange';\n      case '已归还':\n        return 'green';\n      case '已拒绝':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const columns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户姓名',\n      dataIndex: 'userName',\n      key: 'userName',\n      width: 120,\n    },\n    {\n      title: '图书名称',\n      dataIndex: 'bookName',\n      key: 'bookName',\n      ellipsis: true,\n    },\n    {\n      title: '归还时间',\n      dataIndex: 'returnTime',\n      key: 'returnTime',\n      width: 150,\n      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',\n    },\n    {\n      title: '罚金',\n      dataIndex: 'fine',\n      key: 'fine',\n      width: 100,\n      render: (fine: number) => (\n        <span style={{ color: fine > 0 ? '#ff4d4f' : '#52c41a' }}>\n          ¥{fine || 0}\n        </span>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>{status}</Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_: any, record: BookReturn) => (\n        <Space size=\"small\">\n          {record.status === '待审核' && (\n            <Tooltip title=\"审核通过\">\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<CheckOutlined />}\n                onClick={() => handleApprove(record.id)}\n                style={{ color: '#52c41a' }}\n              />\n            </Tooltip>\n          )}\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这条归还记录吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"link\"\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <Card>\n      {/* 提示信息 */}\n      <Alert\n        message=\"归还管理说明\"\n        description=\"管理图书归还记录，包括审核归还申请、计算罚金等。逾期归还的图书会自动计算罚金（每天1元）。\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 16 }}\n      />\n\n      {/* 搜索区域 */}\n      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>\n        <Space wrap>\n          <Input\n            placeholder=\"用户姓名\"\n            value={searchParams.userName}\n            onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}\n            style={{ width: 200 }}\n          />\n          <Input\n            placeholder=\"图书名称\"\n            value={searchParams.bookName}\n            onChange={(e) => setSearchParams(prev => ({ ...prev, bookName: e.target.value }))}\n            style={{ width: 200 }}\n          />\n          <Select\n            placeholder=\"选择状态\"\n            value={searchParams.status}\n            onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}\n            style={{ width: 200 }}\n            allowClear\n          >\n            <Option value=\"待审核\">待审核</Option>\n            <Option value=\"已归还\">已归还</Option>\n            <Option value=\"已拒绝\">已拒绝</Option>\n          </Select>\n          <Button type=\"primary\" icon={<SearchOutlined />} onClick={handleSearch}>\n            搜索\n          </Button>\n          <Button onClick={handleReset}>重置</Button>\n        </Space>\n      </div>\n\n      {/* 操作按钮 */}\n      <div style={{ marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          新增归还记录\n        </Button>\n      </div>\n\n      {/* 表格 */}\n      <Table\n        columns={columns}\n        dataSource={returns}\n        loading={loading}\n        pagination={{\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10,\n            }));\n          },\n        }}\n        rowKey=\"id\"\n        scroll={{ x: 1200 }}\n      />\n\n      {/* 编辑/新增模态框 */}\n      <Modal\n        title={editingReturn ? '编辑归还记录' : '新增归还记录'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          onValuesChange={(changedValues) => {\n            if (changedValues.borrowId || changedValues.returnTime) {\n              const borrowId = form.getFieldValue('borrowId');\n              const returnTime = form.getFieldValue('returnTime');\n              if (borrowId && returnTime) {\n                const fine = calculateFine(borrowId, returnTime.format('YYYY-MM-DD HH:mm:ss'));\n                form.setFieldsValue({ fine });\n              }\n            }\n          }}\n        >\n          <Form.Item\n            name=\"borrowId\"\n            label=\"选择借阅记录\"\n            rules={[{ required: true, message: '请选择借阅记录' }]}\n          >\n            <Select placeholder=\"请选择要归还的借阅记录\" showSearch optionFilterProp=\"children\">\n              {(availableBorrows || []).map(borrow => (\n                <Option key={borrow.id} value={borrow.id}>\n                  {borrow.userName} - {borrow.bookName} (借阅时间: {dayjs(borrow.borrowTime).format('YYYY-MM-DD')})\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"returnTime\"\n            label=\"归还时间\"\n            rules={[{ required: true, message: '请选择归还时间' }]}\n          >\n            <DatePicker\n              showTime\n              placeholder=\"请选择归还时间\"\n              style={{ width: '100%' }}\n              format=\"YYYY-MM-DD HH:mm:ss\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"fine\"\n            label=\"罚金金额\"\n          >\n            <InputNumber\n              placeholder=\"系统自动计算\"\n              min={0}\n              precision={2}\n              style={{ width: '100%' }}\n              addonBefore=\"¥\"\n              disabled\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Card>\n  );\n};\n\nexport default ReturnManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,QACR,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAEzB,MAAMY,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAe,EAAE,CAAC;EACxD,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAe,EAAE,CAAC;EAC1E,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAoB,IAAI,CAAC;EAC3E,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC;IAC3C0C,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC;IAC/C+C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAEC;EACV,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC;EAE7BnD,SAAS,CAAC,MAAM;IACdoD,YAAY,CAAC,CAAC;IACdC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC;EAE7C,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BlB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoB,MAAkB,GAAG;QACzBb,OAAO,EAAEF,UAAU,CAACE,OAAO;QAC3Bc,IAAI,EAAEhB,UAAU,CAACG,QAAQ;QACzB,GAAGE;MACL,CAAC;MACD,MAAMY,QAAQ,GAAG,MAAMlC,aAAa,CAACmC,UAAU,CAACH,MAAM,CAAC;MACvDxB,UAAU,CAAC0B,QAAQ,CAACE,OAAO,CAAC;MAC5BlB,aAAa,CAACmB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPhB,KAAK,EAAEa,QAAQ,CAACb;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF;MACA,MAAMG,QAAQ,GAAG,MAAMjC,aAAa,CAACsC,UAAU,CAAC;QAC9CpB,OAAO,EAAE,CAAC;QACVc,IAAI,EAAE,GAAG;QACTP,MAAM,EAAE;MACV,CAAC,CAAC;MACFhB,mBAAmB,CAAC,CAAAwB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,OAAO,KAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCpD,OAAO,CAACoD,KAAK,CAAC,WAAW,CAAC;MAC1B5B,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAM+B,SAAS,GAAGA,CAAA,KAAM;IACtBzB,gBAAgB,CAAC,IAAI,CAAC;IACtBF,eAAe,CAAC,IAAI,CAAC;IACrBc,IAAI,CAACc,WAAW,CAAC,CAAC;IAClB;IACAd,IAAI,CAACe,cAAc,CAAC;MAClBC,UAAU,EAAE7C,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8C,UAAU,GAAIC,MAAkB,IAAK;IACzC9B,gBAAgB,CAAC8B,MAAM,CAAC;IACxBhC,eAAe,CAAC,IAAI,CAAC;IACrBc,IAAI,CAACe,cAAc,CAAC;MAClB,GAAGG,MAAM;MACTF,UAAU,EAAEE,MAAM,CAACF,UAAU,GAAG7C,KAAK,CAAC+C,MAAM,CAACF,UAAU,CAAC,GAAG;IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMhD,aAAa,CAACiD,YAAY,CAACD,EAAE,CAAC;MACpC9D,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACvBpB,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMa,aAAa,GAAG,MAAOH,EAAU,IAAK;IAC1C,IAAI;MACF,MAAMhD,aAAa,CAACoD,aAAa,CAACJ,EAAE,CAAC;MACrC9D,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACvBpB,YAAY,CAAC,CAAC;MACdC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMe,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MAAA,IAAAC,kBAAA;MACF,MAAMC,UAAU,GAAG;QACjB,GAAGF,MAAM;QACTV,UAAU,GAAAW,kBAAA,GAAED,MAAM,CAACV,UAAU,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmBE,MAAM,CAAC,qBAAqB,CAAC;QAC5D/B,MAAM,EAAEX,aAAa,GAAGA,aAAa,CAACW,MAAM,GAAG;MACjD,CAAC;MAED,IAAIX,aAAa,EAAE;QACjB,MAAMf,aAAa,CAAC0D,YAAY,CAAC;UAAE,GAAG3C,aAAa;UAAE,GAAGyC;QAAW,CAAC,CAAC;QACrEtE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMlD,aAAa,CAAC2D,YAAY,CAACH,UAAU,CAAC;QAC5CtE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACzB;MACApC,eAAe,CAAC,KAAK,CAAC;MACtBgB,YAAY,CAAC,CAAC;MACdC,qBAAqB,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAACvB,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;IAChD;EACF,CAAC;EAED,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzB1C,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDW,YAAY,CAAC,CAAC;EAChB,CAAC;EAED,MAAM+B,WAAW,GAAGA,CAAA,KAAM;IACxBtC,eAAe,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAEC;IACV,CAAC,CAAC;IACFT,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChD2C,UAAU,CAAChC,YAAY,EAAE,GAAG,CAAC;EAC/B,CAAC;EAED,MAAMiC,aAAa,GAAGA,CAACC,QAAgB,EAAEpB,UAAkB,KAAK;IAC9D,MAAMqB,MAAM,GAAG,CAACxD,gBAAgB,IAAI,EAAE,EAAEyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKgB,QAAQ,CAAC;IACpE,IAAI,CAACC,MAAM,EAAE,OAAO,CAAC;IAErB,MAAMG,cAAc,GAAGrE,KAAK,CAACkE,MAAM,CAACI,kBAAkB,CAAC;IACvD,MAAMC,YAAY,GAAGvE,KAAK,CAAC6C,UAAU,CAAC;IAEtC,IAAI0B,YAAY,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;MACxC,MAAMI,WAAW,GAAGF,YAAY,CAACG,IAAI,CAACL,cAAc,EAAE,KAAK,CAAC;MAC5D,OAAOI,WAAW,GAAG,CAAC,CAAC,CAAC;IAC1B;IACA,OAAO,CAAC;EACV,CAAC;EAED,MAAME,cAAc,GAAIhD,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMiD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,IAAY,IAAKA,IAAI,GAAGnF,KAAK,CAACmF,IAAI,CAAC,CAACzB,MAAM,CAAC,kBAAkB,CAAC,GAAG;EAC5E,CAAC,EACD;IACEmB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGE,IAAY,iBACnBhF,OAAA;MAAMiF,KAAK,EAAE;QAAEC,KAAK,EAAEF,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAG,QAAA,GAAC,MACvD,EAACH,IAAI,IAAI,CAAC;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAEV,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGvD,MAAc,iBACrBvB,OAAA,CAACd,GAAG;MAACgG,KAAK,EAAEX,cAAc,CAAChD,MAAM,CAAE;MAAA4D,QAAA,EAAE5D;IAAM;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAErD,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACU,CAAM,EAAE7C,MAAkB,kBACjC3C,OAAA,CAACtB,KAAK;MAACoD,IAAI,EAAC,OAAO;MAAAqD,QAAA,GAChBxC,MAAM,CAACpB,MAAM,KAAK,KAAK,iBACtBvB,OAAA,CAACZ,OAAO;QAACqF,KAAK,EAAC,0BAAM;QAAAU,QAAA,eACnBnF,OAAA,CAACvB,MAAM;UACLgH,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,OAAO;UACZ4D,IAAI,eAAE1F,OAAA,CAACL,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBI,OAAO,EAAEA,CAAA,KAAM3C,aAAa,CAACL,MAAM,CAACE,EAAE,CAAE;UACxCoC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CACV,eACDvF,OAAA,CAACZ,OAAO;QAACqF,KAAK,EAAC,cAAI;QAAAU,QAAA,eACjBnF,OAAA,CAACvB,MAAM;UACLgH,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,OAAO;UACZ4D,IAAI,eAAE1F,OAAA,CAACR,YAAY;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBI,OAAO,EAAEA,CAAA,KAAMjD,UAAU,CAACC,MAAM;QAAE;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVvF,OAAA,CAAChB,UAAU;QACTyF,KAAK,EAAC,gFAAe;QACrBmB,SAAS,EAAEA,CAAA,KAAMhD,YAAY,CAACD,MAAM,CAACE,EAAE,CAAE;QACzCgD,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEfnF,OAAA,CAACZ,OAAO;UAACqF,KAAK,EAAC,cAAI;UAAAU,QAAA,eACjBnF,OAAA,CAACvB,MAAM;YACLgH,IAAI,EAAC,MAAM;YACX3D,IAAI,EAAC,OAAO;YACZiE,MAAM;YACNL,IAAI,eAAE1F,OAAA,CAACP,cAAc;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEvF,OAAA,CAACf,IAAI;IAAAkG,QAAA,gBAEHnF,OAAA,CAACX,KAAK;MACJN,OAAO,EAAC,sCAAQ;MAChBiH,WAAW,EAAC,2QAA+C;MAC3DP,IAAI,EAAC,MAAM;MACXQ,QAAQ;MACRhB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGFvF,OAAA;MAAKiF,KAAK,EAAE;QAAEiB,YAAY,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAlB,QAAA,eACpFnF,OAAA,CAACtB,KAAK;QAAC4H,IAAI;QAAAnB,QAAA,gBACTnF,OAAA,CAACb,KAAK;UACJoH,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAErF,YAAY,CAACE,QAAS;UAC7BoF,QAAQ,EAAGC,CAAC,IAAKtF,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEb,QAAQ,EAAEqF,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAClFvB,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAI;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFvF,OAAA,CAACb,KAAK;UACJoH,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAErF,YAAY,CAACG,QAAS;UAC7BmF,QAAQ,EAAGC,CAAC,IAAKtF,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEZ,QAAQ,EAAEoF,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAClFvB,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAI;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFvF,OAAA,CAACV,MAAM;UACLiH,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAErF,YAAY,CAACI,MAAO;UAC3BkF,QAAQ,EAAGD,KAAK,IAAKpF,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEX,MAAM,EAAEiF;UAAM,CAAC,CAAC,CAAE;UAC3EvB,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAI,CAAE;UACtBgC,UAAU;UAAAzB,QAAA,gBAEVnF,OAAA,CAACC,MAAM;YAACuG,KAAK,EAAC,oBAAK;YAAArB,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCvF,OAAA,CAACC,MAAM;YAACuG,KAAK,EAAC,oBAAK;YAAArB,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCvF,OAAA,CAACC,MAAM;YAACuG,KAAK,EAAC,oBAAK;YAAArB,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACTvF,OAAA,CAACvB,MAAM;UAACgH,IAAI,EAAC,SAAS;UAACC,IAAI,eAAE1F,OAAA,CAACN,cAAc;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACI,OAAO,EAAElC,YAAa;UAAA0B,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvF,OAAA,CAACvB,MAAM;UAACkH,OAAO,EAAEjC,WAAY;UAAAyB,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvF,OAAA;MAAKiF,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAf,QAAA,eAC/BnF,OAAA,CAACvB,MAAM;QACLgH,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE1F,OAAA,CAACT,YAAY;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBI,OAAO,EAAErD,SAAU;QAAA6C,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvF,OAAA,CAACxB,KAAK;MACJgG,OAAO,EAAEA,OAAQ;MACjBqC,UAAU,EAAEzG,OAAQ;MACpBI,OAAO,EAAEA,OAAQ;MACjBM,UAAU,EAAE;QACV,GAAGA,UAAU;QACbgG,eAAe,EAAE,IAAI;QACrBC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAG9F,KAAK,IAAK,KAAKA,KAAK,MAAM;QACtCuF,QAAQ,EAAEA,CAACQ,IAAI,EAAEhG,QAAQ,KAAK;UAC5BF,aAAa,CAACmB,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPlB,OAAO,EAAEiG,IAAI;YACbhG,QAAQ,EAAEA,QAAQ,IAAI;UACxB,CAAC,CAAC,CAAC;QACL;MACF,CAAE;MACFiG,MAAM,EAAC,IAAI;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK;IAAE;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAGFvF,OAAA,CAACrB,KAAK;MACJ8F,KAAK,EAAE7D,aAAa,GAAG,QAAQ,GAAG,QAAS;MAC3CyG,IAAI,EAAE3G,YAAa;MACnB4G,QAAQ,EAAEA,CAAA,KAAM3G,eAAe,CAAC,KAAK,CAAE;MACvC4G,IAAI,EAAEA,CAAA,KAAM9F,IAAI,CAAC+F,MAAM,CAAC,CAAE;MAC1B5C,KAAK,EAAE,GAAI;MAAAO,QAAA,eAEXnF,OAAA,CAACpB,IAAI;QACH6C,IAAI,EAAEA,IAAK;QACXgG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExE,YAAa;QACvByE,cAAc,EAAGC,aAAa,IAAK;UACjC,IAAIA,aAAa,CAAC/D,QAAQ,IAAI+D,aAAa,CAACnF,UAAU,EAAE;YACtD,MAAMoB,QAAQ,GAAGpC,IAAI,CAACoG,aAAa,CAAC,UAAU,CAAC;YAC/C,MAAMpF,UAAU,GAAGhB,IAAI,CAACoG,aAAa,CAAC,YAAY,CAAC;YACnD,IAAIhE,QAAQ,IAAIpB,UAAU,EAAE;cAC1B,MAAMuC,IAAI,GAAGpB,aAAa,CAACC,QAAQ,EAAEpB,UAAU,CAACa,MAAM,CAAC,qBAAqB,CAAC,CAAC;cAC9E7B,IAAI,CAACe,cAAc,CAAC;gBAAEwC;cAAK,CAAC,CAAC;YAC/B;UACF;QACF,CAAE;QAAAG,QAAA,gBAEFnF,OAAA,CAACpB,IAAI,CAACkJ,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,sCAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoG,QAAA,eAEhDnF,OAAA,CAACV,MAAM;YAACiH,WAAW,EAAC,oEAAa;YAAC4B,UAAU;YAACC,gBAAgB,EAAC,UAAU;YAAAjD,QAAA,EACrE,CAAC7E,gBAAgB,IAAI,EAAE,EAAE+H,GAAG,CAACvE,MAAM,iBAClC9D,OAAA,CAACC,MAAM;cAAiBuG,KAAK,EAAE1C,MAAM,CAACjB,EAAG;cAAAsC,QAAA,GACtCrB,MAAM,CAACzC,QAAQ,EAAC,KAAG,EAACyC,MAAM,CAACxC,QAAQ,EAAC,8BAAQ,EAAC1B,KAAK,CAACkE,MAAM,CAACwE,UAAU,CAAC,CAAChF,MAAM,CAAC,YAAY,CAAC,EAAC,GAC9F;YAAA,GAFaQ,MAAM,CAACjB,EAAE;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZvF,OAAA,CAACpB,IAAI,CAACkJ,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoG,QAAA,eAEhDnF,OAAA,CAAClB,UAAU;YACTyJ,QAAQ;YACRhC,WAAW,EAAC,4CAAS;YACrBtB,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAO,CAAE;YACzBtB,MAAM,EAAC;UAAqB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZvF,OAAA,CAACpB,IAAI,CAACkJ,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,0BAAM;UAAA7C,QAAA,eAEZnF,OAAA,CAACnB,WAAW;YACV0H,WAAW,EAAC,sCAAQ;YACpBiC,GAAG,EAAE,CAAE;YACPC,SAAS,EAAE,CAAE;YACbxD,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAO,CAAE;YACzB8D,WAAW,EAAC,MAAG;YACfC,QAAQ;UAAA;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEX,CAAC;AAACpF,EAAA,CAhZID,gBAA0B;EAAA,QAgBftB,IAAI,CAAC8C,OAAO;AAAA;AAAAkH,EAAA,GAhBvB1I,gBAA0B;AAkZhC,eAAeA,gBAAgB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}