{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { isE, isEmpty, num2str, trimNumber, validateNumber } from \"./numberUtil\";\nvar BigIntDecimal = /*#__PURE__*/function () {\n  /** BigInt will convert `0009` to `9`. We need record the len of decimal */\n\n  function BigIntDecimal(value) {\n    _classCallCheck(this, BigIntDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"negative\", void 0);\n    _defineProperty(this, \"integer\", void 0);\n    _defineProperty(this, \"decimal\", void 0);\n    _defineProperty(this, \"decimalLen\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    _defineProperty(this, \"nan\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n\n    // Act like Number convert\n    if (value === '-' || Number.isNaN(value)) {\n      this.nan = true;\n      return;\n    }\n    var mergedValue = value;\n\n    // We need convert back to Number since it require `toFixed` to handle this\n    if (isE(mergedValue)) {\n      mergedValue = Number(mergedValue);\n    }\n    mergedValue = typeof mergedValue === 'string' ? mergedValue : num2str(mergedValue);\n    if (validateNumber(mergedValue)) {\n      var trimRet = trimNumber(mergedValue);\n      this.negative = trimRet.negative;\n      var numbers = trimRet.trimStr.split('.');\n      this.integer = BigInt(numbers[0]);\n      var decimalStr = numbers[1] || '0';\n      this.decimal = BigInt(decimalStr);\n      this.decimalLen = decimalStr.length;\n    } else {\n      this.nan = true;\n    }\n  }\n  _createClass(BigIntDecimal, [{\n    key: \"getMark\",\n    value: function getMark() {\n      return this.negative ? '-' : '';\n    }\n  }, {\n    key: \"getIntegerStr\",\n    value: function getIntegerStr() {\n      return this.integer.toString();\n    }\n\n    /**\n     * @private get decimal string\n     */\n  }, {\n    key: \"getDecimalStr\",\n    value: function getDecimalStr() {\n      return this.decimal.toString().padStart(this.decimalLen, '0');\n    }\n\n    /**\n     * @private Align BigIntDecimal with same decimal length. e.g. 12.3 + 5 = 1230000\n     * This is used for add function only.\n     */\n  }, {\n    key: \"alignDecimal\",\n    value: function alignDecimal(decimalLength) {\n      var str = \"\".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(decimalLength, '0'));\n      return BigInt(str);\n    }\n  }, {\n    key: \"negate\",\n    value: function negate() {\n      var clone = new BigIntDecimal(this.toString());\n      clone.negative = !clone.negative;\n      return clone;\n    }\n  }, {\n    key: \"cal\",\n    value: function cal(offset, calculator, calDecimalLen) {\n      var maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);\n      var myAlignedDecimal = this.alignDecimal(maxDecimalLength);\n      var offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);\n      var valueStr = calculator(myAlignedDecimal, offsetAlignedDecimal).toString();\n      var nextDecimalLength = calDecimalLen(maxDecimalLength);\n\n      // We need fill string length back to `maxDecimalLength` to avoid parser failed\n      var _trimNumber = trimNumber(valueStr),\n        negativeStr = _trimNumber.negativeStr,\n        trimStr = _trimNumber.trimStr;\n      var hydrateValueStr = \"\".concat(negativeStr).concat(trimStr.padStart(nextDecimalLength + 1, '0'));\n      return new BigIntDecimal(\"\".concat(hydrateValueStr.slice(0, -nextDecimalLength), \".\").concat(hydrateValueStr.slice(-nextDecimalLength)));\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new BigIntDecimal(value);\n      }\n      var offset = new BigIntDecimal(value);\n      if (offset.isInvalidate()) {\n        return this;\n      }\n      return this.cal(offset, function (num1, num2) {\n        return num1 + num2;\n      }, function (len) {\n        return len;\n      });\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = new BigIntDecimal(value);\n      if (this.isInvalidate() || target.isInvalidate()) {\n        return new BigIntDecimal(NaN);\n      }\n      return this.cal(target, function (num1, num2) {\n        return num1 * num2;\n      }, function (len) {\n        return len * 2;\n      });\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return this.nan;\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      if (this.isNaN()) {\n        return NaN;\n      }\n      return Number(this.toString());\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return trimNumber(\"\".concat(this.getMark()).concat(this.getIntegerStr(), \".\").concat(this.getDecimalStr())).fullStr;\n    }\n  }]);\n  return BigIntDecimal;\n}();\nexport { BigIntDecimal as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "isE", "isEmpty", "num2str", "trimNumber", "validateNumber", "BigIntDecimal", "value", "empty", "origin", "String", "Number", "isNaN", "nan", "mergedValue", "trimRet", "negative", "numbers", "trimStr", "split", "integer", "BigInt", "decimalStr", "decimal", "decimalLen", "length", "key", "getMark", "getIntegerStr", "toString", "getDecimalStr", "padStart", "alignDecimal", "decimalLength", "str", "concat", "padEnd", "negate", "clone", "cal", "offset", "calculator", "calDecimalLen", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "myAlignedDecimal", "offsetAlignedDecimal", "valueStr", "nextDecimalLength", "_trimNumber", "negativeStr", "hydrateValueStr", "slice", "add", "isInvalidate", "num1", "num2", "len", "multi", "target", "NaN", "equals", "lessEquals", "toNumber", "safe", "arguments", "undefined", "fullStr", "default"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@rc-component/mini-decimal/es/BigIntDecimal.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { isE, isEmpty, num2str, trimNumber, validateNumber } from \"./numberUtil\";\nvar BigIntDecimal = /*#__PURE__*/function () {\n  /** BigInt will convert `0009` to `9`. We need record the len of decimal */\n\n  function BigIntDecimal(value) {\n    _classCallCheck(this, BigIntDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"negative\", void 0);\n    _defineProperty(this, \"integer\", void 0);\n    _defineProperty(this, \"decimal\", void 0);\n    _defineProperty(this, \"decimalLen\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    _defineProperty(this, \"nan\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n\n    // Act like Number convert\n    if (value === '-' || Number.isNaN(value)) {\n      this.nan = true;\n      return;\n    }\n    var mergedValue = value;\n\n    // We need convert back to Number since it require `toFixed` to handle this\n    if (isE(mergedValue)) {\n      mergedValue = Number(mergedValue);\n    }\n    mergedValue = typeof mergedValue === 'string' ? mergedValue : num2str(mergedValue);\n    if (validateNumber(mergedValue)) {\n      var trimRet = trimNumber(mergedValue);\n      this.negative = trimRet.negative;\n      var numbers = trimRet.trimStr.split('.');\n      this.integer = BigInt(numbers[0]);\n      var decimalStr = numbers[1] || '0';\n      this.decimal = BigInt(decimalStr);\n      this.decimalLen = decimalStr.length;\n    } else {\n      this.nan = true;\n    }\n  }\n  _createClass(BigIntDecimal, [{\n    key: \"getMark\",\n    value: function getMark() {\n      return this.negative ? '-' : '';\n    }\n  }, {\n    key: \"getIntegerStr\",\n    value: function getIntegerStr() {\n      return this.integer.toString();\n    }\n\n    /**\n     * @private get decimal string\n     */\n  }, {\n    key: \"getDecimalStr\",\n    value: function getDecimalStr() {\n      return this.decimal.toString().padStart(this.decimalLen, '0');\n    }\n\n    /**\n     * @private Align BigIntDecimal with same decimal length. e.g. 12.3 + 5 = 1230000\n     * This is used for add function only.\n     */\n  }, {\n    key: \"alignDecimal\",\n    value: function alignDecimal(decimalLength) {\n      var str = \"\".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(decimalLength, '0'));\n      return BigInt(str);\n    }\n  }, {\n    key: \"negate\",\n    value: function negate() {\n      var clone = new BigIntDecimal(this.toString());\n      clone.negative = !clone.negative;\n      return clone;\n    }\n  }, {\n    key: \"cal\",\n    value: function cal(offset, calculator, calDecimalLen) {\n      var maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);\n      var myAlignedDecimal = this.alignDecimal(maxDecimalLength);\n      var offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);\n      var valueStr = calculator(myAlignedDecimal, offsetAlignedDecimal).toString();\n      var nextDecimalLength = calDecimalLen(maxDecimalLength);\n\n      // We need fill string length back to `maxDecimalLength` to avoid parser failed\n      var _trimNumber = trimNumber(valueStr),\n        negativeStr = _trimNumber.negativeStr,\n        trimStr = _trimNumber.trimStr;\n      var hydrateValueStr = \"\".concat(negativeStr).concat(trimStr.padStart(nextDecimalLength + 1, '0'));\n      return new BigIntDecimal(\"\".concat(hydrateValueStr.slice(0, -nextDecimalLength), \".\").concat(hydrateValueStr.slice(-nextDecimalLength)));\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new BigIntDecimal(value);\n      }\n      var offset = new BigIntDecimal(value);\n      if (offset.isInvalidate()) {\n        return this;\n      }\n      return this.cal(offset, function (num1, num2) {\n        return num1 + num2;\n      }, function (len) {\n        return len;\n      });\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = new BigIntDecimal(value);\n      if (this.isInvalidate() || target.isInvalidate()) {\n        return new BigIntDecimal(NaN);\n      }\n      return this.cal(target, function (num1, num2) {\n        return num1 * num2;\n      }, function (len) {\n        return len * 2;\n      });\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return this.nan;\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      if (this.isNaN()) {\n        return NaN;\n      }\n      return Number(this.toString());\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return trimNumber(\"\".concat(this.getMark()).concat(this.getIntegerStr(), \".\").concat(this.getDecimalStr())).fullStr;\n    }\n  }]);\n  return BigIntDecimal;\n}();\nexport { BigIntDecimal as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,QAAQ,cAAc;AAChF,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C;;EAEA,SAASA,aAAaA,CAACC,KAAK,EAAE;IAC5BT,eAAe,CAAC,IAAI,EAAEQ,aAAa,CAAC;IACpCN,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;IACnCA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACzCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACxCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACxCA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3CA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtCA,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACpC,IAAIE,OAAO,CAACK,KAAK,CAAC,EAAE;MAClB,IAAI,CAACC,KAAK,GAAG,IAAI;MACjB;IACF;IACA,IAAI,CAACC,MAAM,GAAGC,MAAM,CAACH,KAAK,CAAC;;IAE3B;IACA,IAAIA,KAAK,KAAK,GAAG,IAAII,MAAM,CAACC,KAAK,CAACL,KAAK,CAAC,EAAE;MACxC,IAAI,CAACM,GAAG,GAAG,IAAI;MACf;IACF;IACA,IAAIC,WAAW,GAAGP,KAAK;;IAEvB;IACA,IAAIN,GAAG,CAACa,WAAW,CAAC,EAAE;MACpBA,WAAW,GAAGH,MAAM,CAACG,WAAW,CAAC;IACnC;IACAA,WAAW,GAAG,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGX,OAAO,CAACW,WAAW,CAAC;IAClF,IAAIT,cAAc,CAACS,WAAW,CAAC,EAAE;MAC/B,IAAIC,OAAO,GAAGX,UAAU,CAACU,WAAW,CAAC;MACrC,IAAI,CAACE,QAAQ,GAAGD,OAAO,CAACC,QAAQ;MAChC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;MACxC,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC,IAAIK,UAAU,GAAGL,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;MAClC,IAAI,CAACM,OAAO,GAAGF,MAAM,CAACC,UAAU,CAAC;MACjC,IAAI,CAACE,UAAU,GAAGF,UAAU,CAACG,MAAM;IACrC,CAAC,MAAM;MACL,IAAI,CAACZ,GAAG,GAAG,IAAI;IACjB;EACF;EACAd,YAAY,CAACO,aAAa,EAAE,CAAC;IAC3BoB,GAAG,EAAE,SAAS;IACdnB,KAAK,EAAE,SAASoB,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACX,QAAQ,GAAG,GAAG,GAAG,EAAE;IACjC;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,eAAe;IACpBnB,KAAK,EAAE,SAASqB,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACR,OAAO,CAACS,QAAQ,CAAC,CAAC;IAChC;;IAEA;AACJ;AACA;EACE,CAAC,EAAE;IACDH,GAAG,EAAE,eAAe;IACpBnB,KAAK,EAAE,SAASuB,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACP,OAAO,CAACM,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC,IAAI,CAACP,UAAU,EAAE,GAAG,CAAC;IAC/D;;IAEA;AACJ;AACA;AACA;EACE,CAAC,EAAE;IACDE,GAAG,EAAE,cAAc;IACnBnB,KAAK,EAAE,SAASyB,YAAYA,CAACC,aAAa,EAAE;MAC1C,IAAIC,GAAG,GAAG,EAAE,CAACC,MAAM,CAAC,IAAI,CAACR,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM,CAAC,IAAI,CAACP,aAAa,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,IAAI,CAACL,aAAa,CAAC,CAAC,CAACM,MAAM,CAACH,aAAa,EAAE,GAAG,CAAC,CAAC;MACxH,OAAOZ,MAAM,CAACa,GAAG,CAAC;IACpB;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,QAAQ;IACbnB,KAAK,EAAE,SAAS8B,MAAMA,CAAA,EAAG;MACvB,IAAIC,KAAK,GAAG,IAAIhC,aAAa,CAAC,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAAC;MAC9CS,KAAK,CAACtB,QAAQ,GAAG,CAACsB,KAAK,CAACtB,QAAQ;MAChC,OAAOsB,KAAK;IACd;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,KAAK;IACVnB,KAAK,EAAE,SAASgC,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAE;MACrD,IAAIC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACf,aAAa,CAAC,CAAC,CAACL,MAAM,EAAEe,MAAM,CAACV,aAAa,CAAC,CAAC,CAACL,MAAM,CAAC;MAC3F,IAAIqB,gBAAgB,GAAG,IAAI,CAACd,YAAY,CAACW,gBAAgB,CAAC;MAC1D,IAAII,oBAAoB,GAAGP,MAAM,CAACR,YAAY,CAACW,gBAAgB,CAAC;MAChE,IAAIK,QAAQ,GAAGP,UAAU,CAACK,gBAAgB,EAAEC,oBAAoB,CAAC,CAAClB,QAAQ,CAAC,CAAC;MAC5E,IAAIoB,iBAAiB,GAAGP,aAAa,CAACC,gBAAgB,CAAC;;MAEvD;MACA,IAAIO,WAAW,GAAG9C,UAAU,CAAC4C,QAAQ,CAAC;QACpCG,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCjC,OAAO,GAAGgC,WAAW,CAAChC,OAAO;MAC/B,IAAIkC,eAAe,GAAG,EAAE,CAACjB,MAAM,CAACgB,WAAW,CAAC,CAAChB,MAAM,CAACjB,OAAO,CAACa,QAAQ,CAACkB,iBAAiB,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;MACjG,OAAO,IAAI3C,aAAa,CAAC,EAAE,CAAC6B,MAAM,CAACiB,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,CAACJ,iBAAiB,CAAC,EAAE,GAAG,CAAC,CAACd,MAAM,CAACiB,eAAe,CAACC,KAAK,CAAC,CAACJ,iBAAiB,CAAC,CAAC,CAAC;IAC1I;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,KAAK;IACVnB,KAAK,EAAE,SAAS+C,GAAGA,CAAC/C,KAAK,EAAE;MACzB,IAAI,IAAI,CAACgD,YAAY,CAAC,CAAC,EAAE;QACvB,OAAO,IAAIjD,aAAa,CAACC,KAAK,CAAC;MACjC;MACA,IAAIiC,MAAM,GAAG,IAAIlC,aAAa,CAACC,KAAK,CAAC;MACrC,IAAIiC,MAAM,CAACe,YAAY,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI;MACb;MACA,OAAO,IAAI,CAAChB,GAAG,CAACC,MAAM,EAAE,UAAUgB,IAAI,EAAEC,IAAI,EAAE;QAC5C,OAAOD,IAAI,GAAGC,IAAI;MACpB,CAAC,EAAE,UAAUC,GAAG,EAAE;QAChB,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,OAAO;IACZnB,KAAK,EAAE,SAASoD,KAAKA,CAACpD,KAAK,EAAE;MAC3B,IAAIqD,MAAM,GAAG,IAAItD,aAAa,CAACC,KAAK,CAAC;MACrC,IAAI,IAAI,CAACgD,YAAY,CAAC,CAAC,IAAIK,MAAM,CAACL,YAAY,CAAC,CAAC,EAAE;QAChD,OAAO,IAAIjD,aAAa,CAACuD,GAAG,CAAC;MAC/B;MACA,OAAO,IAAI,CAACtB,GAAG,CAACqB,MAAM,EAAE,UAAUJ,IAAI,EAAEC,IAAI,EAAE;QAC5C,OAAOD,IAAI,GAAGC,IAAI;MACpB,CAAC,EAAE,UAAUC,GAAG,EAAE;QAChB,OAAOA,GAAG,GAAG,CAAC;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,SAAS;IACdnB,KAAK,EAAE,SAASL,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACM,KAAK;IACnB;EACF,CAAC,EAAE;IACDkB,GAAG,EAAE,OAAO;IACZnB,KAAK,EAAE,SAASK,KAAKA,CAAA,EAAG;MACtB,OAAO,IAAI,CAACC,GAAG;IACjB;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,cAAc;IACnBnB,KAAK,EAAE,SAASgD,YAAYA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAACrD,OAAO,CAAC,CAAC,IAAI,IAAI,CAACU,KAAK,CAAC,CAAC;IACvC;EACF,CAAC,EAAE;IACDc,GAAG,EAAE,QAAQ;IACbnB,KAAK,EAAE,SAASuD,MAAMA,CAACF,MAAM,EAAE;MAC7B,OAAO,IAAI,CAAC/B,QAAQ,CAAC,CAAC,MAAM+B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC/B,QAAQ,CAAC,CAAC,CAAC;IAChG;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,YAAY;IACjBnB,KAAK,EAAE,SAASwD,UAAUA,CAACH,MAAM,EAAE;MACjC,OAAO,IAAI,CAACN,GAAG,CAACM,MAAM,CAACvB,MAAM,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,CAAC,CAACmC,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC7D;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,UAAU;IACfnB,KAAK,EAAE,SAASyD,QAAQA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACpD,KAAK,CAAC,CAAC,EAAE;QAChB,OAAOiD,GAAG;MACZ;MACA,OAAOlD,MAAM,CAAC,IAAI,CAACkB,QAAQ,CAAC,CAAC,CAAC;IAChC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,UAAU;IACfnB,KAAK,EAAE,SAASsB,QAAQA,CAAA,EAAG;MACzB,IAAIoC,IAAI,GAAGC,SAAS,CAACzC,MAAM,GAAG,CAAC,IAAIyC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MACnF,IAAI,CAACD,IAAI,EAAE;QACT,OAAO,IAAI,CAACxD,MAAM;MACpB;MACA,IAAI,IAAI,CAAC8C,YAAY,CAAC,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MACA,OAAOnD,UAAU,CAAC,EAAE,CAAC+B,MAAM,CAAC,IAAI,CAACR,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM,CAAC,IAAI,CAACP,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,CAACO,MAAM,CAAC,IAAI,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC,CAACsC,OAAO;IACrH;EACF,CAAC,CAAC,CAAC;EACH,OAAO9D,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,SAASA,aAAa,IAAI+D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}