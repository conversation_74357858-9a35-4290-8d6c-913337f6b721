{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/spec/component.ts"], "sourcesContent": ["import { Primitive } from '../runtime';\nimport { UsePrefix } from './utils';\n\nexport type Component =\n  | TitleComponent\n  | AxisComponent\n  | LegendComponent\n  | ScrollbarComponent\n  | SliderComponent\n  | TooltipComponent;\n\nexport type TitleComponent = {\n  /**\n   * Height of title, default is 36.\n   */\n  size?: number;\n  /**\n   * Text of title.\n   */\n  title?: string;\n  /**\n   * Text of subtitle.\n   */\n  subtitle?: string | null;\n  /**\n   * Align method for title.\n   */\n  align?: 'left' | 'center' | 'right';\n  /**\n   * The vertical spacing between title and subtitle, default is 2.\n   */\n  spacing?: number;\n} & UsePrefix<'title' | 'subtitle', Record<string, any>>;\n\nexport type AxisComponent = {\n  type?: 'axisX' | 'axisY' | 'axisZ';\n  tickCount?: number;\n  labelFormatter?: any;\n  tickFilter?: any;\n  title?: any;\n  state?: Partial<\n    Record<\n      'active' | 'selected' | 'inactive' | 'unselected',\n      Record<string, any>\n    >\n  >;\n  scale?: Record<string, any>; //@todo\n  [key: string]: any; // @todo\n};\n\nexport type LegendComponent = {\n  type?: 'legends';\n  tickCount?: number;\n  labelFormatter?: any;\n  tickFilter?: any;\n  title?: any;\n  position?: string;\n  state?: Partial<\n    Record<\n      'active' | 'selected' | 'inactive' | 'unselected',\n      Record<string, any>\n    >\n  >;\n  scale?: Record<string, any>; //@todo\n  [key: string]: any; // @todo\n};\n\nexport type SliderComponent = any; // @todo\n\nexport type ScrollbarComponent = any; // @todo\n\nexport type TooltipComponent =\n  | TooltipItem\n  | TooltipItem[]\n  | {\n      title?: Encodeable<TooltipTitle>;\n      items?: TooltipItem[] | null | false;\n      [key: string]: any;\n    }\n  | null\n  | false;\n\nexport type TooltipTitle =\n  | string\n  | { field?: string; channel?: string; value?: string };\n\nexport type TooltipItem =\n  | string\n  | {\n      name?: string;\n      color?: string;\n      channel?: string;\n      field?: string;\n      value?: string;\n      valueFormatter?: string | ((d: any) => string);\n    }\n  | Encodeable<Primitive>\n  | Encodeable<TooltipItemValue>;\n\nexport type TooltipItemValue = {\n  name?: string;\n  color?: string;\n  value?: Primitive;\n};\n\nexport type Encodeable<T> =\n  | T\n  | ((d: any, index?: number, data?: any[], column?: any) => T);\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}