{"ast": null, "code": "const genStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper-rtl\")]: {\n      direction: 'rtl',\n      table: {\n        direction: 'rtl'\n      },\n      [\"\".concat(componentCls, \"-pagination-left\")]: {\n        justifyContent: 'flex-end'\n      },\n      [\"\".concat(componentCls, \"-pagination-right\")]: {\n        justifyContent: 'flex-start'\n      },\n      [\"\".concat(componentCls, \"-row-expand-icon\")]: {\n        float: 'right',\n        '&::after': {\n          transform: 'rotate(-90deg)'\n        },\n        '&-collapsed::before': {\n          transform: 'rotate(180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        }\n      },\n      [\"\".concat(componentCls, \"-container\")]: {\n        '&::before': {\n          insetInlineStart: 'unset',\n          insetInlineEnd: 0\n        },\n        '&::after': {\n          insetInlineStart: 0,\n          insetInlineEnd: 'unset'\n        },\n        [\"\".concat(componentCls, \"-row-indent\")]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStyle;", "map": {"version": 3, "names": ["genStyle", "token", "componentCls", "concat", "direction", "table", "justifyContent", "float", "transform", "insetInlineStart", "insetInlineEnd"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/style/rtl.js"], "sourcesContent": ["const genStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper-rtl`]: {\n      direction: 'rtl',\n      table: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-pagination-left`]: {\n        justifyContent: 'flex-end'\n      },\n      [`${componentCls}-pagination-right`]: {\n        justifyContent: 'flex-start'\n      },\n      [`${componentCls}-row-expand-icon`]: {\n        float: 'right',\n        '&::after': {\n          transform: 'rotate(-90deg)'\n        },\n        '&-collapsed::before': {\n          transform: 'rotate(180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        }\n      },\n      [`${componentCls}-container`]: {\n        '&::before': {\n          insetInlineStart: 'unset',\n          insetInlineEnd: 0\n        },\n        '&::after': {\n          insetInlineStart: 0,\n          insetInlineEnd: 'unset'\n        },\n        [`${componentCls}-row-indent`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStyle;"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,IAAAE,MAAA,CAAID,YAAY,oBAAiB;MAC/BE,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;QACLD,SAAS,EAAE;MACb,CAAC;MACD,IAAAD,MAAA,CAAID,YAAY,wBAAqB;QACnCI,cAAc,EAAE;MAClB,CAAC;MACD,IAAAH,MAAA,CAAID,YAAY,yBAAsB;QACpCI,cAAc,EAAE;MAClB,CAAC;MACD,IAAAH,MAAA,CAAID,YAAY,wBAAqB;QACnCK,KAAK,EAAE,OAAO;QACd,UAAU,EAAE;UACVC,SAAS,EAAE;QACb,CAAC;QACD,qBAAqB,EAAE;UACrBA,SAAS,EAAE;QACb,CAAC;QACD,oBAAoB,EAAE;UACpBA,SAAS,EAAE;QACb;MACF,CAAC;MACD,IAAAL,MAAA,CAAID,YAAY,kBAAe;QAC7B,WAAW,EAAE;UACXO,gBAAgB,EAAE,OAAO;UACzBC,cAAc,EAAE;QAClB,CAAC;QACD,UAAU,EAAE;UACVD,gBAAgB,EAAE,CAAC;UACnBC,cAAc,EAAE;QAClB,CAAC;QACD,IAAAP,MAAA,CAAID,YAAY,mBAAgB;UAC9BK,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}