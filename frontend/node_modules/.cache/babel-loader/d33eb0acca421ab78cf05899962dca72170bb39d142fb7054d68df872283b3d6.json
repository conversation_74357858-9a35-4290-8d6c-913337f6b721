{"ast": null, "code": "import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom,\n    left: isScrollAtLeft,\n    right: isScrollAtRight\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  scrollPingRef.current.left = isScrollAtLeft;\n  scrollPingRef.current.right = isScrollAtRight;\n  return function (isHorizontal, delta) {\n    var smoothOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var originScroll = isHorizontal ?\n    // Pass origin wheel when on the left\n    delta < 0 && scrollPingRef.current.left ||\n    // Pass origin wheel when on the right\n    delta > 0 && scrollPingRef.current.right // Pass origin wheel when on the top\n    : delta < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    delta > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});", "map": {"version": 3, "names": ["useRef", "isScrollAtTop", "isScrollAtBottom", "isScrollAtLeft", "isScrollAtRight", "lockRef", "lockTimeoutRef", "lockScroll", "clearTimeout", "current", "setTimeout", "scrollPingRef", "top", "bottom", "left", "right", "isHorizontal", "delta", "smoothOffset", "arguments", "length", "undefined", "originScroll"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-virtual-list/es/hooks/useOriginScroll.js"], "sourcesContent": ["import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom,\n    left: isScrollAtLeft,\n    right: isScrollAtRight\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  scrollPingRef.current.left = isScrollAtLeft;\n  scrollPingRef.current.right = isScrollAtRight;\n  return function (isHorizontal, delta) {\n    var smoothOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var originScroll = isHorizontal ?\n    // Pass origin wheel when on the left\n    delta < 0 && scrollPingRef.current.left ||\n    // Pass origin wheel when on the right\n    delta > 0 && scrollPingRef.current.right // Pass origin wheel when on the top\n    : delta < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    delta > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,gBAAgB,UAAUC,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,EAAE;EAC1F;EACA,IAAIC,OAAO,GAAGL,MAAM,CAAC,KAAK,CAAC;EAC3B,IAAIM,cAAc,GAAGN,MAAM,CAAC,IAAI,CAAC;EACjC,SAASO,UAAUA,CAAA,EAAG;IACpBC,YAAY,CAACF,cAAc,CAACG,OAAO,CAAC;IACpCJ,OAAO,CAACI,OAAO,GAAG,IAAI;IACtBH,cAAc,CAACG,OAAO,GAAGC,UAAU,CAAC,YAAY;MAC9CL,OAAO,CAACI,OAAO,GAAG,KAAK;IACzB,CAAC,EAAE,EAAE,CAAC;EACR;;EAEA;EACA,IAAIE,aAAa,GAAGX,MAAM,CAAC;IACzBY,GAAG,EAAEX,aAAa;IAClBY,MAAM,EAAEX,gBAAgB;IACxBY,IAAI,EAAEX,cAAc;IACpBY,KAAK,EAAEX;EACT,CAAC,CAAC;EACFO,aAAa,CAACF,OAAO,CAACG,GAAG,GAAGX,aAAa;EACzCU,aAAa,CAACF,OAAO,CAACI,MAAM,GAAGX,gBAAgB;EAC/CS,aAAa,CAACF,OAAO,CAACK,IAAI,GAAGX,cAAc;EAC3CQ,aAAa,CAACF,OAAO,CAACM,KAAK,GAAGX,eAAe;EAC7C,OAAO,UAAUY,YAAY,EAAEC,KAAK,EAAE;IACpC,IAAIC,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5F,IAAIG,YAAY,GAAGN,YAAY;IAC/B;IACAC,KAAK,GAAG,CAAC,IAAIN,aAAa,CAACF,OAAO,CAACK,IAAI;IACvC;IACAG,KAAK,GAAG,CAAC,IAAIN,aAAa,CAACF,OAAO,CAACM,KAAK,CAAC;IAAA,EACvCE,KAAK,GAAG,CAAC,IAAIN,aAAa,CAACF,OAAO,CAACG,GAAG;IACxC;IACAK,KAAK,GAAG,CAAC,IAAIN,aAAa,CAACF,OAAO,CAACI,MAAM;IACzC,IAAIK,YAAY,IAAII,YAAY,EAAE;MAChC;MACAd,YAAY,CAACF,cAAc,CAACG,OAAO,CAAC;MACpCJ,OAAO,CAACI,OAAO,GAAG,KAAK;IACzB,CAAC,MAAM,IAAI,CAACa,YAAY,IAAIjB,OAAO,CAACI,OAAO,EAAE;MAC3CF,UAAU,CAAC,CAAC;IACd;IACA,OAAO,CAACF,OAAO,CAACI,OAAO,IAAIa,YAAY;EACzC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}