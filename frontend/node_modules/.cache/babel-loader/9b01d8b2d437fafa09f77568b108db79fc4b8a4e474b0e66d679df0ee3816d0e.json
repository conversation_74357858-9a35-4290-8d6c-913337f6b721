{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g2/src/spec/mark.ts"], "sourcesContent": ["import { MarkComponent } from '../runtime';\nimport { Encode } from './encode';\nimport { Transform } from './transform';\nimport { Scale } from './scale';\nimport { Coordinate } from './coordinate';\nimport { Animation } from './animate';\nimport { Interaction } from './interaction';\nimport { Theme } from './theme';\nimport { Data } from './data';\nimport {\n  AxisComponent,\n  LegendComponent,\n  ScrollbarComponent,\n  SliderComponent,\n  TitleComponent,\n  TooltipComponent,\n} from './component';\nimport { Closeable, Literal2Object, Padding } from './utils';\n\nexport type Mark =\n  | IntervalMark\n  | RectMark\n  | LineMark\n  | PointMark\n  | TextMark\n  | CellMark\n  | AreaMark\n  | NodeMark\n  | EdgeMark\n  | ImageMark\n  | PolygonMark\n  | BoxMark\n  | VectorMark\n  | LineXMark\n  | LineYMark\n  | RangeMark\n  | RangeXMark\n  | RangeYMark\n  | ConnectorMark\n  | SankeyMark\n  | ChordMark\n  | PathMark\n  | TreemapMark\n  | PackMark\n  | BoxPlotMark\n  | ShapeMark\n  | ForceGraphMark\n  | TreeMark\n  | WordCloudMark\n  | DensityMark\n  | CustomMark\n  | CompositeMark;\n\nexport type MarkTypes =\n  | 'auto'\n  | 'interval'\n  | 'rect'\n  | 'line'\n  | 'point'\n  | 'text'\n  | 'cell'\n  | 'area'\n  | 'node'\n  | 'edge'\n  | 'link'\n  | 'image'\n  | 'polygon'\n  | 'box'\n  | 'vector'\n  | 'lineX'\n  | 'lineY'\n  | 'connector'\n  | 'range'\n  | 'rangeX'\n  | 'rangeY'\n  | 'sankey'\n  | 'chord'\n  | 'path'\n  | 'treemap'\n  | 'pack'\n  | 'boxplot'\n  | 'shape'\n  | 'forceGraph'\n  | 'tree'\n  | 'wordCloud'\n  | 'gauge'\n  | 'density'\n  | 'heatmap'\n  | 'liquid'\n  | MarkComponent\n  | CompositeMarkType;\n\nexport type ChannelTypes =\n  | 'x'\n  | 'y'\n  | 'z'\n  | 'x1'\n  | 'y1'\n  | 'series'\n  | 'color'\n  | 'opacity'\n  | 'shape'\n  | 'size'\n  | 'key'\n  | 'groupKey'\n  | 'position'\n  | 'series'\n  | 'enterType'\n  | 'enterEasing'\n  | 'enterDuration'\n  | 'enterDelay'\n  | 'updateType'\n  | 'updateEasing'\n  | 'updateDuration'\n  | 'updateDelay'\n  | 'exitType'\n  | 'exitEasing'\n  | 'exitDuration'\n  | 'exitDelay'\n  | `position${number}`;\n\nexport type PositionChannelTypes =\n  | 'x'\n  | 'y'\n  | 'z'\n  | 'position'\n  | `position${number}`;\n\nexport type AtheisticChanelTypes = 'size' | 'color' | 'shape' | 'opacity';\n\nexport type BaseMark<T extends MarkTypes, C extends string = ChannelTypes> = {\n  type?: T | string;\n  class?: string;\n  key?: string;\n  x?: number;\n  y?: number;\n  width?: number;\n  height?: number;\n  paddingLeft?: Padding;\n  paddingRight?: Padding;\n  paddingBottom?: Padding;\n  paddingTop?: Padding;\n  padding?: Padding;\n  inset?: number;\n  insetLeft?: number;\n  insetBottom?: number;\n  insetTop?: number;\n  insetRight?: number;\n  margin?: number;\n  marginLeft?: number;\n  marginBottom?: number;\n  marginTop?: number;\n  marginRight?: number;\n  facet?: boolean;\n  frame?: boolean;\n  zIndex?: number;\n  cartesian?: boolean;\n  clip?: boolean;\n  data?: Data;\n  transform?: Transform[];\n  layout?: Record<string, any>;\n  encode?: Partial<Record<C, Encode | Encode[]>>;\n  scale?: Partial<Record<C, Scale>>;\n  coordinate?: Coordinate;\n  style?: Record<string, any>;\n  viewStyle?: Record<string, any>;\n  state?: Partial<\n    Record<\n      'active' | 'selected' | 'inactive' | 'unselected',\n      Record<string, any>\n    >\n  >;\n  animate?: Closeable<\n    Partial<Record<'enter' | 'update' | 'exit', Closeable<Animation>>>\n  >;\n  labels?: Record<string, any>[];\n  tooltip?: TooltipComponent;\n  axis?: Closeable<\n    Partial<Record<PositionChannelTypes, Closeable<AxisComponent>>>\n  >;\n  legend?: Closeable<\n    Partial<Record<AtheisticChanelTypes, Closeable<LegendComponent>>>\n  >;\n  slider?: Closeable<\n    Partial<Record<PositionChannelTypes, Closeable<SliderComponent>>>\n  >;\n  scrollbar?: Closeable<\n    Partial<Record<PositionChannelTypes, Closeable<ScrollbarComponent>>>\n  >;\n  title?: string | TitleComponent;\n  interaction?: Literal2Object<Interaction> & Record<string, any>;\n  theme?: Theme;\n};\n\nexport type CompositeMarkType = (\n  options: Record<string, any>,\n  context: Record<string, any>,\n) => any[];\n\nexport type CompositeMark = BaseMark<CompositeMarkType>;\n\nexport type IntervalMark = BaseMark<'interval', ChannelTypes | 'series'>;\n\nexport type RectMark = BaseMark<'rect', ChannelTypes>;\n\nexport type LineMark = BaseMark<\n  'line',\n  ChannelTypes | 'position' | `position${number}`\n>;\n\nexport type PointMark = BaseMark<'point'>;\n\nexport type TextMark = BaseMark<\n  'text',\n  ChannelTypes | 'text' | 'fontSize' | 'fontWeight' | 'rotate'\n>;\n\nexport type LineXMark = BaseMark<'lineX', ChannelTypes>;\n\nexport type LineYMark = BaseMark<'lineY', ChannelTypes>;\n\nexport type RangeMark = BaseMark<'range', ChannelTypes>;\n\nexport type RangeXMark = BaseMark<'rangeX', ChannelTypes>;\n\nexport type RangeYMark = BaseMark<'rangeY', ChannelTypes>;\n\nexport type ConnectorMark = BaseMark<'connector', ChannelTypes>;\n\nexport type CellMark = BaseMark<'cell', ChannelTypes>;\n\nexport type AreaMark = BaseMark<'area', ChannelTypes>;\n\nexport type NodeMark = BaseMark<'node', ChannelTypes>;\n\nexport type EdgeMark = BaseMark<'edge', ChannelTypes>;\n\nexport type LinkMark = BaseMark<'link', ChannelTypes>;\n\nexport type ImageMark = BaseMark<'image', ChannelTypes | 'src'>;\n\nexport type PolygonMark = BaseMark<'polygon', ChannelTypes>;\n\nexport type BoxMark = BaseMark<'box', ChannelTypes>;\n\nexport type BoxPlotMark = BaseMark<'box', ChannelTypes>;\n\nexport type ShapeMark = BaseMark<'shape', ChannelTypes>;\n\nexport type VectorMark = BaseMark<'vector', ChannelTypes | 'rotate' | 'size'>;\n\nexport type SankeyMark = BaseMark<\n  'sankey',\n  | 'source'\n  | 'target'\n  | 'value'\n  | `node${Capitalize<ChannelTypes>}`\n  | `link${Capitalize<ChannelTypes>}`\n  | ChannelTypes\n> & {\n  layout?: {\n    nodeId?: (node: any) => string;\n    nodes?: (graph: any) => any;\n    links?: (graph: any) => any;\n    /**\n     * sankey.nodeSort(undefined) is the default and resorts by ascending breadth during each iteration.\n     * sankey.nodeSort(null) specifies the input order of nodes and never sorts.\n     * sankey.nodeSort(function) specifies the given order as a comparator function and sorts once on initialization.\n     */\n    nodeSort?: null | undefined | ((a: any, b: any) => number);\n    /**\n     * sankey.linkSort(undefined) is the default, indicating that vertical order of links within each node will be determined automatically by the layout. If\n     * sankey.linkSort(null) will resort by the input.\n     * sankey.linkSort(function) specifies the given order as a comparator function and sorts once on initialization.\n     */\n    linkSort?: null | undefined | ((a: any, b: any) => number);\n    nodeAlign?:\n      | 'left'\n      | 'center'\n      | 'right'\n      | 'justify'\n      | ((node: any, n: number) => number);\n    nodeWidth?: number;\n    nodePadding?: number;\n    iterations?: number;\n    // support config the depth of node\n    nodeDepth?: (datum: any, maxDepth: number) => number;\n  };\n  nodeLabels: Record<string, any>[];\n  linkLabels: Record<string, any>[];\n};\n\nexport type ChordMark = BaseMark<\n  'chord',\n  | 'source'\n  | 'target'\n  | 'value'\n  | `node${Capitalize<ChannelTypes>}`\n  | `link${Capitalize<ChannelTypes>}`\n  | ChannelTypes\n> & {\n  layout?: {\n    nodes?: (graph: any) => any;\n    links?: (graph: any) => any;\n    y?: number;\n    id?: (node: any) => any;\n    sortBy?:\n      | 'id'\n      | 'weight'\n      | 'frequency'\n      | null\n      | ((a: any, b: any) => number);\n    nodeWidthRatio?: number;\n    nodePaddingRatio?: number;\n    sourceWeight?(edge: any): number;\n    targetWeight?(edge: any): number;\n  };\n  nodeLabels: Record<string, any>[];\n  linkLabels: Record<string, any>[];\n};\n\nexport type PathMark = BaseMark<'path', ChannelTypes | 'd'>;\n\nexport type TreemapMark = BaseMark<'treemap', 'value' | ChannelTypes> & {\n  layout?: Record<string, any>;\n};\n\nexport type PackMark = BaseMark<'pack', 'value' | ChannelTypes> & {\n  layout?: Record<string, any>;\n};\n\nexport type ForceGraphMark = BaseMark<\n  'forceGraph',\n  | 'source'\n  | 'target'\n  | 'color'\n  | 'value'\n  | `node${Capitalize<ChannelTypes>}`\n  | `link${Capitalize<ChannelTypes>}`\n> & {\n  layout?: Record<string, any>;\n  nodeLabels: Record<string, any>[];\n  linkLabels: Record<string, any>[];\n};\n\nexport type TreeMark = BaseMark<'tree', 'value' | ChannelTypes> & {\n  layout?: {\n    /**\n     * Layout field. Default: 'value'.\n     */\n    field?: string;\n    /**\n     * Sets this cluster layout’s node size to the specified two-element array of numbers [width, height] and returns this cluster layout.\n     * Default: null.\n     */\n    nodeSize?: any;\n    /**\n     * The separation accessor is used to separate neighboring leaves.  Default: (a, b) => a.parent == b.parent ? 1 : 2;\n     */\n    separation?: (a, b) => number;\n    /**\n     * Sort function by compare 2 nodes.\n     */\n    sortBy?: (a, b) => number;\n    /**\n     * Layout infomation saved into fields. Default: ['x', 'y'].\n     */\n    as?: [string, string];\n  };\n  nodeLabels: Record<string, any>[];\n  linkLabels: Record<string, any>[];\n};\n\nexport type WordCloudMark = BaseMark<\n  'wordCloud',\n  'value' | ChannelTypes | 'text'\n> & {\n  layout?: {\n    /**\n     * @description If specified, sets the rectangular [width, height] of the layout\n     * @default [1, 1]\n     */\n    size?: [number, number];\n    font?: string | ((word: any) => string);\n    fontStyle?: string | ((word: any) => string);\n    fontWeight?: any | ((word: any) => any);\n    fontSize?: number | [number, number] | ((word: any) => number);\n    padding?: number | ((word: any) => number);\n    /**\n     * @description sets the text accessor function, which indicates the text for each word\n     * @default (d) => d.text\n     */\n    text?: (word: any) => number;\n    rotate?: number | ((word: any) => number);\n    timeInterval?: number;\n    random?: number | (() => number);\n    /**\n     * @description sets the current type of spiral used for positioning words. This can either be one of the two built-in spirals, \"archimedean\" and \"rectangular\"\n     * @default \"archimedean\"\n     */\n    spiral?:\n      | 'archimedean'\n      | 'rectangular'\n      | ((size: [number, number]) => (t: number) => number[]);\n    imageMask?: HTMLImageElement | string;\n    on?:\n      | ((type: 'end', details?: { cloud; words; bounds }) => void)\n      | ((type: 'word', details?: { cloud; word }) => void);\n  };\n};\n\nexport type GaugeMark = BaseMark<\n  'gauge',\n  | `arc${Capitalize<ChannelTypes>}`\n  | `indicator${Capitalize<ChannelTypes>}`\n  | `pointer${Capitalize<ChannelTypes>}`\n  | `pin${Capitalize<ChannelTypes>}`\n  | ChannelTypes\n>;\n\nexport type DensityMark = BaseMark<'density', ChannelTypes | 'series'>;\nexport type HeatmapMark = BaseMark<'heatmap'>;\nexport type LiquidMark = BaseMark<'liquid'>;\n\nexport type CustomMark = BaseMark<MarkComponent, ChannelTypes>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}