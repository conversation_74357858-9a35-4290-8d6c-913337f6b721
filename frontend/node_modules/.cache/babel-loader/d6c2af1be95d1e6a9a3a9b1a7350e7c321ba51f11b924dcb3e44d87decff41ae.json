{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorAlphaInputPrefixCls = \"\".concat(prefixCls, \"-alpha-input\");\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => \"\".concat(step, \"%\"),\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "getColorAlpha", "ColorSteppers", "ColorAlphaInput", "_ref", "prefixCls", "value", "onChange", "colorAlphaInputPrefixCls", "concat", "internalValue", "setInternalValue", "alphaValue", "handleAlphaChange", "step", "hsba", "toHsb", "a", "genColor", "createElement", "formatter", "className"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/color-picker/components/ColorAlphaInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,aAAa,QAAQ,SAAS;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,eAAe,GAAGC,IAAA,IAIlB;EAAA,IAJmB;IACvBC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,wBAAwB,MAAAC,MAAA,CAAMJ,SAAS,iBAAc;EAC3D,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,MAAMC,aAAa,CAACM,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMM,UAAU,GAAGN,KAAK,IAAII,aAAa;EACzC,MAAMG,iBAAiB,GAAGC,IAAI,IAAI;IAChC,MAAMC,IAAI,GAAGH,UAAU,CAACI,KAAK,CAAC,CAAC;IAC/BD,IAAI,CAACE,CAAC,GAAG,CAACH,IAAI,IAAI,CAAC,IAAI,GAAG;IAC1B,MAAMI,QAAQ,GAAGlB,aAAa,CAACe,IAAI,CAAC;IACpCJ,gBAAgB,CAACO,QAAQ,CAAC;IAC1BX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAapB,KAAK,CAACqB,aAAa,CAACjB,aAAa,EAAE;IACrDI,KAAK,EAAEL,aAAa,CAACW,UAAU,CAAC;IAChCP,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEN,IAAI,OAAAL,MAAA,CAAOK,IAAI,MAAG;IAC7BO,SAAS,EAAEb,wBAAwB;IACnCD,QAAQ,EAAEM;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}