{"ast": null, "code": "/**\n * The base implementation of `_.property` without support for deep paths.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction baseProperty(key) {\n  return function (object) {\n    return object == null ? undefined : object[key];\n  };\n}\nmodule.exports = baseProperty;", "map": {"version": 3, "names": ["baseProperty", "key", "object", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/lodash/_baseProperty.js"], "sourcesContent": ["/**\n * The base implementation of `_.property` without support for deep paths.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction baseProperty(key) {\n  return function(object) {\n    return object == null ? undefined : object[key];\n  };\n}\n\nmodule.exports = baseProperty;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAO,UAASC,MAAM,EAAE;IACtB,OAAOA,MAAM,IAAI,IAAI,GAAGC,SAAS,GAAGD,MAAM,CAACD,GAAG,CAAC;EACjD,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}