{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Row, Col, Card, Statistic, Table, List, Avatar, Typography, Space, Tag, Progress, Timeline, Button, Alert } from 'antd';\nimport { UserOutlined, BookOutlined, ReadOutlined, PayCircleOutlined, SafetyOutlined, TrophyOutlined, ClockCircleOutlined, RiseOutlined, CalendarOutlined, BellOutlined, HeartOutlined, StarOutlined, BarChartOutlined, PieChartOutlined, LineChartOutlined } from '@ant-design/icons';\nimport { Column, Pie, Line } from '@ant-design/charts';\nimport { useAuth } from '../contexts/AuthContext';\nimport { usePermission } from '../components/Auth/PermissionWrapper';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    isAdmin\n  } = usePermission();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalBooks: 0,\n    totalBorrows: 0,\n    totalFines: 0,\n    todayBorrows: 0,\n    todayReturns: 0,\n    overdueBorrows: 0,\n    popularBooks: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [chartData, setChartData] = useState({\n    borrowTrend: [],\n    categoryStats: [],\n    userActivity: [],\n    monthlyStats: []\n  });\n  const [recentBorrows] = useState([{\n    id: 1,\n    userName: '张三',\n    bookName: 'Java编程思想',\n    borrowTime: '2023-12-01',\n    status: '已借出'\n  }, {\n    id: 2,\n    userName: '李四',\n    bookName: 'Spring Boot实战',\n    borrowTime: '2023-12-02',\n    status: '已归还'\n  }]);\n  const [recentNews] = useState([{\n    id: 1,\n    title: '图书馆开放时间调整通知',\n    createTime: '2023-12-01'\n  }, {\n    id: 2,\n    title: '新书上架通知',\n    createTime: '2023-12-02'\n  }]);\n  useEffect(() => {\n    // 模拟API调用获取统计数据和图表数据\n    const fetchStats = async () => {\n      setLoading(true);\n      // 模拟网络延迟\n      setTimeout(() => {\n        setStats({\n          totalUsers: 150,\n          totalBooks: 1200,\n          totalBorrows: 89,\n          totalFines: 5,\n          todayBorrows: 12,\n          todayReturns: 8,\n          overdueBorrows: 3,\n          popularBooks: 25\n        });\n\n        // 生成图表数据\n        setChartData({\n          // 借阅趋势数据\n          borrowTrend: [{\n            date: '2024-01',\n            count: 45\n          }, {\n            date: '2024-02',\n            count: 52\n          }, {\n            date: '2024-03',\n            count: 61\n          }, {\n            date: '2024-04',\n            count: 58\n          }, {\n            date: '2024-05',\n            count: 67\n          }, {\n            date: '2024-06',\n            count: 73\n          }],\n          // 图书分类统计\n          categoryStats: [{\n            category: '文学',\n            count: 320,\n            percentage: 26.7\n          }, {\n            category: '科技',\n            count: 280,\n            percentage: 23.3\n          }, {\n            category: '历史',\n            count: 200,\n            percentage: 16.7\n          }, {\n            category: '艺术',\n            count: 180,\n            percentage: 15.0\n          }, {\n            category: '教育',\n            count: 120,\n            percentage: 10.0\n          }, {\n            category: '其他',\n            count: 100,\n            percentage: 8.3\n          }],\n          // 用户活跃度\n          userActivity: [{\n            time: '00:00',\n            active: 5\n          }, {\n            time: '06:00',\n            active: 12\n          }, {\n            time: '09:00',\n            active: 45\n          }, {\n            time: '12:00',\n            active: 67\n          }, {\n            time: '15:00',\n            active: 52\n          }, {\n            time: '18:00',\n            active: 78\n          }, {\n            time: '21:00',\n            active: 34\n          }, {\n            time: '23:00',\n            active: 15\n          }],\n          // 月度统计\n          monthlyStats: [{\n            month: '1月',\n            borrows: 234,\n            returns: 198\n          }, {\n            month: '2月',\n            borrows: 267,\n            returns: 245\n          }, {\n            month: '3月',\n            borrows: 298,\n            returns: 276\n          }, {\n            month: '4月',\n            borrows: 312,\n            returns: 289\n          }, {\n            month: '5月',\n            borrows: 345,\n            returns: 321\n          }, {\n            month: '6月',\n            borrows: 378,\n            returns: 356\n          }]\n        });\n        setLoading(false);\n      }, 1000);\n    };\n    fetchStats();\n  }, []);\n  const borrowColumns = [{\n    title: '用户姓名',\n    dataIndex: 'userName',\n    key: 'userName'\n  }, {\n    title: '图书名称',\n    dataIndex: 'bookName',\n    key: 'bookName'\n  }, {\n    title: '借阅时间',\n    dataIndex: 'borrowTime',\n    key: 'borrowTime'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '0 4px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 24,\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        border: 'none',\n        color: 'white'\n      },\n      styles: {\n        body: {\n          padding: '24px 32px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              style: {\n                color: 'white',\n                margin: 0\n              },\n              children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username), \"\\uFF01\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: 'rgba(255, 255, 255, 0.8)',\n                fontSize: 16\n              },\n              children: [\"\\u4ECA\\u5929\\u662F \", new Date().toLocaleDateString('zh-CN', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric',\n                weekday: 'long'\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Avatar, {\n            size: 64,\n            icon: (user === null || user === void 0 ? void 0 : user.role) === '管理员' ? /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 44\n            }, this) : /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 65\n            }, this),\n            style: {\n              backgroundColor: 'rgba(255, 255, 255, 0.2)',\n              border: '2px solid rgba(255, 255, 255, 0.3)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7528\\u6237\\u6570\",\n            value: stats.totalUsers,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              style: {\n                marginLeft: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), \" +12\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u56FE\\u4E66\\u603B\\u6570\",\n            value: stats.totalBooks,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              style: {\n                marginLeft: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), \" \\u70ED\\u95E8\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F53\\u524D\\u501F\\u9605\",\n            value: stats.totalBorrows,\n            prefix: /*#__PURE__*/_jsxDEV(ReadOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"orange\",\n              style: {\n                marginLeft: 8\n              },\n              children: [\"\\u4ECA\\u65E5 +\", stats.todayBorrows]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u903E\\u671F\\u56FE\\u4E66\",\n            value: stats.overdueBorrows,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"red\",\n              style: {\n                marginLeft: 8\n              },\n              children: \"\\u9700\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u501F\\u9605\",\n            value: stats.todayBorrows,\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {\n              style: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: Math.round(stats.todayBorrows / 20 * 100),\n            size: \"small\",\n            strokeColor: \"#722ed1\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u5F52\\u8FD8\",\n            value: stats.todayReturns,\n            prefix: /*#__PURE__*/_jsxDEV(HeartOutlined, {\n              style: {\n                color: '#eb2f96'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#eb2f96'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: Math.round(stats.todayReturns / 15 * 100),\n            size: \"small\",\n            strokeColor: \"#eb2f96\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u70ED\\u95E8\\u56FE\\u4E66\",\n            value: stats.popularBooks,\n            prefix: /*#__PURE__*/_jsxDEV(StarOutlined, {\n              style: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: Math.round(stats.popularBooks / 50 * 100),\n            size: \"small\",\n            strokeColor: \"#fa8c16\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u7F34\\u7F5A\\u91D1\",\n            value: stats.totalFines,\n            prefix: /*#__PURE__*/_jsxDEV(PayCircleOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            },\n            suffix: \"\\u7B14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: 12\n            },\n            children: [\"\\u603B\\u91D1\\u989D: \\xA5\", (stats.totalFines * 2.5).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u501F\\u9605\\u8D8B\\u52BF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 350\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: chartData.borrowTrend,\n            xField: \"date\",\n            yField: \"count\",\n            point: {\n              size: 5,\n              shape: 'diamond',\n              style: {\n                fill: 'white',\n                stroke: '#1890ff',\n                lineWidth: 2\n              }\n            },\n            tooltip: {\n              showMarkers: false\n            },\n            state: {\n              active: {\n                style: {\n                  shadowBlur: 4,\n                  stroke: '#000',\n                  fill: 'red'\n                }\n              }\n            },\n            interactions: [{\n              type: 'marker-active'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(PieChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u56FE\\u4E66\\u5206\\u7C7B\\u5206\\u5E03\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 350\n          },\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            appendPadding: 10,\n            data: chartData.categoryStats,\n            angleField: \"count\",\n            colorField: \"category\",\n            radius: 0.8,\n            label: {\n              type: 'outer',\n              content: '{name} {percentage}'\n            },\n            interactions: [{\n              type: 'element-active'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u6708\\u5EA6\\u501F\\u8FD8\\u7EDF\\u8BA1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 350\n          },\n          children: /*#__PURE__*/_jsxDEV(Column, {\n            data: chartData.monthlyStats.flatMap(item => [{\n              month: item.month,\n              type: '借阅',\n              count: item.borrows\n            }, {\n              month: item.month,\n              type: '归还',\n              count: item.returns\n            }]),\n            xField: \"month\",\n            yField: \"count\",\n            seriesField: \"type\",\n            isGroup: true,\n            columnStyle: {\n              radius: [4, 4, 0, 0]\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7528\\u6237\\u6D3B\\u8DC3\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 350\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: chartData.userActivity,\n            xField: \"time\",\n            yField: \"active\",\n            smooth: true,\n            color: \"#1890ff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ReadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u6700\\u8FD1\\u501F\\u9605\\u8BB0\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: borrowColumns,\n            dataSource: recentBorrows,\n            pagination: false,\n            size: \"small\",\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7CFB\\u7EDF\\u516C\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            children: \"\\u66F4\\u591A\\u516C\\u544A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this),\n          style: {\n            height: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"horizontal\",\n            dataSource: recentNews,\n            loading: loading,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 31\n                  }, this),\n                  style: {\n                    backgroundColor: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 23\n                }, this),\n                title: /*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    fontSize: 14\n                  },\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 12\n                    },\n                    children: item.createTime\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u5FEB\\u6377\\u64CD\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            size: \"middle\",\n            children: [isAdmin() && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                block: true,\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 54\n                }, this),\n                children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 39\n                }, this),\n                children: \"\\u56FE\\u4E66\\u7BA1\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                icon: /*#__PURE__*/_jsxDEV(ReadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 39\n                }, this),\n                children: \"\\u501F\\u9605\\u7BA1\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Button, {\n              block: true,\n              icon: /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 35\n              }, this),\n              children: \"\\u6211\\u7684\\u6536\\u85CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              block: true,\n              icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 35\n              }, this),\n              children: \"\\u501F\\u9605\\u5386\\u53F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7CFB\\u7EDF\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            items: [{\n              color: 'green',\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"\\u6240\\u6709\\u670D\\u52A1\\u8FD0\\u884C\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 21\n              }, this)\n            }, {\n              color: 'blue',\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"\\u54CD\\u5E94\\u65F6\\u95F4: 12ms\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 21\n              }, this)\n            }, {\n              color: 'orange',\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u5B9A\\u65F6\\u4EFB\\u52A1\\u8FD0\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"\\u4E0B\\u6B21\\u6267\\u884C: 23:00\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this), isAdmin() && /*#__PURE__*/_jsxDEV(Row, {\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u7BA1\\u7406\\u5458\\u63D0\\u9192\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginBottom: 8\n              },\n              children: [\"\\u2022 \\u5F53\\u524D\\u6709 \", stats.overdueBorrows, \" \\u672C\\u56FE\\u4E66\\u903E\\u671F\\uFF0C\\u8BF7\\u53CA\\u65F6\\u5904\\u7406\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginBottom: 8\n              },\n              children: [\"\\u2022 \\u4ECA\\u65E5\\u65B0\\u589E\\u7528\\u6237 12 \\u4EBA\\uFF0C\\u56FE\\u4E66\\u501F\\u9605 \", stats.todayBorrows, \" \\u6B21\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginBottom: 0\n              },\n              children: \"\\u2022 \\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\\uFF0C\\u6240\\u6709\\u670D\\u52A1\\u72B6\\u6001\\u826F\\u597D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"LDliC9ZkxV7zVkgi9QzJSVIRI2I=\", false, function () {\n  return [useAuth, usePermission];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Row", "Col", "Card", "Statistic", "Table", "List", "Avatar", "Typography", "Space", "Tag", "Progress", "Timeline", "<PERSON><PERSON>", "<PERSON><PERSON>", "UserOutlined", "BookOutlined", "ReadOutlined", "PayCircleOutlined", "SafetyOutlined", "TrophyOutlined", "ClockCircleOutlined", "RiseOutlined", "CalendarOutlined", "BellOutlined", "HeartOutlined", "StarOutlined", "BarChartOutlined", "PieChartOutlined", "LineChartOutlined", "Column", "Pie", "Line", "useAuth", "usePermission", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "Dashboard", "_s", "user", "isAdmin", "stats", "setStats", "totalUsers", "totalBooks", "totalBorrows", "totalFines", "todayBorrows", "todayReturns", "overdueBorrows", "popularBooks", "loading", "setLoading", "chartData", "setChartData", "borrowTrend", "categoryStats", "userActivity", "monthlyStats", "recentBorrows", "id", "userName", "bookName", "borrowTime", "status", "recentNews", "title", "createTime", "fetchStats", "setTimeout", "date", "count", "category", "percentage", "time", "active", "month", "borrows", "returns", "borrowColumns", "dataIndex", "key", "style", "padding", "children", "marginBottom", "background", "border", "color", "styles", "body", "align", "flex", "direction", "size", "level", "margin", "name", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "Date", "toLocaleDateString", "year", "day", "weekday", "icon", "role", "backgroundColor", "gutter", "xs", "sm", "md", "value", "prefix", "valueStyle", "suffix", "marginLeft", "percent", "Math", "round", "strokeColor", "marginTop", "type", "toFixed", "lg", "height", "data", "xField", "yField", "point", "shape", "fill", "stroke", "lineWidth", "tooltip", "showMarkers", "state", "<PERSON><PERSON><PERSON><PERSON>", "interactions", "appendPadding", "angleField", "colorField", "radius", "label", "content", "flatMap", "item", "seriesField", "isGroup", "columnStyle", "smooth", "extra", "columns", "dataSource", "pagination", "itemLayout", "renderItem", "<PERSON><PERSON>", "Meta", "avatar", "strong", "description", "width", "block", "items", "span", "message", "showIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Statistic,\n  Table,\n  List,\n  Avatar,\n  Typography,\n  Space,\n  Tag,\n  Progress,\n  Timeline,\n  Button,\n  Alert\n} from 'antd';\nimport {\n  UserOutlined,\n  BookOutlined,\n  ReadOutlined,\n  PayCircleOutlined,\n  SafetyOutlined,\n  TrophyOutlined,\n  ClockCircleOutlined,\n  RiseOutlined,\n  CalendarOutlined,\n  <PERSON>Outlined,\n  HeartOutlined,\n  <PERSON>Outlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  Pie<PERSON><PERSON>Outlined,\n  Line<PERSON>hartOutlined,\n} from '@ant-design/icons';\nimport { Column, Pie, Line } from '@ant-design/charts';\nimport { useAuth } from '../contexts/AuthContext';\nimport { usePermission } from '../components/Auth/PermissionWrapper';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { isAdmin } = usePermission();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalBooks: 0,\n    totalBorrows: 0,\n    totalFines: 0,\n    todayBorrows: 0,\n    todayReturns: 0,\n    overdueBorrows: 0,\n    popularBooks: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [chartData, setChartData] = useState({\n    borrowTrend: [] as any[],\n    categoryStats: [] as any[],\n    userActivity: [] as any[],\n    monthlyStats: [] as any[],\n  });\n\n  const [recentBorrows] = useState([\n    {\n      id: 1,\n      userName: '张三',\n      bookName: 'Java编程思想',\n      borrowTime: '2023-12-01',\n      status: '已借出',\n    },\n    {\n      id: 2,\n      userName: '李四',\n      bookName: 'Spring Boot实战',\n      borrowTime: '2023-12-02',\n      status: '已归还',\n    },\n  ]);\n\n  const [recentNews] = useState([\n    {\n      id: 1,\n      title: '图书馆开放时间调整通知',\n      createTime: '2023-12-01',\n    },\n    {\n      id: 2,\n      title: '新书上架通知',\n      createTime: '2023-12-02',\n    },\n  ]);\n\n  useEffect(() => {\n    // 模拟API调用获取统计数据和图表数据\n    const fetchStats = async () => {\n      setLoading(true);\n      // 模拟网络延迟\n      setTimeout(() => {\n        setStats({\n          totalUsers: 150,\n          totalBooks: 1200,\n          totalBorrows: 89,\n          totalFines: 5,\n          todayBorrows: 12,\n          todayReturns: 8,\n          overdueBorrows: 3,\n          popularBooks: 25,\n        });\n\n        // 生成图表数据\n        setChartData({\n          // 借阅趋势数据\n          borrowTrend: [\n            { date: '2024-01', count: 45 },\n            { date: '2024-02', count: 52 },\n            { date: '2024-03', count: 61 },\n            { date: '2024-04', count: 58 },\n            { date: '2024-05', count: 67 },\n            { date: '2024-06', count: 73 },\n          ],\n          // 图书分类统计\n          categoryStats: [\n            { category: '文学', count: 320, percentage: 26.7 },\n            { category: '科技', count: 280, percentage: 23.3 },\n            { category: '历史', count: 200, percentage: 16.7 },\n            { category: '艺术', count: 180, percentage: 15.0 },\n            { category: '教育', count: 120, percentage: 10.0 },\n            { category: '其他', count: 100, percentage: 8.3 },\n          ],\n          // 用户活跃度\n          userActivity: [\n            { time: '00:00', active: 5 },\n            { time: '06:00', active: 12 },\n            { time: '09:00', active: 45 },\n            { time: '12:00', active: 67 },\n            { time: '15:00', active: 52 },\n            { time: '18:00', active: 78 },\n            { time: '21:00', active: 34 },\n            { time: '23:00', active: 15 },\n          ],\n          // 月度统计\n          monthlyStats: [\n            { month: '1月', borrows: 234, returns: 198 },\n            { month: '2月', borrows: 267, returns: 245 },\n            { month: '3月', borrows: 298, returns: 276 },\n            { month: '4月', borrows: 312, returns: 289 },\n            { month: '5月', borrows: 345, returns: 321 },\n            { month: '6月', borrows: 378, returns: 356 },\n          ],\n        });\n\n        setLoading(false);\n      }, 1000);\n    };\n\n    fetchStats();\n  }, []);\n\n  const borrowColumns = [\n    {\n      title: '用户姓名',\n      dataIndex: 'userName',\n      key: 'userName',\n    },\n    {\n      title: '图书名称',\n      dataIndex: 'bookName',\n      key: 'bookName',\n    },\n    {\n      title: '借阅时间',\n      dataIndex: 'borrowTime',\n      key: 'borrowTime',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n    },\n  ];\n\n  return (\n    <div style={{ padding: '0 4px' }}>\n      {/* 欢迎横幅 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          color: 'white'\n        }}\n        styles={{ body: { padding: '24px 32px' } }}\n      >\n        <Row align=\"middle\">\n          <Col flex=\"auto\">\n            <Space direction=\"vertical\" size=\"small\">\n              <Title level={3} style={{ color: 'white', margin: 0 }}>\n                欢迎回来，{user?.name || user?.username}！\n              </Title>\n              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 16 }}>\n                今天是 {new Date().toLocaleDateString('zh-CN', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric',\n                  weekday: 'long'\n                })}\n              </Text>\n            </Space>\n          </Col>\n          <Col>\n            <Avatar\n              size={64}\n              icon={user?.role === '管理员' ? <SafetyOutlined /> : <UserOutlined />}\n              style={{\n                backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                border: '2px solid rgba(255, 255, 255, 0.3)'\n              }}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card loading={loading}>\n            <Statistic\n              title=\"总用户数\"\n              value={stats.totalUsers}\n              prefix={<UserOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a' }}\n              suffix={\n                <Tag color=\"green\" style={{ marginLeft: 8 }}>\n                  <RiseOutlined /> +12\n                </Tag>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card loading={loading}>\n            <Statistic\n              title=\"图书总数\"\n              value={stats.totalBooks}\n              prefix={<BookOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n              suffix={\n                <Tag color=\"blue\" style={{ marginLeft: 8 }}>\n                  <StarOutlined /> 热门\n                </Tag>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card loading={loading}>\n            <Statistic\n              title=\"当前借阅\"\n              value={stats.totalBorrows}\n              prefix={<ReadOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14' }}\n              suffix={\n                <Tag color=\"orange\" style={{ marginLeft: 8 }}>\n                  今日 +{stats.todayBorrows}\n                </Tag>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card loading={loading}>\n            <Statistic\n              title=\"逾期图书\"\n              value={stats.overdueBorrows}\n              prefix={<ClockCircleOutlined style={{ color: '#ff4d4f' }} />}\n              valueStyle={{ color: '#ff4d4f' }}\n              suffix={\n                <Tag color=\"red\" style={{ marginLeft: 8 }}>\n                  需处理\n                </Tag>\n              }\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 今日数据 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"今日借阅\"\n              value={stats.todayBorrows}\n              prefix={<TrophyOutlined style={{ color: '#722ed1' }} />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n            <Progress\n              percent={Math.round((stats.todayBorrows / 20) * 100)}\n              size=\"small\"\n              strokeColor=\"#722ed1\"\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"今日归还\"\n              value={stats.todayReturns}\n              prefix={<HeartOutlined style={{ color: '#eb2f96' }} />}\n              valueStyle={{ color: '#eb2f96' }}\n            />\n            <Progress\n              percent={Math.round((stats.todayReturns / 15) * 100)}\n              size=\"small\"\n              strokeColor=\"#eb2f96\"\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"热门图书\"\n              value={stats.popularBooks}\n              prefix={<StarOutlined style={{ color: '#fa8c16' }} />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n            <Progress\n              percent={Math.round((stats.popularBooks / 50) * 100)}\n              size=\"small\"\n              strokeColor=\"#fa8c16\"\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"待缴罚金\"\n              value={stats.totalFines}\n              prefix={<PayCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14' }}\n              suffix=\"笔\"\n            />\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              总金额: ¥{(stats.totalFines * 2.5).toFixed(2)}\n            </Text>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表展示区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <LineChartOutlined />\n                <span>借阅趋势</span>\n              </Space>\n            }\n            style={{ height: 350 }}\n          >\n            <Line\n              data={chartData.borrowTrend}\n              xField=\"date\"\n              yField=\"count\"\n              point={{\n                size: 5,\n                shape: 'diamond',\n                style: {\n                  fill: 'white',\n                  stroke: '#1890ff',\n                  lineWidth: 2,\n                },\n              }}\n              tooltip={{\n                showMarkers: false,\n              }}\n              state={{\n                active: {\n                  style: {\n                    shadowBlur: 4,\n                    stroke: '#000',\n                    fill: 'red',\n                  },\n                },\n              }}\n              interactions={[\n                {\n                  type: 'marker-active',\n                },\n              ]}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <PieChartOutlined />\n                <span>图书分类分布</span>\n              </Space>\n            }\n            style={{ height: 350 }}\n          >\n            <Pie\n              appendPadding={10}\n              data={chartData.categoryStats}\n              angleField=\"count\"\n              colorField=\"category\"\n              radius={0.8}\n              label={{\n                type: 'outer',\n                content: '{name} {percentage}',\n              }}\n              interactions={[\n                {\n                  type: 'element-active',\n                },\n              ]}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <BarChartOutlined />\n                <span>月度借还统计</span>\n              </Space>\n            }\n            style={{ height: 350 }}\n          >\n            <Column\n              data={chartData.monthlyStats.flatMap(item => [\n                { month: item.month, type: '借阅', count: item.borrows },\n                { month: item.month, type: '归还', count: item.returns },\n              ])}\n              xField=\"month\"\n              yField=\"count\"\n              seriesField=\"type\"\n              isGroup={true}\n              columnStyle={{\n                radius: [4, 4, 0, 0],\n              }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <LineChartOutlined />\n                <span>用户活跃度</span>\n              </Space>\n            }\n            style={{ height: 350 }}\n          >\n            <Line\n              data={chartData.userActivity}\n              xField=\"time\"\n              yField=\"active\"\n              smooth={true}\n              color=\"#1890ff\"\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容区域 */}\n      <Row gutter={[16, 16]}>\n        <Col xs={24} lg={16}>\n          <Card\n            title={\n              <Space>\n                <ReadOutlined />\n                <span>最近借阅记录</span>\n              </Space>\n            }\n            extra={\n              <Button type=\"link\" size=\"small\">\n                查看全部\n              </Button>\n            }\n            style={{ height: 400 }}\n          >\n            <Table\n              columns={borrowColumns}\n              dataSource={recentBorrows}\n              pagination={false}\n              size=\"small\"\n              loading={loading}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} lg={8}>\n          <Card\n            title={\n              <Space>\n                <BellOutlined />\n                <span>系统公告</span>\n              </Space>\n            }\n            extra={\n              <Button type=\"link\" size=\"small\">\n                更多公告\n              </Button>\n            }\n            style={{ height: 400 }}\n          >\n            <List\n              itemLayout=\"horizontal\"\n              dataSource={recentNews}\n              loading={loading}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar\n                        icon={<BellOutlined />}\n                        style={{ backgroundColor: '#1890ff' }}\n                      />\n                    }\n                    title={\n                      <Text strong style={{ fontSize: 14 }}>\n                        {item.title}\n                      </Text>\n                    }\n                    description={\n                      <Space>\n                        <CalendarOutlined />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {item.createTime}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快捷操作和系统状态 */}\n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        <Col xs={24} md={12}>\n          <Card\n            title={\n              <Space>\n                <TrophyOutlined />\n                <span>快捷操作</span>\n              </Space>\n            }\n          >\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"middle\">\n              {isAdmin() && (\n                <>\n                  <Button type=\"primary\" block icon={<UserOutlined />}>\n                    用户管理\n                  </Button>\n                  <Button block icon={<BookOutlined />}>\n                    图书管理\n                  </Button>\n                  <Button block icon={<ReadOutlined />}>\n                    借阅管理\n                  </Button>\n                </>\n              )}\n              <Button block icon={<HeartOutlined />}>\n                我的收藏\n              </Button>\n              <Button block icon={<CalendarOutlined />}>\n                借阅历史\n              </Button>\n            </Space>\n          </Card>\n        </Col>\n        <Col xs={24} md={12}>\n          <Card\n            title={\n              <Space>\n                <ClockCircleOutlined />\n                <span>系统状态</span>\n              </Space>\n            }\n          >\n            <Timeline\n              items={[\n                {\n                  color: 'green',\n                  children: (\n                    <div>\n                      <Text strong>系统运行正常</Text>\n                      <br />\n                      <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                        所有服务运行稳定\n                      </Text>\n                    </div>\n                  ),\n                },\n                {\n                  color: 'blue',\n                  children: (\n                    <div>\n                      <Text strong>数据库连接正常</Text>\n                      <br />\n                      <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                        响应时间: 12ms\n                      </Text>\n                    </div>\n                  ),\n                },\n                {\n                  color: 'orange',\n                  children: (\n                    <div>\n                      <Text strong>定时任务运行中</Text>\n                      <br />\n                      <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                        下次执行: 23:00\n                      </Text>\n                    </div>\n                  ),\n                },\n              ]}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 管理员专用提醒 */}\n      {isAdmin() && (\n        <Row style={{ marginTop: 24 }}>\n          <Col span={24}>\n            <Alert\n              message=\"管理员提醒\"\n              description={\n                <div>\n                  <Paragraph style={{ marginBottom: 8 }}>\n                    • 当前有 {stats.overdueBorrows} 本图书逾期，请及时处理\n                  </Paragraph>\n                  <Paragraph style={{ marginBottom: 8 }}>\n                    • 今日新增用户 12 人，图书借阅 {stats.todayBorrows} 次\n                  </Paragraph>\n                  <Paragraph style={{ marginBottom: 0 }}>\n                    • 系统运行正常，所有服务状态良好\n                  </Paragraph>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          </Col>\n        </Row>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,iBAAiB,QACZ,mBAAmB;AAC1B,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,QAAQ,oBAAoB;AACtD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,aAAa,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGjC,UAAU;AAE7C,MAAMkC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEY;EAAQ,CAAC,GAAGX,aAAa,CAAC,CAAC;EACnC,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC;IACjCgD,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC;IACzC4D,WAAW,EAAE,EAAW;IACxBC,aAAa,EAAE,EAAW;IAC1BC,YAAY,EAAE,EAAW;IACzBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,CAC/B;IACEiE,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,eAAe;IACzBC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,CAC5B;IACEiE,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;EAEFzE,SAAS,CAAC,MAAM;IACd;IACA,MAAM0E,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BhB,UAAU,CAAC,IAAI,CAAC;MAChB;MACAiB,UAAU,CAAC,MAAM;QACf3B,QAAQ,CAAC;UACPC,UAAU,EAAE,GAAG;UACfC,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,EAAE;UAChBC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,EAAE;UAChBC,YAAY,EAAE,CAAC;UACfC,cAAc,EAAE,CAAC;UACjBC,YAAY,EAAE;QAChB,CAAC,CAAC;;QAEF;QACAI,YAAY,CAAC;UACX;UACAC,WAAW,EAAE,CACX;YAAEe,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC9B;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC9B;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC9B;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC9B;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC9B;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAG,CAAC,CAC/B;UACD;UACAf,aAAa,EAAE,CACb;YAAEgB,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAK,CAAC,EAChD;YAAED,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAK,CAAC,EAChD;YAAED,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAK,CAAC,EAChD;YAAED,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAK,CAAC,EAChD;YAAED,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAK,CAAC,EAChD;YAAED,QAAQ,EAAE,IAAI;YAAED,KAAK,EAAE,GAAG;YAAEE,UAAU,EAAE;UAAI,CAAC,CAChD;UACD;UACAhB,YAAY,EAAE,CACZ;YAAEiB,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAE,CAAC,EAC5B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,EAC7B;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAG,CAAC,CAC9B;UACD;UACAjB,YAAY,EAAE,CACZ;YAAEkB,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC,EAC3C;YAAEF,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC,EAC3C;YAAEF,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC,EAC3C;YAAEF,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC,EAC3C;YAAEF,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC,EAC3C;YAAEF,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAI,CAAC;QAE/C,CAAC,CAAC;QAEF1B,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDgB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,aAAa,GAAG,CACpB;IACEb,KAAK,EAAE,MAAM;IACbc,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbc,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbc,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE;EACP,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXc,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE;EACP,CAAC,CACF;EAED,oBACElD,OAAA;IAAKmD,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAE/BrD,OAAA,CAACjC,IAAI;MACHoF,KAAK,EAAE;QACLG,YAAY,EAAE,EAAE;QAChBC,UAAU,EAAE,mDAAmD;QAC/DC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE;MACT,CAAE;MACFC,MAAM,EAAE;QAAEC,IAAI,EAAE;UAAEP,OAAO,EAAE;QAAY;MAAE,CAAE;MAAAC,QAAA,eAE3CrD,OAAA,CAACnC,GAAG;QAAC+F,KAAK,EAAC,QAAQ;QAAAP,QAAA,gBACjBrD,OAAA,CAAClC,GAAG;UAAC+F,IAAI,EAAC,MAAM;UAAAR,QAAA,eACdrD,OAAA,CAAC3B,KAAK;YAACyF,SAAS,EAAC,UAAU;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,gBACtCrD,OAAA,CAACG,KAAK;cAAC6D,KAAK,EAAE,CAAE;cAACb,KAAK,EAAE;gBAAEM,KAAK,EAAE,OAAO;gBAAEQ,MAAM,EAAE;cAAE,CAAE;cAAAZ,QAAA,GAAC,gCAChD,EAAC,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,MAAI1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,QAAQ,GAAC,QACrC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA,CAACI,IAAI;cAAC+C,KAAK,EAAE;gBAAEM,KAAK,EAAE,0BAA0B;gBAAEe,QAAQ,EAAE;cAAG,CAAE;cAAAnB,QAAA,GAAC,qBAC5D,EAAC,IAAIoB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;gBAC1CC,IAAI,EAAE,SAAS;gBACf9B,KAAK,EAAE,MAAM;gBACb+B,GAAG,EAAE,SAAS;gBACdC,OAAO,EAAE;cACX,CAAC,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvE,OAAA,CAAClC,GAAG;UAAAuF,QAAA,eACFrD,OAAA,CAAC7B,MAAM;YACL4F,IAAI,EAAE,EAAG;YACTe,IAAI,EAAE,CAAAtE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,IAAI,MAAK,KAAK,gBAAG/E,OAAA,CAACjB,cAAc;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGvE,OAAA,CAACrB,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEpB,KAAK,EAAE;cACL6B,eAAe,EAAE,0BAA0B;cAC3CxB,MAAM,EAAE;YACV;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC9B,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,gBACjDrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAACqD,OAAO,EAAEA,OAAQ;UAAAiC,QAAA,eACrBrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACE,UAAW;YACxB0E,MAAM,eAAEtF,OAAA,CAACrB,YAAY;cAACwE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU,CAAE;YACjC+B,MAAM,eACJxF,OAAA,CAAC1B,GAAG;cAACmF,KAAK,EAAC,OAAO;cAACN,KAAK,EAAE;gBAAEsC,UAAU,EAAE;cAAE,CAAE;cAAApC,QAAA,gBAC1CrD,OAAA,CAACd,YAAY;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAClB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAACqD,OAAO,EAAEA,OAAQ;UAAAiC,QAAA,eACrBrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACG,UAAW;YACxByE,MAAM,eAAEtF,OAAA,CAACpB,YAAY;cAACuE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU,CAAE;YACjC+B,MAAM,eACJxF,OAAA,CAAC1B,GAAG;cAACmF,KAAK,EAAC,MAAM;cAACN,KAAK,EAAE;gBAAEsC,UAAU,EAAE;cAAE,CAAE;cAAApC,QAAA,gBACzCrD,OAAA,CAACV,YAAY;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAClB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAACqD,OAAO,EAAEA,OAAQ;UAAAiC,QAAA,eACrBrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACI,YAAa;YAC1BwE,MAAM,eAAEtF,OAAA,CAACnB,YAAY;cAACsE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU,CAAE;YACjC+B,MAAM,eACJxF,OAAA,CAAC1B,GAAG;cAACmF,KAAK,EAAC,QAAQ;cAACN,KAAK,EAAE;gBAAEsC,UAAU,EAAE;cAAE,CAAE;cAAApC,QAAA,GAAC,gBACxC,EAAC3C,KAAK,CAACM,YAAY;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAACqD,OAAO,EAAEA,OAAQ;UAAAiC,QAAA,eACrBrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACQ,cAAe;YAC5BoE,MAAM,eAAEtF,OAAA,CAACf,mBAAmB;cAACkE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7DgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU,CAAE;YACjC+B,MAAM,eACJxF,OAAA,CAAC1B,GAAG;cAACmF,KAAK,EAAC,KAAK;cAACN,KAAK,EAAE;gBAAEsC,UAAU,EAAE;cAAE,CAAE;cAAApC,QAAA,EAAC;YAE3C;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC9B,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,gBACjDrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAAAsF,QAAA,gBACHrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACM,YAAa;YAC1BsE,MAAM,eAAEtF,OAAA,CAAChB,cAAc;cAACmE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFvE,OAAA,CAACzB,QAAQ;YACPmH,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAElF,KAAK,CAACM,YAAY,GAAG,EAAE,GAAI,GAAG,CAAE;YACrD+C,IAAI,EAAC,OAAO;YACZ8B,WAAW,EAAC,SAAS;YACrB1C,KAAK,EAAE;cAAE2C,SAAS,EAAE;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAAAsF,QAAA,gBACHrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACO,YAAa;YAC1BqE,MAAM,eAAEtF,OAAA,CAACX,aAAa;cAAC8D,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFvE,OAAA,CAACzB,QAAQ;YACPmH,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAElF,KAAK,CAACO,YAAY,GAAG,EAAE,GAAI,GAAG,CAAE;YACrD8C,IAAI,EAAC,OAAO;YACZ8B,WAAW,EAAC,SAAS;YACrB1C,KAAK,EAAE;cAAE2C,SAAS,EAAE;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAAAsF,QAAA,gBACHrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACS,YAAa;YAC1BmE,MAAM,eAAEtF,OAAA,CAACV,YAAY;cAAC6D,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtDgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFvE,OAAA,CAACzB,QAAQ;YACPmH,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAElF,KAAK,CAACS,YAAY,GAAG,EAAE,GAAI,GAAG,CAAE;YACrD4C,IAAI,EAAC,OAAO;YACZ8B,WAAW,EAAC,SAAS;YACrB1C,KAAK,EAAE;cAAE2C,SAAS,EAAE;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACzBrD,OAAA,CAACjC,IAAI;UAAAsF,QAAA,gBACHrD,OAAA,CAAChC,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZkD,KAAK,EAAE3E,KAAK,CAACK,UAAW;YACxBuE,MAAM,eAAEtF,OAAA,CAAClB,iBAAiB;cAACqE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3DgB,UAAU,EAAE;cAAE9B,KAAK,EAAE;YAAU,CAAE;YACjC+B,MAAM,EAAC;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFvE,OAAA,CAACI,IAAI;YAAC2F,IAAI,EAAC,WAAW;YAAC5C,KAAK,EAAE;cAAEqB,QAAQ,EAAE;YAAG,CAAE;YAAAnB,QAAA,GAAC,0BACxC,EAAC,CAAC3C,KAAK,CAACK,UAAU,GAAG,GAAG,EAAEiF,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC9B,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,gBACjDrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAAA5C,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACP,iBAAiB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBvE,OAAA;cAAAqD,QAAA,EAAM;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAACJ,IAAI;YACHuG,IAAI,EAAE7E,SAAS,CAACE,WAAY;YAC5B4E,MAAM,EAAC,MAAM;YACbC,MAAM,EAAC,OAAO;YACdC,KAAK,EAAE;cACLvC,IAAI,EAAE,CAAC;cACPwC,KAAK,EAAE,SAAS;cAChBpD,KAAK,EAAE;gBACLqD,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE,SAAS;gBACjBC,SAAS,EAAE;cACb;YACF,CAAE;YACFC,OAAO,EAAE;cACPC,WAAW,EAAE;YACf,CAAE;YACFC,KAAK,EAAE;cACLjE,MAAM,EAAE;gBACNO,KAAK,EAAE;kBACL2D,UAAU,EAAE,CAAC;kBACbL,MAAM,EAAE,MAAM;kBACdD,IAAI,EAAE;gBACR;cACF;YACF,CAAE;YACFO,YAAY,EAAE,CACZ;cACEhB,IAAI,EAAE;YACR,CAAC;UACD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAAA5C,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACR,gBAAgB;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBvE,OAAA;cAAAqD,QAAA,EAAM;YAAM;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACR;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAACL,GAAG;YACFqH,aAAa,EAAE,EAAG;YAClBb,IAAI,EAAE7E,SAAS,CAACG,aAAc;YAC9BwF,UAAU,EAAC,OAAO;YAClBC,UAAU,EAAC,UAAU;YACrBC,MAAM,EAAE,GAAI;YACZC,KAAK,EAAE;cACLrB,IAAI,EAAE,OAAO;cACbsB,OAAO,EAAE;YACX,CAAE;YACFN,YAAY,EAAE,CACZ;cACEhB,IAAI,EAAE;YACR,CAAC;UACD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC9B,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,gBACjDrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAAA5C,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACT,gBAAgB;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBvE,OAAA;cAAAqD,QAAA,EAAM;YAAM;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACR;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAACN,MAAM;YACLyG,IAAI,EAAE7E,SAAS,CAACK,YAAY,CAAC2F,OAAO,CAACC,IAAI,IAAI,CAC3C;cAAE1E,KAAK,EAAE0E,IAAI,CAAC1E,KAAK;cAAEkD,IAAI,EAAE,IAAI;cAAEvD,KAAK,EAAE+E,IAAI,CAACzE;YAAQ,CAAC,EACtD;cAAED,KAAK,EAAE0E,IAAI,CAAC1E,KAAK;cAAEkD,IAAI,EAAE,IAAI;cAAEvD,KAAK,EAAE+E,IAAI,CAACxE;YAAQ,CAAC,CACvD,CAAE;YACHqD,MAAM,EAAC,OAAO;YACdC,MAAM,EAAC,OAAO;YACdmB,WAAW,EAAC,MAAM;YAClBC,OAAO,EAAE,IAAK;YACdC,WAAW,EAAE;cACXP,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACrB;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAAA5C,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACP,iBAAiB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBvE,OAAA;cAAAqD,QAAA,EAAM;YAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACR;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAACJ,IAAI;YACHuG,IAAI,EAAE7E,SAAS,CAACI,YAAa;YAC7B0E,MAAM,EAAC,MAAM;YACbC,MAAM,EAAC,QAAQ;YACfsB,MAAM,EAAE,IAAK;YACblE,KAAK,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAA5B,QAAA,gBACpBrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAAA5C,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACnB,YAAY;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChBvE,OAAA;cAAAqD,QAAA,EAAM;YAAM;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACR;UACDqD,KAAK,eACH5H,OAAA,CAACvB,MAAM;YAACsH,IAAI,EAAC,MAAM;YAAChC,IAAI,EAAC,OAAO;YAAAV,QAAA,EAAC;UAEjC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAAC/B,KAAK;YACJ4J,OAAO,EAAE7E,aAAc;YACvB8E,UAAU,EAAElG,aAAc;YAC1BmG,UAAU,EAAE,KAAM;YAClBhE,IAAI,EAAC,OAAO;YACZ3C,OAAO,EAAEA;UAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACjBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACZ,YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChBvE,OAAA;cAAAqD,QAAA,EAAM;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UACDqD,KAAK,eACH5H,OAAA,CAACvB,MAAM;YAACsH,IAAI,EAAC,MAAM;YAAChC,IAAI,EAAC,OAAO;YAAAV,QAAA,EAAC;UAEjC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDpB,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAI,CAAE;UAAA7C,QAAA,eAEvBrD,OAAA,CAAC9B,IAAI;YACH8J,UAAU,EAAC,YAAY;YACvBF,UAAU,EAAE5F,UAAW;YACvBd,OAAO,EAAEA,OAAQ;YACjB6G,UAAU,EAAGV,IAAI,iBACfvH,OAAA,CAAC9B,IAAI,CAACgK,IAAI;cAAA7E,QAAA,eACRrD,OAAA,CAAC9B,IAAI,CAACgK,IAAI,CAACC,IAAI;gBACbC,MAAM,eACJpI,OAAA,CAAC7B,MAAM;kBACL2G,IAAI,eAAE9E,OAAA,CAACZ,YAAY;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBpB,KAAK,EAAE;oBAAE6B,eAAe,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACF;gBACDpC,KAAK,eACHnC,OAAA,CAACI,IAAI;kBAACiI,MAAM;kBAAClF,KAAK,EAAE;oBAAEqB,QAAQ,EAAE;kBAAG,CAAE;kBAAAnB,QAAA,EAClCkE,IAAI,CAACpF;gBAAK;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACP;gBACD+D,WAAW,eACTtI,OAAA,CAAC3B,KAAK;kBAAAgF,QAAA,gBACJrD,OAAA,CAACb,gBAAgB;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpBvE,OAAA,CAACI,IAAI;oBAAC2F,IAAI,EAAC,WAAW;oBAAC5C,KAAK,EAAE;sBAAEqB,QAAQ,EAAE;oBAAG,CAAE;oBAAAnB,QAAA,EAC5CkE,IAAI,CAACnF;kBAAU;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA,CAACnC,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC9B,KAAK,EAAE;QAAE2C,SAAS,EAAE;MAAG,CAAE;MAAAzC,QAAA,gBAC9CrD,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAA/B,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAAChB,cAAc;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClBvE,OAAA;cAAAqD,QAAA,EAAM;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UAAAlB,QAAA,eAEDrD,OAAA,CAAC3B,KAAK;YAACyF,SAAS,EAAC,UAAU;YAACX,KAAK,EAAE;cAAEoF,KAAK,EAAE;YAAO,CAAE;YAACxE,IAAI,EAAC,QAAQ;YAAAV,QAAA,GAChE5C,OAAO,CAAC,CAAC,iBACRT,OAAA,CAAAE,SAAA;cAAAmD,QAAA,gBACErD,OAAA,CAACvB,MAAM;gBAACsH,IAAI,EAAC,SAAS;gBAACyC,KAAK;gBAAC1D,IAAI,eAAE9E,OAAA,CAACrB,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAlB,QAAA,EAAC;cAErD;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvE,OAAA,CAACvB,MAAM;gBAAC+J,KAAK;gBAAC1D,IAAI,eAAE9E,OAAA,CAACpB,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAlB,QAAA,EAAC;cAEtC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvE,OAAA,CAACvB,MAAM;gBAAC+J,KAAK;gBAAC1D,IAAI,eAAE9E,OAAA,CAACnB,YAAY;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAlB,QAAA,EAAC;cAEtC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CACH,eACDvE,OAAA,CAACvB,MAAM;cAAC+J,KAAK;cAAC1D,IAAI,eAAE9E,OAAA,CAACX,aAAa;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAlB,QAAA,EAAC;YAEvC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvE,OAAA,CAACvB,MAAM;cAAC+J,KAAK;cAAC1D,IAAI,eAAE9E,OAAA,CAACb,gBAAgB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAlB,QAAA,EAAC;YAE1C;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAAClC,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAA/B,QAAA,eAClBrD,OAAA,CAACjC,IAAI;UACHoE,KAAK,eACHnC,OAAA,CAAC3B,KAAK;YAAAgF,QAAA,gBACJrD,OAAA,CAACf,mBAAmB;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBvE,OAAA;cAAAqD,QAAA,EAAM;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UAAAlB,QAAA,eAEDrD,OAAA,CAACxB,QAAQ;YACPiK,KAAK,EAAE,CACL;cACEhF,KAAK,EAAE,OAAO;cACdJ,QAAQ,eACNrD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACI,IAAI;kBAACiI,MAAM;kBAAAhF,QAAA,EAAC;gBAAM;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BvE,OAAA;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvE,OAAA,CAACI,IAAI;kBAAC2F,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqB,QAAQ,EAAE;kBAAG,CAAE;kBAAAnB,QAAA,EAAC;gBAEhD;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAET,CAAC,EACD;cACEd,KAAK,EAAE,MAAM;cACbJ,QAAQ,eACNrD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACI,IAAI;kBAACiI,MAAM;kBAAAhF,QAAA,EAAC;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BvE,OAAA;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvE,OAAA,CAACI,IAAI;kBAAC2F,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqB,QAAQ,EAAE;kBAAG,CAAE;kBAAAnB,QAAA,EAAC;gBAEhD;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAET,CAAC,EACD;cACEd,KAAK,EAAE,QAAQ;cACfJ,QAAQ,eACNrD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACI,IAAI;kBAACiI,MAAM;kBAAAhF,QAAA,EAAC;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BvE,OAAA;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvE,OAAA,CAACI,IAAI;kBAAC2F,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqB,QAAQ,EAAE;kBAAG,CAAE;kBAAAnB,QAAA,EAAC;gBAEhD;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAET,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9D,OAAO,CAAC,CAAC,iBACRT,OAAA,CAACnC,GAAG;MAACsF,KAAK,EAAE;QAAE2C,SAAS,EAAE;MAAG,CAAE;MAAAzC,QAAA,eAC5BrD,OAAA,CAAClC,GAAG;QAAC4K,IAAI,EAAE,EAAG;QAAArF,QAAA,eACZrD,OAAA,CAACtB,KAAK;UACJiK,OAAO,EAAC,gCAAO;UACfL,WAAW,eACTtI,OAAA;YAAAqD,QAAA,gBACErD,OAAA,CAACK,SAAS;cAAC8C,KAAK,EAAE;gBAAEG,YAAY,EAAE;cAAE,CAAE;cAAAD,QAAA,GAAC,4BAC/B,EAAC3C,KAAK,CAACQ,cAAc,EAAC,qEAC9B;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZvE,OAAA,CAACK,SAAS;cAAC8C,KAAK,EAAE;gBAAEG,YAAY,EAAE;cAAE,CAAE;cAAAD,QAAA,GAAC,sFAClB,EAAC3C,KAAK,CAACM,YAAY,EAAC,SACzC;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZvE,OAAA,CAACK,SAAS;cAAC8C,KAAK,EAAE;gBAAEG,YAAY,EAAE;cAAE,CAAE;cAAAD,QAAA,EAAC;YAEvC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACN;UACDwB,IAAI,EAAC,MAAM;UACX6C,QAAQ;UACRzF,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAG;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChE,EAAA,CA/mBID,SAAmB;EAAA,QACNT,OAAO,EACJC,aAAa;AAAA;AAAA+I,EAAA,GAF7BvI,SAAmB;AAinBzB,eAAeA,SAAS;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}