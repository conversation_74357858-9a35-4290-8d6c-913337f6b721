{"ast": null, "code": "/**\n * <zh/> 获取元素的 zIndex\n * <en/> Get the zIndex of the element\n * @param datum - <zh/> 元素数据 | <en/> element data\n * @returns - <zh/> zIndex | <en/> zIndex\n */\nexport function getZIndexOf(datum) {\n  var _a;\n  return ((_a = datum === null || datum === void 0 ? void 0 : datum.style) === null || _a === void 0 ? void 0 : _a.zIndex) || 0;\n}", "map": {"version": 3, "names": ["getZIndexOf", "datum", "_a", "style", "zIndex"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/@antv/g6/src/utils/z-index.ts"], "sourcesContent": ["import { ElementDatum } from '../types';\n\n/**\n * <zh/> 获取元素的 zIndex\n * <en/> Get the zIndex of the element\n * @param datum - <zh/> 元素数据 | <en/> element data\n * @returns - <zh/> zIndex | <en/> zIndex\n */\nexport function getZIndexOf(datum: ElementDatum): number {\n  return datum?.style?.zIndex || 0;\n}\n"], "mappings": "AAEA;;;;;;AAMA,OAAM,SAAUA,WAAWA,CAACC,KAAmB;;EAC7C,OAAO,EAAAC,EAAA,GAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,MAAM,KAAI,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}