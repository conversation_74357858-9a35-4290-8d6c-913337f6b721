{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\n\n// 扩展 AxiosInstance 类型以返回解包后的数据\n\n// 创建axios实例\nconst request = axios.create({\n  baseURL: process.env.NODE_ENV === 'development' ? '/springboot33dng' : '/springboot33dng',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(config => {\n  // 添加token到请求头 - 后端期望的是Token头而不是Authorization\n  const token = localStorage.getItem('token');\n  if (token && config.headers) {\n    config.headers.Token = token;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nrequest.interceptors.response.use(response => {\n  const {\n    data\n  } = response;\n\n  // 如果是文件下载等特殊响应，直接返回\n  if (response.config.responseType === 'blob') {\n    return response;\n  }\n\n  // 检查业务状态码\n  if (data.code === 0) {\n    // 如果有data字段，返回data字段的内容，否则返回整个data对象（去除code字段）\n    if (data.data !== undefined) {\n      return data.data;\n    } else {\n      // 创建一个新对象，排除code和msg字段\n      const {\n        code,\n        msg,\n        ...result\n      } = data;\n      return result;\n    }\n  } else {\n    message.error(data.msg || '请求失败');\n    return Promise.reject(new Error(data.msg || '请求失败'));\n  }\n}, error => {\n  // 处理HTTP错误状态码\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 401:\n        message.error('未授权，请重新登录');\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        break;\n      case 403:\n        message.error('拒绝访问');\n        break;\n      case 404:\n        message.error('请求地址不存在');\n        break;\n      case 500:\n        message.error('服务器内部错误');\n        break;\n      default:\n        message.error((data === null || data === void 0 ? void 0 : data.msg) || `请求失败 (${status})`);\n    }\n  } else if (error.request) {\n    message.error('网络错误，请检查网络连接');\n  } else {\n    message.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\nexport default request;", "map": {"version": 3, "names": ["axios", "message", "request", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "interceptors", "use", "config", "token", "localStorage", "getItem", "Token", "error", "Promise", "reject", "response", "data", "responseType", "code", "undefined", "msg", "result", "Error", "status", "removeItem", "window", "location", "href"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts"], "sourcesContent": ["import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';\nimport { message } from 'antd';\nimport { ApiResponse } from '../types';\n\n// 扩展 AxiosInstance 类型以返回解包后的数据\ninterface CustomAxiosInstance extends Omit<AxiosInstance, 'get' | 'post' | 'put' | 'delete'> {\n  get<T = any>(url: string, config?: any): Promise<T>;\n  post<T = any>(url: string, data?: any, config?: any): Promise<T>;\n  put<T = any>(url: string, data?: any, config?: any): Promise<T>;\n  delete<T = any>(url: string, config?: any): Promise<T>;\n}\n\n// 创建axios实例\nconst request: CustomAxiosInstance = axios.create({\n  baseURL: process.env.NODE_ENV === 'development' ? '/springboot33dng' : '/springboot33dng',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(\n  (config: InternalAxiosRequestConfig) => {\n    // 添加token到请求头 - 后端期望的是Token头而不是Authorization\n    const token = localStorage.getItem('token');\n    if (token && config.headers) {\n      config.headers.Token = token;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nrequest.interceptors.response.use(\n  (response: AxiosResponse<ApiResponse>) => {\n    const { data } = response;\n    \n    // 如果是文件下载等特殊响应，直接返回\n    if (response.config.responseType === 'blob') {\n      return response;\n    }\n    \n    // 检查业务状态码\n    if (data.code === 0) {\n      // 如果有data字段，返回data字段的内容，否则返回整个data对象（去除code字段）\n      if (data.data !== undefined) {\n        return data.data;\n      } else {\n        // 创建一个新对象，排除code和msg字段\n        const { code, msg, ...result } = data;\n        return result;\n      }\n    } else {\n      message.error(data.msg || '请求失败');\n      return Promise.reject(new Error(data.msg || '请求失败'));\n    }\n  },\n  (error) => {\n    // 处理HTTP错误状态码\n    if (error.response) {\n      const { status, data } = error.response;\n      \n      switch (status) {\n        case 401:\n          message.error('未授权，请重新登录');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n          break;\n        case 403:\n          message.error('拒绝访问');\n          break;\n        case 404:\n          message.error('请求地址不存在');\n          break;\n        case 500:\n          message.error('服务器内部错误');\n          break;\n        default:\n          message.error(data?.msg || `请求失败 (${status})`);\n      }\n    } else if (error.request) {\n      message.error('网络错误，请检查网络连接');\n    } else {\n      message.error('请求配置错误');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\nexport default request;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoE,OAAO;AACvF,SAASC,OAAO,QAAQ,MAAM;;AAG9B;;AAQA;AACA,MAAMC,OAA4B,GAAGF,KAAK,CAACG,MAAM,CAAC;EAChDC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAG,kBAAkB,GAAG,kBAAkB;EACzFC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACR,OAAO,CAACS,GAAG,CAC7BC,MAAkC,IAAK;EACtC;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,IAAID,MAAM,CAACH,OAAO,EAAE;IAC3BG,MAAM,CAACH,OAAO,CAACO,KAAK,GAAGH,KAAK;EAC9B;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,OAAO,CAACQ,YAAY,CAACU,QAAQ,CAACT,GAAG,CAC9BS,QAAoC,IAAK;EACxC,MAAM;IAAEC;EAAK,CAAC,GAAGD,QAAQ;;EAEzB;EACA,IAAIA,QAAQ,CAACR,MAAM,CAACU,YAAY,KAAK,MAAM,EAAE;IAC3C,OAAOF,QAAQ;EACjB;;EAEA;EACA,IAAIC,IAAI,CAACE,IAAI,KAAK,CAAC,EAAE;IACnB;IACA,IAAIF,IAAI,CAACA,IAAI,KAAKG,SAAS,EAAE;MAC3B,OAAOH,IAAI,CAACA,IAAI;IAClB,CAAC,MAAM;MACL;MACA,MAAM;QAAEE,IAAI;QAAEE,GAAG;QAAE,GAAGC;MAAO,CAAC,GAAGL,IAAI;MACrC,OAAOK,MAAM;IACf;EACF,CAAC,MAAM;IACLzB,OAAO,CAACgB,KAAK,CAACI,IAAI,CAACI,GAAG,IAAI,MAAM,CAAC;IACjC,OAAOP,OAAO,CAACC,MAAM,CAAC,IAAIQ,KAAK,CAACN,IAAI,CAACI,GAAG,IAAI,MAAM,CAAC,CAAC;EACtD;AACF,CAAC,EACAR,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB,MAAM;MAAEQ,MAAM;MAAEP;IAAK,CAAC,GAAGJ,KAAK,CAACG,QAAQ;IAEvC,QAAQQ,MAAM;MACZ,KAAK,GAAG;QACN3B,OAAO,CAACgB,KAAK,CAAC,WAAW,CAAC;QAC1BH,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;QAChCf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACF,KAAK,GAAG;QACN/B,OAAO,CAACgB,KAAK,CAAC,MAAM,CAAC;QACrB;MACF,KAAK,GAAG;QACNhB,OAAO,CAACgB,KAAK,CAAC,SAAS,CAAC;QACxB;MACF,KAAK,GAAG;QACNhB,OAAO,CAACgB,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACEhB,OAAO,CAACgB,KAAK,CAAC,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG,KAAI,SAASG,MAAM,GAAG,CAAC;IAClD;EACF,CAAC,MAAM,IAAIX,KAAK,CAACf,OAAO,EAAE;IACxBD,OAAO,CAACgB,KAAK,CAAC,cAAc,CAAC;EAC/B,CAAC,MAAM;IACLhB,OAAO,CAACgB,KAAK,CAAC,QAAQ,CAAC;EACzB;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAef,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}