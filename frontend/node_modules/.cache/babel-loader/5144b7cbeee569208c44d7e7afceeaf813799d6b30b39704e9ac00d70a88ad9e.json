{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport K<PERSON><PERSON><PERSON>DE from \"rc-util/es/KeyCode\";\nimport React from 'react';\nvar defaultPageSizeOptions = [10, 20, 50, 100];\nvar Options = function Options(props) {\n  var _props$pageSizeOption = props.pageSizeOptions,\n    pageSizeOptions = _props$pageSizeOption === void 0 ? defaultPageSizeOptions : _props$pageSizeOption,\n    locale = props.locale,\n    changeSize = props.changeSize,\n    pageSize = props.pageSize,\n    goButton = props.goButton,\n    quickGo = props.quickGo,\n    rootPrefixCls = props.rootPrefixCls,\n    disabled = props.disabled,\n    buildOptionText = props.buildOptionText,\n    showSizeChanger = props.showSizeChanger,\n    sizeChangerRender = props.sizeChangerRender;\n  var _React$useState = React.useState(''),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    goInputText = _React$useState2[0],\n    setGoInputText = _React$useState2[1];\n  var getValidValue = function getValidValue() {\n    return !goInputText || Number.isNaN(goInputText) ? undefined : Number(goInputText);\n  };\n  var mergeBuildOptionText = typeof buildOptionText === 'function' ? buildOptionText : function (value) {\n    return \"\".concat(value, \" \").concat(locale.items_per_page);\n  };\n  var handleChange = function handleChange(e) {\n    setGoInputText(e.target.value);\n  };\n  var handleBlur = function handleBlur(e) {\n    if (goButton || goInputText === '') {\n      return;\n    }\n    setGoInputText('');\n    if (e.relatedTarget && (e.relatedTarget.className.indexOf(\"\".concat(rootPrefixCls, \"-item-link\")) >= 0 || e.relatedTarget.className.indexOf(\"\".concat(rootPrefixCls, \"-item\")) >= 0)) {\n      return;\n    }\n    quickGo === null || quickGo === void 0 || quickGo(getValidValue());\n  };\n  var go = function go(e) {\n    if (goInputText === '') {\n      return;\n    }\n    if (e.keyCode === KEYCODE.ENTER || e.type === 'click') {\n      setGoInputText('');\n      quickGo === null || quickGo === void 0 || quickGo(getValidValue());\n    }\n  };\n  var getPageSizeOptions = function getPageSizeOptions() {\n    if (pageSizeOptions.some(function (option) {\n      return option.toString() === pageSize.toString();\n    })) {\n      return pageSizeOptions;\n    }\n    return pageSizeOptions.concat([pageSize]).sort(function (a, b) {\n      var numberA = Number.isNaN(Number(a)) ? 0 : Number(a);\n      var numberB = Number.isNaN(Number(b)) ? 0 : Number(b);\n      return numberA - numberB;\n    });\n  };\n  // ============== cls ==============\n  var prefixCls = \"\".concat(rootPrefixCls, \"-options\");\n\n  // ============== render ==============\n\n  if (!showSizeChanger && !quickGo) {\n    return null;\n  }\n  var changeSelect = null;\n  var goInput = null;\n  var gotoButton = null;\n\n  // >>>>> Size Changer\n  if (showSizeChanger && sizeChangerRender) {\n    changeSelect = sizeChangerRender({\n      disabled: disabled,\n      size: pageSize,\n      onSizeChange: function onSizeChange(nextValue) {\n        changeSize === null || changeSize === void 0 || changeSize(Number(nextValue));\n      },\n      'aria-label': locale.page_size,\n      className: \"\".concat(prefixCls, \"-size-changer\"),\n      options: getPageSizeOptions().map(function (opt) {\n        return {\n          label: mergeBuildOptionText(opt),\n          value: opt\n        };\n      })\n    });\n  }\n\n  // >>>>> Quick Go\n  if (quickGo) {\n    if (goButton) {\n      gotoButton = typeof goButton === 'boolean' ? /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        onClick: go,\n        onKeyUp: go,\n        disabled: disabled,\n        className: \"\".concat(prefixCls, \"-quick-jumper-button\")\n      }, locale.jump_to_confirm) : /*#__PURE__*/React.createElement(\"span\", {\n        onClick: go,\n        onKeyUp: go\n      }, goButton);\n    }\n    goInput = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-quick-jumper\")\n    }, locale.jump_to, /*#__PURE__*/React.createElement(\"input\", {\n      disabled: disabled,\n      type: \"text\",\n      value: goInputText,\n      onChange: handleChange,\n      onKeyUp: go,\n      onBlur: handleBlur,\n      \"aria-label\": locale.page\n    }), locale.page, gotoButton);\n  }\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: prefixCls\n  }, changeSelect, goInput);\n};\nif (process.env.NODE_ENV !== 'production') {\n  Options.displayName = 'Options';\n}\nexport default Options;", "map": {"version": 3, "names": ["_slicedToArray", "KEYCODE", "React", "defaultPageSizeOptions", "Options", "props", "_props$pageSizeOption", "pageSizeOptions", "locale", "changeSize", "pageSize", "goButton", "quickGo", "rootPrefixCls", "disabled", "buildOptionText", "showSizeChanger", "sizeChangerRender", "_React$useState", "useState", "_React$useState2", "goInputText", "setGoInputText", "getValidValue", "Number", "isNaN", "undefined", "mergeBuildOptionText", "value", "concat", "items_per_page", "handleChange", "e", "target", "handleBlur", "relatedTarget", "className", "indexOf", "go", "keyCode", "ENTER", "type", "getPageSizeOptions", "some", "option", "toString", "sort", "a", "b", "numberA", "numberB", "prefixCls", "changeSelect", "goInput", "gotoButton", "size", "onSizeChange", "nextValue", "page_size", "options", "map", "opt", "label", "createElement", "onClick", "onKeyUp", "jump_to_confirm", "jump_to", "onChange", "onBlur", "page", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-pagination/es/Options.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport K<PERSON><PERSON><PERSON>DE from \"rc-util/es/KeyCode\";\nimport React from 'react';\nvar defaultPageSizeOptions = [10, 20, 50, 100];\nvar Options = function Options(props) {\n  var _props$pageSizeOption = props.pageSizeOptions,\n    pageSizeOptions = _props$pageSizeOption === void 0 ? defaultPageSizeOptions : _props$pageSizeOption,\n    locale = props.locale,\n    changeSize = props.changeSize,\n    pageSize = props.pageSize,\n    goButton = props.goButton,\n    quickGo = props.quickGo,\n    rootPrefixCls = props.rootPrefixCls,\n    disabled = props.disabled,\n    buildOptionText = props.buildOptionText,\n    showSizeChanger = props.showSizeChanger,\n    sizeChangerRender = props.sizeChangerRender;\n  var _React$useState = React.useState(''),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    goInputText = _React$useState2[0],\n    setGoInputText = _React$useState2[1];\n  var getValidValue = function getValidValue() {\n    return !goInputText || Number.isNaN(goInputText) ? undefined : Number(goInputText);\n  };\n  var mergeBuildOptionText = typeof buildOptionText === 'function' ? buildOptionText : function (value) {\n    return \"\".concat(value, \" \").concat(locale.items_per_page);\n  };\n  var handleChange = function handleChange(e) {\n    setGoInputText(e.target.value);\n  };\n  var handleBlur = function handleBlur(e) {\n    if (goButton || goInputText === '') {\n      return;\n    }\n    setGoInputText('');\n    if (e.relatedTarget && (e.relatedTarget.className.indexOf(\"\".concat(rootPrefixCls, \"-item-link\")) >= 0 || e.relatedTarget.className.indexOf(\"\".concat(rootPrefixCls, \"-item\")) >= 0)) {\n      return;\n    }\n    quickGo === null || quickGo === void 0 || quickGo(getValidValue());\n  };\n  var go = function go(e) {\n    if (goInputText === '') {\n      return;\n    }\n    if (e.keyCode === KEYCODE.ENTER || e.type === 'click') {\n      setGoInputText('');\n      quickGo === null || quickGo === void 0 || quickGo(getValidValue());\n    }\n  };\n  var getPageSizeOptions = function getPageSizeOptions() {\n    if (pageSizeOptions.some(function (option) {\n      return option.toString() === pageSize.toString();\n    })) {\n      return pageSizeOptions;\n    }\n    return pageSizeOptions.concat([pageSize]).sort(function (a, b) {\n      var numberA = Number.isNaN(Number(a)) ? 0 : Number(a);\n      var numberB = Number.isNaN(Number(b)) ? 0 : Number(b);\n      return numberA - numberB;\n    });\n  };\n  // ============== cls ==============\n  var prefixCls = \"\".concat(rootPrefixCls, \"-options\");\n\n  // ============== render ==============\n\n  if (!showSizeChanger && !quickGo) {\n    return null;\n  }\n  var changeSelect = null;\n  var goInput = null;\n  var gotoButton = null;\n\n  // >>>>> Size Changer\n  if (showSizeChanger && sizeChangerRender) {\n    changeSelect = sizeChangerRender({\n      disabled: disabled,\n      size: pageSize,\n      onSizeChange: function onSizeChange(nextValue) {\n        changeSize === null || changeSize === void 0 || changeSize(Number(nextValue));\n      },\n      'aria-label': locale.page_size,\n      className: \"\".concat(prefixCls, \"-size-changer\"),\n      options: getPageSizeOptions().map(function (opt) {\n        return {\n          label: mergeBuildOptionText(opt),\n          value: opt\n        };\n      })\n    });\n  }\n\n  // >>>>> Quick Go\n  if (quickGo) {\n    if (goButton) {\n      gotoButton = typeof goButton === 'boolean' ? /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        onClick: go,\n        onKeyUp: go,\n        disabled: disabled,\n        className: \"\".concat(prefixCls, \"-quick-jumper-button\")\n      }, locale.jump_to_confirm) : /*#__PURE__*/React.createElement(\"span\", {\n        onClick: go,\n        onKeyUp: go\n      }, goButton);\n    }\n    goInput = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-quick-jumper\")\n    }, locale.jump_to, /*#__PURE__*/React.createElement(\"input\", {\n      disabled: disabled,\n      type: \"text\",\n      value: goInputText,\n      onChange: handleChange,\n      onKeyUp: go,\n      onBlur: handleBlur,\n      \"aria-label\": locale.page\n    }), locale.page, gotoButton);\n  }\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: prefixCls\n  }, changeSelect, goInput);\n};\nif (process.env.NODE_ENV !== 'production') {\n  Options.displayName = 'Options';\n}\nexport default Options;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,sBAAsB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAC9C,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,qBAAqB,GAAGD,KAAK,CAACE,eAAe;IAC/CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGH,sBAAsB,GAAGG,qBAAqB;IACnGE,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,eAAe,GAAGV,KAAK,CAACU,eAAe;IACvCC,eAAe,GAAGX,KAAK,CAACW,eAAe;IACvCC,iBAAiB,GAAGZ,KAAK,CAACY,iBAAiB;EAC7C,IAAIC,eAAe,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGpB,cAAc,CAACkB,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,OAAO,CAACF,WAAW,IAAIG,MAAM,CAACC,KAAK,CAACJ,WAAW,CAAC,GAAGK,SAAS,GAAGF,MAAM,CAACH,WAAW,CAAC;EACpF,CAAC;EACD,IAAIM,oBAAoB,GAAG,OAAOZ,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUa,KAAK,EAAE;IACpG,OAAO,EAAE,CAACC,MAAM,CAACD,KAAK,EAAE,GAAG,CAAC,CAACC,MAAM,CAACrB,MAAM,CAACsB,cAAc,CAAC;EAC5D,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,CAAC,EAAE;IAC1CV,cAAc,CAACU,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAChC,CAAC;EACD,IAAIM,UAAU,GAAG,SAASA,UAAUA,CAACF,CAAC,EAAE;IACtC,IAAIrB,QAAQ,IAAIU,WAAW,KAAK,EAAE,EAAE;MAClC;IACF;IACAC,cAAc,CAAC,EAAE,CAAC;IAClB,IAAIU,CAAC,CAACG,aAAa,KAAKH,CAAC,CAACG,aAAa,CAACC,SAAS,CAACC,OAAO,CAAC,EAAE,CAACR,MAAM,CAAChB,aAAa,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAImB,CAAC,CAACG,aAAa,CAACC,SAAS,CAACC,OAAO,CAAC,EAAE,CAACR,MAAM,CAAChB,aAAa,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;MACpL;IACF;IACAD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACW,aAAa,CAAC,CAAC,CAAC;EACpE,CAAC;EACD,IAAIe,EAAE,GAAG,SAASA,EAAEA,CAACN,CAAC,EAAE;IACtB,IAAIX,WAAW,KAAK,EAAE,EAAE;MACtB;IACF;IACA,IAAIW,CAAC,CAACO,OAAO,KAAKtC,OAAO,CAACuC,KAAK,IAAIR,CAAC,CAACS,IAAI,KAAK,OAAO,EAAE;MACrDnB,cAAc,CAAC,EAAE,CAAC;MAClBV,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACW,aAAa,CAAC,CAAC,CAAC;IACpE;EACF,CAAC;EACD,IAAImB,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAInC,eAAe,CAACoC,IAAI,CAAC,UAAUC,MAAM,EAAE;MACzC,OAAOA,MAAM,CAACC,QAAQ,CAAC,CAAC,KAAKnC,QAAQ,CAACmC,QAAQ,CAAC,CAAC;IAClD,CAAC,CAAC,EAAE;MACF,OAAOtC,eAAe;IACxB;IACA,OAAOA,eAAe,CAACsB,MAAM,CAAC,CAACnB,QAAQ,CAAC,CAAC,CAACoC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC7D,IAAIC,OAAO,GAAGzB,MAAM,CAACC,KAAK,CAACD,MAAM,CAACuB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGvB,MAAM,CAACuB,CAAC,CAAC;MACrD,IAAIG,OAAO,GAAG1B,MAAM,CAACC,KAAK,CAACD,MAAM,CAACwB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGxB,MAAM,CAACwB,CAAC,CAAC;MACrD,OAAOC,OAAO,GAAGC,OAAO;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD;EACA,IAAIC,SAAS,GAAG,EAAE,CAACtB,MAAM,CAAChB,aAAa,EAAE,UAAU,CAAC;;EAEpD;;EAEA,IAAI,CAACG,eAAe,IAAI,CAACJ,OAAO,EAAE;IAChC,OAAO,IAAI;EACb;EACA,IAAIwC,YAAY,GAAG,IAAI;EACvB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,UAAU,GAAG,IAAI;;EAErB;EACA,IAAItC,eAAe,IAAIC,iBAAiB,EAAE;IACxCmC,YAAY,GAAGnC,iBAAiB,CAAC;MAC/BH,QAAQ,EAAEA,QAAQ;MAClByC,IAAI,EAAE7C,QAAQ;MACd8C,YAAY,EAAE,SAASA,YAAYA,CAACC,SAAS,EAAE;QAC7ChD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACe,MAAM,CAACiC,SAAS,CAAC,CAAC;MAC/E,CAAC;MACD,YAAY,EAAEjD,MAAM,CAACkD,SAAS;MAC9BtB,SAAS,EAAE,EAAE,CAACP,MAAM,CAACsB,SAAS,EAAE,eAAe,CAAC;MAChDQ,OAAO,EAAEjB,kBAAkB,CAAC,CAAC,CAACkB,GAAG,CAAC,UAAUC,GAAG,EAAE;QAC/C,OAAO;UACLC,KAAK,EAAEnC,oBAAoB,CAACkC,GAAG,CAAC;UAChCjC,KAAK,EAAEiC;QACT,CAAC;MACH,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIjD,OAAO,EAAE;IACX,IAAID,QAAQ,EAAE;MACZ2C,UAAU,GAAG,OAAO3C,QAAQ,KAAK,SAAS,GAAG,aAAaT,KAAK,CAAC6D,aAAa,CAAC,QAAQ,EAAE;QACtFtB,IAAI,EAAE,QAAQ;QACduB,OAAO,EAAE1B,EAAE;QACX2B,OAAO,EAAE3B,EAAE;QACXxB,QAAQ,EAAEA,QAAQ;QAClBsB,SAAS,EAAE,EAAE,CAACP,MAAM,CAACsB,SAAS,EAAE,sBAAsB;MACxD,CAAC,EAAE3C,MAAM,CAAC0D,eAAe,CAAC,GAAG,aAAahE,KAAK,CAAC6D,aAAa,CAAC,MAAM,EAAE;QACpEC,OAAO,EAAE1B,EAAE;QACX2B,OAAO,EAAE3B;MACX,CAAC,EAAE3B,QAAQ,CAAC;IACd;IACA0C,OAAO,GAAG,aAAanD,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;MAChD3B,SAAS,EAAE,EAAE,CAACP,MAAM,CAACsB,SAAS,EAAE,eAAe;IACjD,CAAC,EAAE3C,MAAM,CAAC2D,OAAO,EAAE,aAAajE,KAAK,CAAC6D,aAAa,CAAC,OAAO,EAAE;MAC3DjD,QAAQ,EAAEA,QAAQ;MAClB2B,IAAI,EAAE,MAAM;MACZb,KAAK,EAAEP,WAAW;MAClB+C,QAAQ,EAAErC,YAAY;MACtBkC,OAAO,EAAE3B,EAAE;MACX+B,MAAM,EAAEnC,UAAU;MAClB,YAAY,EAAE1B,MAAM,CAAC8D;IACvB,CAAC,CAAC,EAAE9D,MAAM,CAAC8D,IAAI,EAAEhB,UAAU,CAAC;EAC9B;EACA,OAAO,aAAapD,KAAK,CAAC6D,aAAa,CAAC,IAAI,EAAE;IAC5C3B,SAAS,EAAEe;EACb,CAAC,EAAEC,YAAY,EAAEC,OAAO,CAAC;AAC3B,CAAC;AACD,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrE,OAAO,CAACsE,WAAW,GAAG,SAAS;AACjC;AACA,eAAetE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}