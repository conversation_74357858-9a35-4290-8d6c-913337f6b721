export function create({
  colorDefault,
  colorBlack,
  colorWhite,
  colorStroke,
  colorBackground,
  padding1,
  padding2,
  padding3,
  alpha90,
  alpha65,
  alpha45,
  alpha25,
  alpha10,
  category10,
  category20,
  sizeDefault = 1,
  padding = 'auto',
  margin = 16,
}) {
  return {
    padding,
    margin,
    size: sizeDefault,
    color: colorDefault,
    category10,
    category20,
    enter: {
      duration: 300,
      fill: 'both',
      delay: 0,
    },
    update: {
      duration: 300,
      fill: 'both',
      delay: 0,
    },
    exit: {
      duration: 300,
      fill: 'both',
      delay: 0,
    },
    view: {
      viewFill: colorBackground,
      plotFill: 'transparent',
      mainFill: 'transparent',
      contentFill: 'transparent',
    },
    line: {
      line: {
        fill: '',
        strokeOpacity: 1,
        lineWidth: 1,
        lineCap: 'round',
      },
    },
    point: {
      point: {
        r: 3,
        fillOpacity: 0.95,
        lineWidth: 0,
      },
      hollow: {
        r: 3,
        strokeOpacity: 0.95,
        lineWidth: 1,
      },
      plus: {
        r: 3,
        strokeOpacity: 0.95,
        lineWidth: 3,
      },
      diamond: {
        r: 3,
        strokeOpacity: 0.95,
        lineWidth: 1,
      },
    },
    interval: {
      rect: {
        fillOpacity: 0.95,
      },
      hollow: {
        fill: '',
        strokeOpacity: 1,
        lineWidth: 2,
      },
    },
    area: {
      area: {
        fillOpacity: 0.85,
        lineWidth: 0,
      },
    },
    polygon: {
      polygon: {
        fillOpacity: 0.95,
      },
    },
    cell: {
      cell: {
        fillOpacity: 0.95,
      },
      hollow: {
        fill: '',
        strokeOpacity: 1,
        lineWidth: 2,
      },
    },
    rect: {
      rect: {
        fillOpacity: 0.95,
      },
      hollow: {
        fill: '',
        strokeOpacity: 1,
        lineWidth: 2,
      },
    },
    link: {
      link: {
        fill: '',
        strokeOpacity: 1,
      },
    },
    vector: {
      vector: {
        fillOpacity: 1,
      },
    },
    box: {
      box: {
        fillOpacity: 0.95,
        stroke: colorBlack,
        lineWidth: 1,
      },
    },
    text: {
      text: {
        fill: '#1D2129',
        fontSize: 12,
        lineWidth: 0,
        connectorStroke: colorStroke,
        connectorStrokeOpacity: 0.45,
        connectorLineWidth: 1,
        backgroundFill: colorStroke,
        backgroundFillOpacity: 0.15,
        backgroundPadding: [2, 4],
        startMarkerSymbol: 'circle',
        startMarkerSize: 4,
        endMarkerSymbol: 'circle',
        endMarkerSize: 4,
      },
      badge: {
        fill: '#1D2129',
        fillOpacity: 0.65,
        lineWidth: 0,
        fontSize: 10,
        textAlign: 'center',
        textBaseline: 'middle',
        markerFill: colorStroke,
        markerFillOpacity: 0.25,
        markerStrokeOpacity: 0,
      },
    },
    lineX: {
      line: {
        stroke: colorStroke,
        strokeOpacity: 0.45,
        lineWidth: 1,
      },
    },
    lineY: {
      line: {
        stroke: colorStroke,
        strokeOpacity: 0.45,
        lineWidth: 1,
      },
    },
    rangeX: {
      range: {
        fill: colorStroke,
        fillOpacity: 0.15,
        lineWidth: 0,
      },
    },
    rangeY: {
      range: {
        fill: colorStroke,
        fillOpacity: 0.15,
        lineWidth: 0,
      },
    },
    connector: {
      connector: {
        stroke: colorStroke,
        strokeOpacity: 0.45,
        lineWidth: 1,
        connectLength1: 12,
        endMarker: true,
        endMarkerSize: 6,
        endMarkerFill: colorStroke,
        endMarkerFillOpacity: 0.95,
      },
    },
    axis: {
      arrow: false,
      gridLineDash: [3, 4],
      gridLineWidth: 0.5,
      gridStroke: colorBlack,
      gridStrokeOpacity: alpha10,
      labelAlign: 'horizontal',
      labelFill: colorBlack,
      labelOpacity: alpha45,
      labelFontSize: 12,
      labelFontWeight: 'normal',
      labelSpacing: padding1, // spacing between label and it's tick
      line: false,
      lineLineWidth: 0.5,
      lineStroke: colorBlack,
      lineStrokeOpacity: alpha45,
      tickLength: 4,
      tickLineWidth: 1,
      tickStroke: colorBlack,
      tickOpacity: alpha45,
      titleFill: colorBlack,
      titleOpacity: alpha90,
      titleFontSize: 12,
      titleFontWeight: 'normal',
      titleSpacing: 12,
      titleTransformOrigin: 'center',
      lineArrowOffset: 6,
      lineArrowSize: 6,
    },
    axisTop: {
      gridDirection: 'positive',
      labelDirection: 'negative',
      tickDirection: 'negative',
      titlePosition: 'top',
      titleSpacing: 12,
      labelSpacing: 4,
      titleTextBaseline: 'middle',
    },
    axisBottom: {
      gridDirection: 'negative',
      labelDirection: 'positive',
      tickDirection: 'positive',
      titlePosition: 'bottom',
      titleSpacing: 12,
      labelSpacing: 4,
      titleTextBaseline: 'bottom',
      titleTransform: 'translate(0, 8)',
    },
    axisLeft: {
      gridDirection: 'positive',
      labelDirection: 'negative',
      labelSpacing: 4,
      tickDirection: 'negative',
      titlePosition: 'left',
      titleSpacing: 12,
      titleTextBaseline: 'middle',
      titleDirection: 'vertical',
      titleTransform: 'rotate(-90) translate(0, -8)',
      titleTransformOrigin: 'center',
    },
    axisRight: {
      gridDirection: 'negative',
      labelDirection: 'positive',
      labelSpacing: 4,
      tickDirection: 'positive',
      titlePosition: 'right',
      titleSpacing: 12,
      titleTextBaseline: 'top',
      titleDirection: 'vertical',
      titleTransformOrigin: 'center',
    },
    axisLinear: {
      girdClosed: true,
      gridConnect: 'arc',
      gridDirection: 'negative',
      gridType: 'surround',
      titlePosition: 'top',
      titleSpacing: 0,
    },
    axisArc: {
      title: false,
      titlePosition: 'inner',
      line: false,
      tick: true,
      labelSpacing: 4,
    },
    axisRadar: {
      girdClosed: true,
      gridStrokeOpacity: 0.3,
      gridType: 'surround',
      tick: false,
      titlePosition: 'start',
    },
    legendCategory: {
      backgroundFill: 'transparent',
      itemBackgroundFill: 'transparent',
      itemLabelFill: colorBlack,
      itemLabelFillOpacity: alpha90,
      itemLabelFontSize: 12,
      itemLabelFontWeight: 'normal',
      itemMarkerFillOpacity: 1,
      itemMarkerSize: 8,
      itemSpacing: [padding1, padding1],
      itemValueFill: colorBlack,
      itemValueFillOpacity: 0.65,
      itemValueFontSize: 12,
      itemValueFontWeight: 'normal',
      navButtonFill: colorBlack,
      navButtonFillOpacity: 0.65,
      navPageNumFill: colorBlack,
      navPageNumFillOpacity: 0.45,
      navPageNumFontSize: 12,
      padding: 8,
      title: false,
      titleFill: colorBlack,
      titleFillOpacity: 0.65,
      titleFontSize: 12,
      titleFontWeight: 'normal',
      titleSpacing: 4,
      tickStroke: colorBlack,
      tickStrokeOpacity: 0.25,
      rowPadding: padding1,
      colPadding: padding2,
      maxRows: 3,
      maxCols: 3,
    },
    legendContinuous: {
      handleHeight: 12,
      handleLabelFill: colorBlack,
      handleLabelFillOpacity: alpha45,
      handleLabelFontSize: 12,
      handleLabelFontWeight: 'normal',
      handleMarkerFill: colorBlack,
      handleMarkerFillOpacity: 0.6,
      handleMarkerLineWidth: 1,
      handleMarkerStroke: colorBlack,
      handleMarkerStrokeOpacity: 0.25,
      handleWidth: 10,
      labelFill: colorBlack,
      labelFillOpacity: alpha45,
      labelFontSize: 12,
      labelFontWeight: 'normal',
      labelSpacing: 3,
      tick: true,
      tickLength: 12,
      ribbonSize: 12,
      ribbonFill: '#aaa',
      handle: true,
      handleLabel: false,
      handleShape: 'slider',
      handleIconSize: 12 / 1.8,
      indicator: false,
      titleFontSize: 12,
      titleSpacing: 4,
      titleFontWeight: 'normal',
      titleFillOpacity: alpha90,
      tickStroke: colorBlack,
      tickStrokeOpacity: alpha45,
    },
    label: {
      fill: colorBlack,
      fillOpacity: 0.65,
      fontSize: 12,
      fontWeight: 'normal',
      stroke: undefined,
      offset: 12,
      connectorStroke: colorBlack,
      connectorStrokeOpacity: 0.45,
      connectorLineWidth: 1,
      connectorLength: 12,
      connectorLength2: 8,
      connectorDistance: 4,
    },
    innerLabel: {
      fill: colorWhite,
      fontSize: 12,
      fillOpacity: 0.85,
      fontWeight: 'normal',
      stroke: undefined,
      offset: 0,
    },
    htmlLabel: {
      fontSize: 12,
      opacity: 0.65,
      color: colorBlack,
      fontWeight: 'normal',
    },
    slider: {
      trackSize: 16,
      trackFill: colorStroke,
      trackFillOpacity: 1,
      selectionFill: colorDefault,
      selectionFillOpacity: 0.15,
      handleIconSize: 10,
      handleIconFill: '#f7f7f7',
      handleIconFillOpacity: 1,
      handleIconStroke: colorBlack,
      handleIconStrokeOpacity: 0.25,
      handleIconLineWidth: 1,
      handleIconRadius: 2,
      handleLabelFill: colorBlack,
      handleLabelFillOpacity: 0.45,
      handleLabelFontSize: 12,
      handleLabelFontWeight: 'normal',
    },
    scrollbar: {
      padding: [0, 0, 0, 0],
      trackSize: 6,
      isRound: true,
      slidable: true,
      scrollable: true,
      trackFill: '#e5e5e5',
      trackFillOpacity: 0,
      thumbFill: '#000',
      thumbFillOpacity: 0.15,
      thumbHighlightedFillOpacity: 0.2,
    },
    title: {
      spacing: 8,
      titleFill: colorBlack,
      titleFillOpacity: alpha90,
      titleFontSize: 16,
      titleFontWeight: 'bold',
      titleTextBaseline: 'top',
      subtitleFill: colorBlack,
      subtitleFillOpacity: alpha65,
      subtitleFontSize: 12,
      subtitleFontWeight: 'normal',
      subtitleTextBaseline: 'top',
    },
    tooltip: {
      css: {
        '.g2-tooltip': {
          'font-family': 'sans-serif',
        },
      },
    },
  };
}
