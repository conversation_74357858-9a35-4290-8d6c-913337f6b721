import type { Padding, STDPadding } from '../types/padding';
/**
 * <zh/> 解析 padding
 *
 * <en/> parse padding
 * @param padding - <zh/> padding | <en/> padding
 * @returns <zh/> 标准 padding | <en/> standard padding
 */
export declare function parsePadding(padding?: Padding): STDPadding;
/**
 * <zh/> 获取在垂直方向上的 padding
 *
 * <en/> get vertical padding
 * @param padding - <zh/> padding | <en/> padding
 * @returns <zh/> 垂直方向上的 padding | <en/> vertical padding
 */
export declare function getVerticalPadding(padding?: Padding): number;
/**
 * <zh/> 获取在水平方向上的 padding
 *
 * <en/> get horizontal padding
 * @param padding - <zh/> padding | <en/> padding
 * @returns <zh/> 水平方向上的 padding | <en/> horizontal padding
 */
export declare function getHorizontalPadding(padding?: Padding): number;
