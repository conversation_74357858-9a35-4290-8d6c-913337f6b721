import type { TransformArray } from '@antv/g';
/**
 * <zh/> 从 transform 字符串中替换 translate 部分
 *
 * <en/> replace the translate part from the transform string
 * @param x - <zh/> x | <en/> x
 * @param y - <zh/> y | <en/> y
 * @param z - <zh/> z | <en/> z
 * @param transform - <zh/> transform 字符串 | <en/> transform string
 * @returns <zh/> 替换后的 transform 字符串，返回 null 表示无需替换 | <en/> the replaced transform string, return null means no need to replace
 */
export declare function replaceTranslateInTransform(x: number, y: number, z?: number, transform?: string | TransformArray): string | TransformArray | null;
