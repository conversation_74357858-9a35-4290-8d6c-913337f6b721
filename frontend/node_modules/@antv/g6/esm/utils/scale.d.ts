/**
 * <zh/> 将一个值从一个范围线性映射到另一个范围
 *
 * <en/> Linearly maps a value from one range to another range
 * @param value - <zh/> 需要映射的值 | <en/> The value to be mapped
 * @param domain - <zh/> 输入值的范围 [最小值, 最大值] | <en/> The input range [min, max]
 * @param range - <zh/> 输出值的范围 [最小值, 最大值] | <en/> The output range [min, max]
 * @returns <zh/> 映射后的值 | <en/> The mapped value
 */
export declare const linear: (value: number, domain: [number, number], range: [number, number]) => number;
/**
 * <zh/> 将一个值从一个范围对数映射到另一个范围
 *
 * <en/> Logarithmically maps a value from one range to another range
 * @param value - <zh/> 需要映射的值 | <en/> The value to be mapped
 * @param domain - <zh/> 输入值的范围 [最小值, 最大值] | <en/> The input range [min, max]
 * @param range - <zh/> 输出值的范围 [最小值, 最大值] | <en/> The output range [min, max]
 * @returns <zh/> 映射后的值 | <en/> The mapped value
 */
export declare const log: (value: number, domain: [number, number], range: [number, number]) => number;
/**
 * <zh/> 将一个值从一个范围幂映射到另一个范围
 *
 * <en/> Maps a value from one range to another range
 * @param value - <zh/> 需要映射的值 | <en/> The value to be mapped
 * @param domain - <zh/> 输入值的范围 [最小值, 最大值] | <en/> The input range [min, max]
 * @param range - <zh/> 输出值的范围 [最小值, 最大值] | <en/> The output range [min, max]
 * @param exponent - <zh/> 幂指数 | <en/> The exponent
 * @returns <zh/> 映射后的值 | <en/> The mapped value
 */
export declare const pow: (value: number, domain: [number, number], range: [number, number], exponent?: number) => number;
/**
 * <zh/> 将一个值从一个范围平方根映射到另一个范围
 *
 * <en/> Maps a value from one range to another range using square root
 * @param value - <zh/> 需要映射的值 | <en/> The value to be mapped
 * @param domain - <zh/> 输入值的范围 [最小值, 最大值] | <en/> The input range [min, max]
 * @param range - <zh/> 输出值的范围 [最小值, 最大值] | <en/> The output range [min, max]
 * @returns <zh/> 映射后的值 | <en/> The mapped value
 */
export declare const sqrt: (value: number, domain: [number, number], range: [number, number]) => number;
