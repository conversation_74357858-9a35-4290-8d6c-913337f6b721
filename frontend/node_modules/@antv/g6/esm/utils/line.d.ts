import type { Point } from '../types';
export type LineSegment = [Point, Point];
/**
 * <zh/> 判断两条线段是否平行
 *
 * <en/> Judge whether two line segments are parallel
 * @param l1 - <zh/> 第一条线段 | <en/> the first line segment
 * @param l2 - <zh/> 第二条线段 | <en/> the second line segment
 * @returns <zh/> 是否平行 | <en/> whether parallel or not
 */
export declare function isLinesParallel(l1: LineSegment, l2: LineSegment): boolean;
/**
 * <zh/> 获取两条线段的交点
 *
 * <en/> Get the intersection of two line segments
 * @param l1 - <zh/> 第一条线段 | <en/> the first line segment
 * @param l2 - <zh/> 第二条线段 | <en/> the second line segment
 * @param extended - <zh/> 是否包含延长线上的交点 | <en/> whether to include the intersection on the extension line
 * @returns <zh/> 交点 | <en/> intersection
 */
export declare function getLinesIntersection(l1: LineSegment, l2: LineSegment, extended?: boolean): Point | undefined;
