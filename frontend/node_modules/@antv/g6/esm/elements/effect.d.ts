import type { Element } from '../types';
/**
 * <zh/> 判定给定样式是否与上一次的样式相同
 *
 * <en/> Determine whether the given style are the same as the previous ones
 * @param target - <zh/> 目标元素 | <en/> Target element
 * @param key - <zh/> 缓存 key | <en/> Cache key
 * @param style - <zh/> 样式属性 | <en/> Style attribute
 * @returns <zh/> 是否执行函数 | <en/> Whether to execute the function
 * @deprecated <zh/> 该方法已废弃，并不能显著提升性能 | <en/> This method is deprecated and does not significantly improve performance
 */
export declare function effect<T extends false | Record<string, any>>(target: Element, key: string, style: T): boolean;
