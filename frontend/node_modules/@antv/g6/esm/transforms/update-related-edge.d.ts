import type { DrawContext } from '../runtime/element';
import { BaseTransform } from './base-transform';
import type { DrawData } from './types';
/**
 * 如果更新了节点 / combo，需要更新连接的边
 * If the node / combo is updated, the connected edge and the combo it is in need to be updated
 */
export declare class UpdateRelatedEdge extends BaseTransform {
    beforeDraw(input: DrawData, context: DrawContext): DrawData;
}
