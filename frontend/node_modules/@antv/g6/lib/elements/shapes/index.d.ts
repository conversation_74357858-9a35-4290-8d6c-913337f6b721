/**
 * <zh/> 图形组件，用于构建复合元素
 *
 * <en/> Shape components, used to build composite elements
 */
export { Badge } from './badge';
export { BaseShape } from './base-shape';
export { Contour } from './contour';
export { Icon } from './icon';
export { Image } from './image';
export { Label } from './label';
export type { BadgeStyleProps } from './badge';
export type { BaseShapeStyleProps } from './base-shape';
export type { ContourStyleProps } from './contour';
export type { IconStyleProps } from './icon';
export type { ImageStyleProps } from './image';
export type { LabelStyleProps } from './label';
export type { PolygonStyleProps } from './polygon';
