import request from '../utils/request';
import { LoginParams, LoginResponse, User } from '../types';
import { md5 } from 'js-md5';

export const authService = {
  // 用户登录
  async login(params: LoginParams): Promise<LoginResponse> {
    const { username, password, role } = params;

    // 密码MD5加密
    const encryptedPassword = md5(password);

    let response;
    let loginUrl;
    let loginData;

    if (role === '管理员') {
      // 管理员登录 - 使用统一认证接口
      loginUrl = '/api/auth/login';
      loginData = {
        username,
        password: encryptedPassword,
        role: role,
      };
    } else {
      // 普通用户登录
      loginUrl = '/yonghu/login';
      loginData = {
        yonghuming: username,
        mima: encryptedPassword,
      };
    }

    response = await request.post(loginUrl, loginData);

    return {
      token: response.token,
      user: {
        id: response.userId || response.user?.id || 1,
        username: username,
        role: role || '用户',
        name: response.user?.username || username, // 使用返回的用户信息
      },
    };
  },

  // 用户注册（普通用户）
  async register(params: {
    username: string;
    password: string;
    name: string;
    phone?: string;
    email?: string;
    role?: string;
  }): Promise<void> {
    const { username, password, name, phone } = params;
    const encryptedPassword = md5(password);

    // 调用普通用户注册接口，使用后端字段名
    await request.post('/yonghu/register', {
      yonghuming: username,  // 用户名
      mima: encryptedPassword,  // 密码
      xingming: name,  // 姓名
      shouji: phone || '',  // 手机号
    });
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return await request.get('/api/users/current');
  },

  // 更新用户信息
  async updateProfile(params: Partial<User>): Promise<void> {
    await request.put('/api/users/update', params);
  },

  // 修改密码
  async changePassword(params: {
    oldPassword: string;
    newPassword: string;
  }): Promise<void> {
    const { oldPassword, newPassword } = params;

    await request.put('/api/users/change-password', {
      oldPassword: md5(oldPassword),
      newPassword: md5(newPassword),
    });
  },

  // 退出登录
  async logout(): Promise<void> {
    await request.post('/api/auth/logout');
  },
};
