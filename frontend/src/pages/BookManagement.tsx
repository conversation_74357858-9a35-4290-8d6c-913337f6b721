import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Popconfirm,
  Card,
  Image,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Divider,
  Upload,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  BookOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined,
  UploadOutlined,
  StarOutlined,
  HeartOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { Book, BookCategory, PageParams } from '../types';
import { bookService } from '../services/bookService';
import PermissionWrapper, { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';
import ActionFeedback from '../components/Common/ActionFeedback';

const { Option } = Select;
const { TextArea, Search } = Input;
const { Title, Text } = Typography;

const BookManagement: React.FC = () => {
  const [books, setBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<BookCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [viewingBook, setViewingBook] = useState<Book | null>(null);
  const [statistics, setStatistics] = useState({
    total: 0,
    available: 0,
    borrowed: 0,
    maintenance: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    name: '',
    author: '',
    categoryId: undefined as number | undefined,
  });
  const [form] = Form.useForm();
  const { hasPermission } = usePermission();

  useEffect(() => {
    fetchBooks();
    fetchCategories();
  }, [pagination.current, pagination.pageSize]);

  const fetchBooks = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await bookService.getBooks(params);
      setBooks(response?.records || []);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0,
      }));
    } catch (error) {
      console.error('获取图书列表失败:', error);
      message.error('获取图书列表失败');
      setBooks([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await bookService.getCategories({ current: 1, size: 100 });
      setCategories(response?.records || []);
    } catch (error) {
      console.error('获取图书分类失败:', error);
      message.error('获取图书分类失败');
      setCategories([]);
    }
  };

  const handleAdd = () => {
    setEditingBook(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (record: Book) => {
    setEditingBook(record);
    setModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleView = (record: Book) => {
    setViewingBook(record);
    setDetailModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await bookService.deleteBook(id);
      message.success('删除成功');
      fetchBooks();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingBook) {
        await bookService.updateBook({ ...editingBook, ...values });
        message.success('更新成功');
      } else {
        await bookService.createBook(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchBooks();
    } catch (error) {
      message.error(editingBook ? '更新失败' : '创建失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchBooks();
  };

  const handleReset = () => {
    setSearchParams({
      name: '',
      author: '',
      categoryId: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchBooks, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '可借阅':
        return 'green';
      case '已借出':
        return 'orange';
      case '维护中':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '封面',
      dataIndex: 'cover',
      key: 'cover',
      width: 80,
      render: (cover: string) => (
        cover ? (
          <Image
            width={50}
            height={60}
            src={cover}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 50, height: 60, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图
          </div>
        )
      ),
    },
    {
      title: '图书名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      ellipsis: true,
    },
    {
      title: 'ISBN',
      dataIndex: 'isbn',
      key: 'isbn',
      width: 120,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 100,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      render: (price: number) => `¥${price}`,
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Book) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这本图书吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="图书总数"
              value={statistics.total}
              prefix={<BookOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可借阅"
              value={statistics.available}
              prefix={<StarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已借出"
              value={statistics.borrowed}
              prefix={<ShoppingCartOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="维护中"
              value={statistics.maintenance}
              prefix={<HeartOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card
        title={
          <Space>
            <BookOutlined />
            <Title level={4} style={{ margin: 0 }}>图书管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchBooks();
                  fetchCategories();
                }}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="导入图书">
              <Button icon={<ImportOutlined />} />
            </Tooltip>
            <Tooltip title="导出数据">
              <Button icon={<ExportOutlined />} />
            </Tooltip>
          </Space>
        }
      >
        {/* 搜索区域 */}
        <Card
          size="small"
          style={{
            marginBottom: 16,
            background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
            border: '1px solid #b7eb8f'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="搜索图书名称"
                value={searchParams.name}
                onChange={(e) => setSearchParams(prev => ({ ...prev, name: e.target.value }))}
                prefix={<SearchOutlined />}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="搜索作者"
                value={searchParams.author}
                onChange={(e) => setSearchParams(prev => ({ ...prev, author: e.target.value }))}
                prefix={<EditOutlined />}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="选择分类"
                value={searchParams.categoryId}
                onChange={(value) => setSearchParams(prev => ({ ...prev, categoryId: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                {(categories || []).map(category => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Space style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Space>
              {hasPermission(PERMISSIONS.BOOK_CREATE) && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  size="large"
                >
                  新增图书
                </Button>
              )}
            </Space>
          </Col>
          <Col xs={24} sm={12} md={16} style={{ textAlign: 'right' }}>
            <Text type="secondary">
              共找到 {pagination.total} 本图书
            </Text>
          </Col>
        </Row>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={books}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1200 }}
      />

      {/* 编辑/新增模态框 */}
      <Modal
        title={editingBook ? '编辑图书' : '新增图书'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="图书名称"
            rules={[{ required: true, message: '请输入图书名称' }]}
          >
            <Input placeholder="请输入图书名称" />
          </Form.Item>

          <Form.Item
            name="author"
            label="作者"
            rules={[{ required: true, message: '请输入作者' }]}
          >
            <Input placeholder="请输入作者" />
          </Form.Item>

          <Form.Item
            name="publisher"
            label="出版社"
            rules={[{ required: true, message: '请输入出版社' }]}
          >
            <Input placeholder="请输入出版社" />
          </Form.Item>

          <Form.Item
            name="isbn"
            label="ISBN"
            rules={[{ required: true, message: '请输入ISBN' }]}
          >
            <Input placeholder="请输入ISBN" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="图书分类"
            rules={[{ required: true, message: '请选择图书分类' }]}
          >
            <Select placeholder="请选择图书分类">
              {(categories || []).map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="price"
            label="价格"
            rules={[{ required: true, message: '请输入价格' }]}
          >
            <InputNumber
              placeholder="请输入价格"
              min={0}
              precision={2}
              style={{ width: '100%' }}
              addonBefore="¥"
            />
          </Form.Item>

          <Form.Item
            name="stock"
            label="库存数量"
            rules={[{ required: true, message: '请输入库存数量' }]}
          >
            <InputNumber
              placeholder="请输入库存数量"
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="可借阅">可借阅</Option>
              <Option value="已借出">已借出</Option>
              <Option value="维护中">维护中</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="图书描述"
          >
            <TextArea placeholder="请输入图书描述" rows={4} />
          </Form.Item>

          <Form.Item
            name="cover"
            label="封面图片"
          >
            <Input placeholder="请输入封面图片URL" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title="图书详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {viewingBook && (
          <div>
            <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
              <div>
                {viewingBook.cover ? (
                  <Image
                    width={120}
                    height={150}
                    src={viewingBook.cover}
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />
                ) : (
                  <div style={{ width: 120, height: 150, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    无封面
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <h3>{viewingBook.name}</h3>
                <p><strong>作者：</strong>{viewingBook.author}</p>
                <p><strong>出版社：</strong>{viewingBook.publisher}</p>
                <p><strong>ISBN：</strong>{viewingBook.isbn}</p>
                <p><strong>分类：</strong>{viewingBook.categoryName}</p>
                <p><strong>价格：</strong>¥{viewingBook.price}</p>
                <p><strong>库存：</strong>{viewingBook.stock}</p>
                <p><strong>状态：</strong><Tag color={getStatusColor(viewingBook.status)}>{viewingBook.status}</Tag></p>
              </div>
            </div>
            {viewingBook.description && (
              <div>
                <h4>图书描述</h4>
                <p>{viewingBook.description}</p>
              </div>
            )}
          </div>
        )}
      </Modal>
      </Card>
    </div>
  );
};

export default BookManagement;
