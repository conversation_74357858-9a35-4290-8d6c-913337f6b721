import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Card,
  Tag,
  Tooltip,
  Alert,
  Select,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Avatar,
  Timeline,
  Tabs,
  Divider,
  notification,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  MessageOutlined,
  CommentOutlined,
  BellOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  ExportOutlined,
  SendOutlined,
  NotificationOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { Message, PageParams } from '../types';
import { messageService } from '../services/messageService';
import PermissionWrapper, { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';
import ActionFeedback from '../components/Common/ActionFeedback';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

const MessageManagement: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [replyingMessage, setReplyingMessage] = useState<Message | null>(null);
  const [viewingMessage, setViewingMessage] = useState<Message | null>(null);
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    replied: 0,
    closed: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    userName: '',
    status: undefined as string | undefined,
  });
  const [form] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const { hasPermission } = usePermission();

  useEffect(() => {
    fetchMessages();
  }, [pagination.current, pagination.pageSize]);

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await messageService.getMessages(params);
      const records = response?.records || [];
      setMessages(records);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0,
      }));

      // 计算统计信息
      const stats = {
        total: response?.total || 0,
        pending: records.filter((item: Message) => item.status === '待回复').length,
        replied: records.filter((item: Message) => item.status === '已回复').length,
        closed: records.filter((item: Message) => item.status === '已关闭').length,
      };
      setStatistics(stats);
    } catch (error) {
      console.error('获取留言列表失败:', error);
      ActionFeedback.error('获取留言列表失败');
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleReply = (record: Message) => {
    setReplyingMessage(record);
    setReplyModalVisible(true);
    form.resetFields();
  };

  const handleView = (record: Message) => {
    setViewingMessage(record);
    setDetailModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await messageService.deleteMessage(id);
      message.success('删除成功');
      fetchMessages();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleReplySubmit = async (values: any) => {
    if (!replyingMessage) return;

    try {
      await messageService.replyMessage(replyingMessage.id, values.reply);
      ActionFeedback.success('回复成功');
      setReplyModalVisible(false);
      fetchMessages();
    } catch (error) {
      ActionFeedback.error('回复失败');
    }
  };

  const handleSendNotification = async (values: any) => {
    try {
      // 模拟发送系统通知
      setTimeout(() => {
        ActionFeedback.success('系统通知发送成功');
        setNotificationModalVisible(false);
        notificationForm.resetFields();

        // 显示通知预览
        notification.info({
          message: values.title,
          description: values.content,
          duration: 4.5,
          placement: 'topRight',
        });
      }, 1000);
    } catch (error) {
      ActionFeedback.error('发送通知失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchMessages();
  };

  const handleReset = () => {
    setSearchParams({
      userName: '',
      status: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchMessages, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已回复':
        return 'green';
      case '待回复':
        return 'orange';
      case '已关闭':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content: string) => (
        <div style={{ maxWidth: 300 }}>
          {content.length > 50 ? `${content.substring(0, 50)}...` : content}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Message) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<MessageOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          {record.status === '待回复' && (
            <Tooltip title="回复">
              <Button
                type="link"
                size="small"
                icon={<CommentOutlined />}
                onClick={() => handleReply(record)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleReply(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条留言吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={statistics.total}
              prefix={<MessageOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待回复"
              value={statistics.pending}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已回复"
              value={statistics.replied}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已关闭"
              value={statistics.closed}
              prefix={<ExclamationCircleOutlined style={{ color: '#8c8c8c' }} />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card
        title={
          <Space>
            <MessageOutlined />
            <Title level={4} style={{ margin: 0 }}>消息通知管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchMessages}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="发送系统通知">
              <Button
                type="primary"
                icon={<NotificationOutlined />}
                onClick={() => setNotificationModalVisible(true)}
              >
                发送通知
              </Button>
            </Tooltip>
            <Tooltip title="导出数据">
              <Button icon={<ExportOutlined />} />
            </Tooltip>
          </Space>
        }
      >
        {/* 提示信息 */}
        <Alert
          message="消息通知管理说明"
          description="管理用户留言和系统通知，包括查看留言内容、回复用户留言、发送系统公告等。及时回复用户留言可以提升用户体验。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 搜索区域 */}
        <Card
          size="small"
          style={{
            marginBottom: 16,
            background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
            border: '1px solid #b7eb8f'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Input
                placeholder="搜索用户姓名"
                value={searchParams.userName}
                onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}
                prefix={<UserOutlined />}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Select
                placeholder="选择状态"
                value={searchParams.status}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="待回复">
                  <Space>
                    <ClockCircleOutlined />
                    待回复
                  </Space>
                </Option>
                <Option value="已回复">
                  <Space>
                    <CheckCircleOutlined />
                    已回复
                  </Space>
                </Option>
                <Option value="已关闭">
                  <Space>
                    <ExclamationCircleOutlined />
                    已关闭
                  </Space>
                </Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Card>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={messages}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1000 }}
      />

      {/* 回复模态框 */}
      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        {replyingMessage && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
              <p><strong>用户：</strong>{replyingMessage.userName}</p>
              <p><strong>留言时间：</strong>{dayjs(replyingMessage.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
              <p><strong>留言内容：</strong></p>
              <div style={{ padding: 8, background: 'white', borderRadius: 4, marginTop: 8 }}>
                {replyingMessage.content}
              </div>
            </div>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleReplySubmit}
            >
              <Form.Item
                name="reply"
                label="回复内容"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  placeholder="请输入回复内容"
                  rows={4}
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title="留言详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {viewingMessage && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <p><strong>用户：</strong>{viewingMessage.userName}</p>
              <p><strong>状态：</strong><Tag color={getStatusColor(viewingMessage.status)}>{viewingMessage.status}</Tag></p>
              <p><strong>留言时间：</strong>{dayjs(viewingMessage.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            </div>

            <div style={{ marginBottom: 16 }}>
              <h4>留言内容：</h4>
              <div style={{ padding: 12, background: '#f5f5f5', borderRadius: 4, marginTop: 8 }}>
                {viewingMessage.content}
              </div>
            </div>

            {viewingMessage.reply && (
              <div>
                <h4>回复内容：</h4>
                <div style={{ padding: 12, background: '#e6f7ff', borderRadius: 4, marginTop: 8 }}>
                  {viewingMessage.reply}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 系统通知模态框 */}
      <Modal
        title={
          <Space>
            <NotificationOutlined />
            <span>发送系统通知</span>
          </Space>
        }
        open={notificationModalVisible}
        onCancel={() => setNotificationModalVisible(false)}
        onOk={() => notificationForm.submit()}
        width={600}
        centered
      >
        <Alert
          message="通知说明"
          description="系统通知将发送给所有用户，请谨慎编写通知内容。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={notificationForm}
          layout="vertical"
          onFinish={handleSendNotification}
        >
          <Form.Item
            name="type"
            label="通知类型"
            rules={[{ required: true, message: '请选择通知类型' }]}
          >
            <Select placeholder="请选择通知类型">
              <Option value="system">系统公告</Option>
              <Option value="maintenance">维护通知</Option>
              <Option value="reminder">借阅提醒</Option>
              <Option value="overdue">逾期通知</Option>
              <Option value="event">活动通知</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="通知标题"
            rules={[
              { required: true, message: '请输入通知标题' },
              { max: 50, message: '标题最多50个字符' }
            ]}
          >
            <Input
              placeholder="请输入通知标题"
              showCount
              maxLength={50}
            />
          </Form.Item>

          <Form.Item
            name="content"
            label="通知内容"
            rules={[
              { required: true, message: '请输入通知内容' },
              { max: 500, message: '内容最多500个字符' }
            ]}
          >
            <TextArea
              placeholder="请输入通知内容"
              rows={6}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="low">
                <Space>
                  <Badge color="blue" />
                  普通
                </Space>
              </Option>
              <Option value="medium">
                <Space>
                  <Badge color="orange" />
                  重要
                </Space>
              </Option>
              <Option value="high">
                <Space>
                  <Badge color="red" />
                  紧急
                </Space>
              </Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      </Card>
    </div>
  );
};

export default MessageManagement;
