import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  InputNumber,
  DatePicker,
  message,
  Popconfirm,
  Card,
  Tag,
  Input,
  Tooltip,
  Alert,
  Select,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { BookReturn, BookBorrow, PageParams } from '../types';
import { returnService } from '../services/returnService';
import { borrowService } from '../services/borrowService';

const { Option } = Select;

const ReturnManagement: React.FC = () => {
  const [returns, setReturns] = useState<BookReturn[]>([]);
  const [availableBorrows, setAvailableBorrows] = useState<BookBorrow[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReturn, setEditingReturn] = useState<BookReturn | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    userName: '',
    bookName: '',
    status: undefined as string | undefined,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    fetchReturns();
    fetchAvailableBorrows();
  }, [pagination.current, pagination.pageSize]);

  const fetchReturns = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await returnService.getReturns(params);
      setReturns(response?.records || []);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0,
      }));
    } catch (error) {
      console.error('获取归还记录失败:', error);
      message.error('获取归还记录失败');
      setReturns([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableBorrows = async () => {
    try {
      // 获取已借出但未归还的借阅记录
      const response = await borrowService.getBorrows({
        current: 1,
        size: 100,
        status: '已借出'
      });
      setAvailableBorrows(response?.records || []);
    } catch (error) {
      console.error('获取可归还图书失败:', error);
      message.error('获取可归还图书失败');
      setAvailableBorrows([]);
    }
  };

  const handleAdd = () => {
    setEditingReturn(null);
    setModalVisible(true);
    form.resetFields();
    // 设置默认归还时间为当前时间
    form.setFieldsValue({
      returnTime: dayjs(),
    });
  };

  const handleEdit = (record: BookReturn) => {
    setEditingReturn(record);
    setModalVisible(true);
    form.setFieldsValue({
      ...record,
      returnTime: record.returnTime ? dayjs(record.returnTime) : null,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await returnService.deleteReturn(id);
      message.success('删除成功');
      fetchReturns();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await returnService.approveReturn(id);
      message.success('审核通过');
      fetchReturns();
      fetchAvailableBorrows(); // 刷新可借阅列表
    } catch (error) {
      message.error('审核失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        returnTime: values.returnTime?.format('YYYY-MM-DD HH:mm:ss'),
        status: editingReturn ? editingReturn.status : '待审核',
      };

      if (editingReturn) {
        await returnService.updateReturn({ ...editingReturn, ...submitData });
        message.success('更新成功');
      } else {
        await returnService.createReturn(submitData);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchReturns();
      fetchAvailableBorrows();
    } catch (error) {
      message.error(editingReturn ? '更新失败' : '创建失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchReturns();
  };

  const handleReset = () => {
    setSearchParams({
      userName: '',
      bookName: '',
      status: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchReturns, 100);
  };

  const calculateFine = (borrowId: number, returnTime: string) => {
    const borrow = (availableBorrows || []).find(b => b.id === borrowId);
    if (!borrow) return 0;

    const expectedReturn = dayjs(borrow.expectedReturnTime);
    const actualReturn = dayjs(returnTime);

    if (actualReturn.isAfter(expectedReturn)) {
      const overdueDays = actualReturn.diff(expectedReturn, 'day');
      return overdueDays * 1; // 每天1元罚金
    }
    return 0;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '待审核':
        return 'orange';
      case '已归还':
        return 'green';
      case '已拒绝':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
    },
    {
      title: '图书名称',
      dataIndex: 'bookName',
      key: 'bookName',
      ellipsis: true,
    },
    {
      title: '归还时间',
      dataIndex: 'returnTime',
      key: 'returnTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      width: 100,
      render: (fine: number) => (
        <span style={{ color: fine > 0 ? '#ff4d4f' : '#52c41a' }}>
          ¥{fine || 0}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: BookReturn) => (
        <Space size="small">
          {record.status === '待审核' && (
            <Tooltip title="审核通过">
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleApprove(record.id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条归还记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      {/* 提示信息 */}
      <Alert
        message="归还管理说明"
        description="管理图书归还记录，包括审核归还申请、计算罚金等。逾期归还的图书会自动计算罚金（每天1元）。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 搜索区域 */}
      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <Space wrap>
          <Input
            placeholder="用户姓名"
            value={searchParams.userName}
            onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}
            style={{ width: 200 }}
          />
          <Input
            placeholder="图书名称"
            value={searchParams.bookName}
            onChange={(e) => setSearchParams(prev => ({ ...prev, bookName: e.target.value }))}
            style={{ width: 200 }}
          />
          <Select
            placeholder="选择状态"
            value={searchParams.status}
            onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
            style={{ width: 200 }}
            allowClear
          >
            <Option value="待审核">待审核</Option>
            <Option value="已归还">已归还</Option>
            <Option value="已拒绝">已拒绝</Option>
          </Select>
          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </div>

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          新增归还记录
        </Button>
      </div>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={returns}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1200 }}
      />

      {/* 编辑/新增模态框 */}
      <Modal
        title={editingReturn ? '编辑归还记录' : '新增归还记录'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={(changedValues) => {
            if (changedValues.borrowId || changedValues.returnTime) {
              const borrowId = form.getFieldValue('borrowId');
              const returnTime = form.getFieldValue('returnTime');
              if (borrowId && returnTime) {
                const fine = calculateFine(borrowId, returnTime.format('YYYY-MM-DD HH:mm:ss'));
                form.setFieldsValue({ fine });
              }
            }
          }}
        >
          <Form.Item
            name="borrowId"
            label="选择借阅记录"
            rules={[{ required: true, message: '请选择借阅记录' }]}
          >
            <Select placeholder="请选择要归还的借阅记录" showSearch optionFilterProp="children">
              {(availableBorrows || []).map(borrow => (
                <Option key={borrow.id} value={borrow.id}>
                  {borrow.userName} - {borrow.bookName} (借阅时间: {dayjs(borrow.borrowTime).format('YYYY-MM-DD')})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="returnTime"
            label="归还时间"
            rules={[{ required: true, message: '请选择归还时间' }]}
          >
            <DatePicker
              showTime
              placeholder="请选择归还时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          <Form.Item
            name="fine"
            label="罚金金额"
          >
            <InputNumber
              placeholder="系统自动计算"
              min={0}
              precision={2}
              style={{ width: '100%' }}
              addonBefore="¥"
              disabled
            />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default ReturnManagement;
