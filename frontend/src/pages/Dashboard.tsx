import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Table,
  List,
  Avatar,
  Typography,
  Space,
  Tag,
  Progress,
  Timeline,
  Button,
  Divider,
  Alert
} from 'antd';
import {
  UserOutlined,
  BookOutlined,
  ReadOutlined,
  PayCircleOutlined,
  SafetyOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  CalendarOutlined,
  BellOutlined,
  HeartOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { usePermission } from '../components/Auth/PermissionWrapper';

const { Title, Text, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { isAdmin } = usePermission();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBooks: 0,
    totalBorrows: 0,
    totalFines: 0,
    todayBorrows: 0,
    todayReturns: 0,
    overdueBorrows: 0,
    popularBooks: 0,
  });
  const [loading, setLoading] = useState(true);

  const [recentBorrows] = useState([
    {
      id: 1,
      userName: '张三',
      bookName: 'Java编程思想',
      borrowTime: '2023-12-01',
      status: '已借出',
    },
    {
      id: 2,
      userName: '李四',
      bookName: 'Spring Boot实战',
      borrowTime: '2023-12-02',
      status: '已归还',
    },
  ]);

  const [recentNews] = useState([
    {
      id: 1,
      title: '图书馆开放时间调整通知',
      createTime: '2023-12-01',
    },
    {
      id: 2,
      title: '新书上架通知',
      createTime: '2023-12-02',
    },
  ]);

  useEffect(() => {
    // 模拟API调用获取统计数据
    const fetchStats = async () => {
      setLoading(true);
      // 模拟网络延迟
      setTimeout(() => {
        setStats({
          totalUsers: 150,
          totalBooks: 1200,
          totalBorrows: 89,
          totalFines: 5,
          todayBorrows: 12,
          todayReturns: 8,
          overdueBorrows: 3,
          popularBooks: 25,
        });
        setLoading(false);
      }, 1000);
    };

    fetchStats();
  }, []);

  const borrowColumns = [
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '图书名称',
      dataIndex: 'bookName',
      key: 'bookName',
    },
    {
      title: '借阅时间',
      dataIndex: 'borrowTime',
      key: 'borrowTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      {/* 欢迎横幅 */}
      <Card
        style={{
          marginBottom: 24,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          color: 'white'
        }}
        bodyStyle={{ padding: '24px 32px' }}
      >
        <Row align="middle">
          <Col flex="auto">
            <Space direction="vertical" size="small">
              <Title level={3} style={{ color: 'white', margin: 0 }}>
                欢迎回来，{user?.name || user?.username}！
              </Title>
              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 16 }}>
                今天是 {new Date().toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </Text>
            </Space>
          </Col>
          <Col>
            <Avatar
              size={64}
              icon={user?.role === '管理员' ? <SafetyOutlined /> : <UserOutlined />}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                border: '2px solid rgba(255, 255, 255, 0.3)'
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card loading={loading}>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <Tag color="green" style={{ marginLeft: 8 }}>
                  <RiseOutlined /> +12
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card loading={loading}>
            <Statistic
              title="图书总数"
              value={stats.totalBooks}
              prefix={<BookOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  <StarOutlined /> 热门
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card loading={loading}>
            <Statistic
              title="当前借阅"
              value={stats.totalBorrows}
              prefix={<ReadOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
              suffix={
                <Tag color="orange" style={{ marginLeft: 8 }}>
                  今日 +{stats.todayBorrows}
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card loading={loading}>
            <Statistic
              title="逾期图书"
              value={stats.overdueBorrows}
              prefix={<ClockCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
              suffix={
                <Tag color="red" style={{ marginLeft: 8 }}>
                  需处理
                </Tag>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 今日数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日借阅"
              value={stats.todayBorrows}
              prefix={<TrophyOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
            <Progress
              percent={Math.round((stats.todayBorrows / 20) * 100)}
              size="small"
              strokeColor="#722ed1"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日归还"
              value={stats.todayReturns}
              prefix={<HeartOutlined style={{ color: '#eb2f96' }} />}
              valueStyle={{ color: '#eb2f96' }}
            />
            <Progress
              percent={Math.round((stats.todayReturns / 15) * 100)}
              size="small"
              strokeColor="#eb2f96"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="热门图书"
              value={stats.popularBooks}
              prefix={<StarOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <Progress
              percent={Math.round((stats.popularBooks / 50) * 100)}
              size="small"
              strokeColor="#fa8c16"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待缴罚金"
              value={stats.totalFines}
              prefix={<PayCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
              suffix="笔"
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              总金额: ¥{(stats.totalFines * 2.5).toFixed(2)}
            </Text>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <ReadOutlined />
                <span>最近借阅记录</span>
              </Space>
            }
            extra={
              <Button type="link" size="small">
                查看全部
              </Button>
            }
            style={{ height: 400 }}
          >
            <Table
              columns={borrowColumns}
              dataSource={recentBorrows}
              pagination={false}
              size="small"
              loading={loading}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <BellOutlined />
                <span>系统公告</span>
              </Space>
            }
            extra={
              <Button type="link" size="small">
                更多公告
              </Button>
            }
            style={{ height: 400 }}
          >
            <List
              itemLayout="horizontal"
              dataSource={recentNews}
              loading={loading}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<BellOutlined />}
                        style={{ backgroundColor: '#1890ff' }}
                      />
                    }
                    title={
                      <Text strong style={{ fontSize: 14 }}>
                        {item.title}
                      </Text>
                    }
                    description={
                      <Space>
                        <CalendarOutlined />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.createTime}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快捷操作和系统状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} md={12}>
          <Card
            title={
              <Space>
                <TrophyOutlined />
                <span>快捷操作</span>
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {isAdmin() && (
                <>
                  <Button type="primary" block icon={<UserOutlined />}>
                    用户管理
                  </Button>
                  <Button block icon={<BookOutlined />}>
                    图书管理
                  </Button>
                  <Button block icon={<ReadOutlined />}>
                    借阅管理
                  </Button>
                </>
              )}
              <Button block icon={<HeartOutlined />}>
                我的收藏
              </Button>
              <Button block icon={<CalendarOutlined />}>
                借阅历史
              </Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                <span>系统状态</span>
              </Space>
            }
          >
            <Timeline
              items={[
                {
                  color: 'green',
                  children: (
                    <div>
                      <Text strong>系统运行正常</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        所有服务运行稳定
                      </Text>
                    </div>
                  ),
                },
                {
                  color: 'blue',
                  children: (
                    <div>
                      <Text strong>数据库连接正常</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        响应时间: 12ms
                      </Text>
                    </div>
                  ),
                },
                {
                  color: 'orange',
                  children: (
                    <div>
                      <Text strong>定时任务运行中</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        下次执行: 23:00
                      </Text>
                    </div>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* 管理员专用提醒 */}
      {isAdmin() && (
        <Row style={{ marginTop: 24 }}>
          <Col span={24}>
            <Alert
              message="管理员提醒"
              description={
                <div>
                  <Paragraph style={{ marginBottom: 8 }}>
                    • 当前有 {stats.overdueBorrows} 本图书逾期，请及时处理
                  </Paragraph>
                  <Paragraph style={{ marginBottom: 8 }}>
                    • 今日新增用户 12 人，图书借阅 {stats.todayBorrows} 次
                  </Paragraph>
                  <Paragraph style={{ marginBottom: 0 }}>
                    • 系统运行正常，所有服务状态良好
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
