import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, message, Modal, Space, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, UserAddOutlined, SafetyOutlined, BookOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { LoginParams } from '../types';
import { authService } from '../services/authService';

const { Option } = Select;
const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [registerModalVisible, setRegisterModalVisible] = useState(false);
  const [registerLoading, setRegisterLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const [registerForm] = Form.useForm();
  const [loginForm] = Form.useForm();

  const onFinish = async (values: LoginParams) => {
    setLoading(true);
    try {
      await login(values);
      message.success('登录成功');
      navigate('/dashboard');
    } catch (error: any) {
      message.error(error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (values: any) => {
    setRegisterLoading(true);
    try {
      // 注册时默认角色为普通用户
      const registerData = {
        ...values,
        role: '用户' // 强制设置为普通用户
      };
      delete registerData.confirmPassword; // 移除确认密码字段

      await authService.register(registerData);
      message.success('注册成功，请使用新账户登录');
      setRegisterModalVisible(false);
      registerForm.resetFields();
    } catch (error: any) {
      message.error(error.message || '注册失败');
    } finally {
      setRegisterLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-form">
        <div className="login-title">
          <BookOutlined style={{ marginRight: 8, fontSize: 32 }} />
          图书馆管理系统
        </div>
        <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginBottom: 32 }}>
          智能化图书管理，让阅读更简单
        </Text>

        <Form
          form={loginForm}
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' }
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请输入用户名"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 4, message: '密码至少4个字符!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请输入密码"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="role"
            label="登录角色"
            rules={[{ required: true, message: '请选择登录角色!' }]}
          >
            <Select
              placeholder="请选择登录角色"
              suffixIcon={<SafetyOutlined style={{ color: '#bfbfbf' }} />}
            >
              <Option value="管理员">
                <Space>
                  <SafetyOutlined />
                  管理员
                </Space>
              </Option>
              <Option value="用户">
                <Space>
                  <UserOutlined />
                  普通用户
                </Space>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%', height: 48 }}
              size="large"
            >
              {loading ? '登录中...' : '立即登录'}
            </Button>
          </Form.Item>

          <Divider plain>
            <Text type="secondary" style={{ fontSize: 12 }}>其他操作</Text>
          </Divider>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="default"
              icon={<UserAddOutlined />}
              onClick={() => setRegisterModalVisible(true)}
              style={{ width: '100%' }}
              size="large"
            >
              注册新用户
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 注册模态框 */}
      <Modal
        title={
          <Space>
            <UserAddOutlined style={{ color: '#1890ff' }} />
            <span>用户注册</span>
          </Space>
        }
        open={registerModalVisible}
        onCancel={() => {
          setRegisterModalVisible(false);
          registerForm.resetFields();
        }}
        onOk={() => registerForm.submit()}
        confirmLoading={registerLoading}
        width={520}
        centered
        destroyOnClose
      >
        <div style={{
          marginBottom: 24,
          padding: 16,
          background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
          border: '1px solid #b7eb8f',
          borderRadius: 8,
          position: 'relative'
        }}>
          <div style={{
            position: 'absolute',
            top: -8,
            left: 16,
            background: '#52c41a',
            color: 'white',
            padding: '4px 12px',
            borderRadius: 12,
            fontSize: 12,
            fontWeight: 600
          }}>
            注册须知
          </div>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a', fontSize: 14 }}>
            📝 新注册的账户将自动设置为普通用户权限，可以借阅图书和使用基本功能。
          </p>
        </div>

        <Form
          form={registerForm}
          layout="vertical"
          onFinish={handleRegister}
          size="large"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' },
              { max: 20, message: '用户名最多20个字符!' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线!' }
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请输入用户名"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请输入密码"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请再次输入密码"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="name"
            label="真实姓名"
            rules={[
              { required: true, message: '请输入真实姓名!' },
              { max: 50, message: '姓名最多50个字符!' }
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="请输入真实姓名"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号码"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号!' }
            ]}
          >
            <Input
              prefix={<span style={{ color: '#bfbfbf' }}>📱</span>}
              placeholder="请输入手机号码（可选）"
              allowClear
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Login;
