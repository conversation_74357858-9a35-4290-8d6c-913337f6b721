import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Card,
  Tag,
  Input,
  Tooltip,
  Alert,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  CheckOutlined,
  PayCircleOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { FinePayment, BookReturn, PageParams } from '../types';
import { fineService } from '../services/fineService';
import { returnService } from '../services/returnService';

const { Option } = Select;

const FineManagement: React.FC = () => {
  const [fines, setFines] = useState<FinePayment[]>([]);
  const [unpaidReturns, setUnpaidReturns] = useState<BookReturn[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingFine, setEditingFine] = useState<FinePayment | null>(null);
  const [statistics, setStatistics] = useState({
    totalFines: 0,
    paidFines: 0,
    unpaidFines: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    userName: '',
    status: undefined as string | undefined,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    fetchFines();
    fetchUnpaidReturns();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize]);

  const fetchFines = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await fineService.getFines(params);
      setFines(response?.records || []);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0,
      }));
    } catch (error) {
      console.error('获取罚金记录失败:', error);
      message.error('获取罚金记录失败');
      setFines([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUnpaidReturns = async () => {
    try {
      // 获取有罚金但未缴纳的归还记录
      const response = await returnService.getReturns({
        current: 1,
        size: 100,
        hasFine: true,
        fineStatus: '未缴纳'
      });
      setUnpaidReturns(response?.records || []);
    } catch (error) {
      console.error('获取待缴费记录失败:', error);
      message.error('获取待缴费记录失败');
      setUnpaidReturns([]);
    }
  };

  const fetchStatistics = async () => {
    try {
      const stats = await fineService.getStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('获取统计数据失败');
    }
  };

  const handleAdd = () => {
    setEditingFine(null);
    setModalVisible(true);
    form.resetFields();
    // 设置默认缴费时间为当前时间
    form.setFieldsValue({
      paymentTime: dayjs(),
    });
  };

  const handleEdit = (record: FinePayment) => {
    setEditingFine(record);
    setModalVisible(true);
    form.setFieldsValue({
      ...record,
      paymentTime: record.paymentTime ? dayjs(record.paymentTime) : null,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await fineService.deleteFine(id);
      message.success('删除成功');
      fetchFines();
      fetchStatistics();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handlePay = async (id: number) => {
    try {
      await fineService.payFine(id);
      message.success('缴费成功');
      fetchFines();
      fetchUnpaidReturns();
      fetchStatistics();
    } catch (error) {
      message.error('缴费失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        paymentTime: values.paymentTime?.format('YYYY-MM-DD HH:mm:ss'),
        status: editingFine ? editingFine.status : '已缴费',
      };

      if (editingFine) {
        await fineService.updateFine({ ...editingFine, ...submitData });
        message.success('更新成功');
      } else {
        await fineService.createFine(submitData);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchFines();
      fetchUnpaidReturns();
      fetchStatistics();
    } catch (error) {
      message.error(editingFine ? '更新失败' : '创建失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchFines();
  };

  const handleReset = () => {
    setSearchParams({
      userName: '',
      status: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchFines, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已缴费':
        return 'green';
      case '未缴费':
        return 'red';
      case '部分缴费':
        return 'orange';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
    },
    {
      title: '归还记录ID',
      dataIndex: 'returnId',
      key: 'returnId',
      width: 120,
    },
    {
      title: '罚金金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ¥{amount}
        </span>
      ),
    },
    {
      title: '缴费时间',
      dataIndex: 'paymentTime',
      key: 'paymentTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: FinePayment) => (
        <Space size="small">
          {record.status === '未缴费' && (
            <Tooltip title="确认缴费">
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handlePay(record.id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条罚金记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总罚金金额"
              value={statistics.totalFines}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已缴费金额"
              value={statistics.paidFines}
              prefix={<PayCircleOutlined />}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="未缴费金额"
              value={statistics.unpaidFines}
              prefix={<PayCircleOutlined />}
              precision={2}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 提示信息 */}
      <Alert
        message="罚金管理说明"
        description="管理图书逾期归还产生的罚金，包括罚金记录的创建、缴费确认等。系统会自动计算逾期天数产生的罚金。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 搜索区域 */}
      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <Space wrap>
          <Input
            placeholder="用户姓名"
            value={searchParams.userName}
            onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}
            style={{ width: 200 }}
          />
          <Select
            placeholder="选择状态"
            value={searchParams.status}
            onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
            style={{ width: 200 }}
            allowClear
          >
            <Option value="已缴费">已缴费</Option>
            <Option value="未缴费">未缴费</Option>
            <Option value="部分缴费">部分缴费</Option>
          </Select>
          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </div>

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          新增罚金记录
        </Button>
      </div>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={fines}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1200 }}
      />

      {/* 编辑/新增模态框 */}
      <Modal
        title={editingFine ? '编辑罚金记录' : '新增罚金记录'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="returnId"
            label="选择归还记录"
            rules={[{ required: true, message: '请选择归还记录' }]}
          >
            <Select placeholder="请选择有罚金的归还记录" showSearch optionFilterProp="children">
              {(unpaidReturns || []).map(returnRecord => (
                <Option key={returnRecord.id} value={returnRecord.id}>
                  {returnRecord.userName} - {returnRecord.bookName} (罚金: ¥{returnRecord.fine})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="paymentTime"
            label="缴费时间"
            rules={[{ required: true, message: '请选择缴费时间' }]}
          >
            <DatePicker
              showTime
              placeholder="请选择缴费时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="缴费状态"
            rules={[{ required: true, message: '请选择缴费状态' }]}
          >
            <Select placeholder="请选择缴费状态">
              <Option value="已缴费">已缴费</Option>
              <Option value="未缴费">未缴费</Option>
              <Option value="部分缴费">部分缴费</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default FineManagement;
