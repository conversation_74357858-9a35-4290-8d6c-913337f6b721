import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Card,
  Tag,
  Avatar,
  Tooltip,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  SafetyOutlined,
  PhoneOutlined,
  MailOutlined,
  CalendarOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { User, PageParams } from '../types';
import { userService } from '../services/userService';
import PermissionWrapper, { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';

const { Option } = Select;
const { Search } = Input;
const { Title, Text } = Typography;

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [statistics, setStatistics] = useState({
    total: 0,
    adminCount: 0,
    userCount: 0,
    activeCount: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [form] = Form.useForm();
  const { hasPermission } = usePermission();

  useEffect(() => {
    fetchUsers();
  }, [pagination.current, pagination.pageSize]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
      };
      const response = await userService.getUsers(params);
      setUsers(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingUser(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (record: User) => {
    setEditingUser(record);
    setModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleDelete = async (id: number) => {
    try {
      await userService.deleteUser(id);
      message.success('删除成功');
      fetchUsers();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingUser) {
        await userService.updateUser({ ...editingUser, ...values });
        message.success('更新成功');
      } else {
        await userService.createUser(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchUsers();
    } catch (error) {
      message.error(editingUser ? '更新失败' : '创建失败');
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id: number) => (
        <Badge count={id} style={{ backgroundColor: '#f0f0f0', color: '#666' }} />
      ),
    },
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_: any, record: User) => (
        <Space>
          <Avatar
            icon={<UserOutlined />}
            style={{
              backgroundColor: record.role === '管理员' ? '#ff4d4f' : '#1890ff'
            }}
          />
          <div>
            <div style={{ fontWeight: 600, color: '#262626' }}>
              {record.name || record.username}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              @{record.username}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_: any, record: User) => (
        <Space direction="vertical" size="small">
          {record.phone && (
            <Space size="small">
              <PhoneOutlined style={{ color: '#52c41a' }} />
              <Text>{record.phone}</Text>
            </Space>
          )}
          {record.email && (
            <Space size="small">
              <MailOutlined style={{ color: '#1890ff' }} />
              <Text>{record.email}</Text>
            </Space>
          )}
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag
          icon={role === '管理员' ? <SafetyOutlined /> : <UserOutlined />}
          color={role === '管理员' ? 'red' : 'blue'}
          style={{ borderRadius: '16px', padding: '4px 12px' }}
        >
          {role}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (time: string) => (
        <Space>
          <CalendarOutlined style={{ color: '#8c8c8c' }} />
          <Text type="secondary">{time}</Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: User) => (
        <Space size="small">
          {hasPermission(PERMISSIONS.USER_EDIT) && (
            <Tooltip title="编辑用户">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
          )}
          {hasPermission(PERMISSIONS.USER_DELETE) && (
            <Popconfirm
              title="确定要删除这个用户吗？"
              description="删除后将无法恢复，请谨慎操作。"
              onConfirm={() => handleDelete(record.id)}
              okText="确定删除"
              cancelText="取消"
              okButtonProps={{ danger: true }}
            >
              <Tooltip title="删除用户">
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={statistics.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="管理员"
              value={statistics.adminCount}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="普通用户"
              value={statistics.userCount}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={statistics.activeCount}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card
        title={
          <Space>
            <UserOutlined />
            <Title level={4} style={{ margin: 0 }}>用户管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchUsers}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="导出数据">
              <Button icon={<ExportOutlined />} />
            </Tooltip>
          </Space>
        }
      >
        {/* 搜索和操作栏 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索用户名、姓名或手机号"
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={(value) => setSearchText(value)}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="筛选角色"
              allowClear
              size="large"
              style={{ width: '100%' }}
              onChange={(value) => setSelectedRole(value || '')}
            >
              <Option value="管理员">管理员</Option>
              <Option value="用户">普通用户</Option>
            </Select>
          </Col>
          <Col xs={24} sm={24} md={10} style={{ textAlign: 'right' }}>
            <Space>
              <PermissionWrapper permission={PERMISSIONS.USER_CREATE}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  size="large"
                >
                  新增用户
                </Button>
              </PermissionWrapper>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={users}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10,
              }));
            },
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          rowKey="id"
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>

      <Modal
        title={
          <Space>
            {editingUser ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingUser ? '编辑用户' : '新增用户'}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
        centered
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="large"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线' }
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="请输入用户名"
                  disabled={!!editingUser}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="真实姓名"
                rules={[
                  { required: true, message: '请输入真实姓名' },
                  { max: 50, message: '姓名最多50个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="请输入真实姓名"
                />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Form.Item
              name="password"
              label="登录密码"
              rules={[
                { required: true, message: '请输入登录密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                prefix={<SafetyOutlined style={{ color: '#bfbfbf' }} />}
                placeholder="请输入登录密码"
              />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号码"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                ]}
              >
                <Input
                  prefix={<PhoneOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="请输入手机号码"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { type: 'email', message: '请输入正确的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="请输入邮箱地址"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="role"
            label="用户角色"
            rules={[{ required: true, message: '请选择用户角色' }]}
          >
            <Select
              placeholder="请选择用户角色"
              suffixIcon={<SafetyOutlined style={{ color: '#bfbfbf' }} />}
            >
              <Option value="用户">
                <Space>
                  <UserOutlined />
                  普通用户
                </Space>
              </Option>
              <Option value="管理员">
                <Space>
                  <SafetyOutlined />
                  系统管理员
                </Space>
              </Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
