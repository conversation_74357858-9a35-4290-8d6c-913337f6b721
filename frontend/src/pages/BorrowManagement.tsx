import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Card,
  Tag,
  Input,
  Tooltip,
  Alert,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Avatar,
  Progress,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  CheckOutlined,
  CloseOutlined,
  ReadOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  ExportOutlined,
  UserOutlined,
  BookOutlined,
  CalendarOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { BookBorrow, User, Book, PageParams } from '../types';
import { borrowService } from '../services/borrowService';
import { userService } from '../services/userService';
import { bookService } from '../services/bookService';
import PermissionWrapper, { PERMISSIONS, usePermission } from '../components/Auth/PermissionWrapper';
import ActionFeedback from '../components/Common/ActionFeedback';

const { Option } = Select;
const { Search } = Input;
const { Title, Text } = Typography;

const BorrowManagement: React.FC = () => {
  const [borrows, setBorrows] = useState<BookBorrow[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBorrow, setEditingBorrow] = useState<BookBorrow | null>(null);
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    borrowed: 0,
    returned: 0,
    overdue: 0,
    rejected: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    userName: '',
    bookName: '',
    status: undefined as string | undefined,
  });
  const [form] = Form.useForm();
  const { hasPermission } = usePermission();

  useEffect(() => {
    fetchBorrows();
    fetchUsers();
    fetchBooks();
  }, [pagination.current, pagination.pageSize]);

  const fetchBorrows = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await borrowService.getBorrows(params);
      const records = response?.records || [];
      setBorrows(records);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0,
      }));

      // 计算统计信息
      const stats = {
        total: response?.total || 0,
        pending: records.filter((item: BookBorrow) => item.status === '待审核').length,
        borrowed: records.filter((item: BookBorrow) => item.status === '已借出').length,
        returned: records.filter((item: BookBorrow) => item.status === '已归还').length,
        overdue: records.filter((item: BookBorrow) =>
          item.status === '已借出' && isOverdue(item.expectedReturnTime, item.status)
        ).length,
        rejected: records.filter((item: BookBorrow) => item.status === '已拒绝').length,
      };
      setStatistics(stats);
    } catch (error) {
      console.error('获取借阅记录失败:', error);
      ActionFeedback.error('获取借阅记录失败');
      setBorrows([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await userService.getUsers({ current: 1, size: 100 });
      setUsers(response?.records || []);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setUsers([]);
    }
  };

  const fetchBooks = async () => {
    try {
      const response = await bookService.getBooks({ current: 1, size: 100 });
      const records = response?.records || [];
      setBooks(records.filter(book => book.status === '可借阅'));
    } catch (error) {
      console.error('获取图书列表失败:', error);
      message.error('获取图书列表失败');
      setBooks([]);
    }
  };

  const handleAdd = () => {
    setEditingBorrow(null);
    setModalVisible(true);
    form.resetFields();
    // 设置默认的预期归还时间（30天后）
    form.setFieldsValue({
      expectedReturnTime: dayjs().add(30, 'day'),
    });
  };

  const handleEdit = (record: BookBorrow) => {
    setEditingBorrow(record);
    setModalVisible(true);
    form.setFieldsValue({
      ...record,
      borrowTime: record.borrowTime ? dayjs(record.borrowTime) : null,
      expectedReturnTime: record.expectedReturnTime ? dayjs(record.expectedReturnTime) : null,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await borrowService.deleteBorrow(id);
      message.success('删除成功');
      fetchBorrows();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await borrowService.approveBorrow(id);
      message.success('审核通过');
      fetchBorrows();
    } catch (error) {
      message.error('审核失败');
    }
  };

  const handleReject = async (id: number) => {
    try {
      await borrowService.rejectBorrow(id);
      message.success('审核拒绝');
      fetchBorrows();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        borrowTime: values.borrowTime?.format('YYYY-MM-DD HH:mm:ss'),
        expectedReturnTime: values.expectedReturnTime?.format('YYYY-MM-DD HH:mm:ss'),
        status: editingBorrow ? editingBorrow.status : '待审核',
      };

      if (editingBorrow) {
        await borrowService.updateBorrow({ ...editingBorrow, ...submitData });
        message.success('更新成功');
      } else {
        await borrowService.createBorrow(submitData);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchBorrows();
    } catch (error) {
      message.error(editingBorrow ? '更新失败' : '创建失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchBorrows();
  };

  const handleReset = () => {
    setSearchParams({
      userName: '',
      bookName: '',
      status: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchBorrows, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '待审核':
        return 'orange';
      case '已借出':
        return 'blue';
      case '已归还':
        return 'green';
      case '已拒绝':
        return 'red';
      case '逾期':
        return 'red';
      default:
        return 'default';
    }
  };

  const isOverdue = (expectedReturnTime: string, status: string) => {
    if (status === '已归还') return false;
    return dayjs().isAfter(dayjs(expectedReturnTime));
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
    },
    {
      title: '图书名称',
      dataIndex: 'bookName',
      key: 'bookName',
      ellipsis: true,
    },
    {
      title: '借阅时间',
      dataIndex: 'borrowTime',
      key: 'borrowTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '预期归还时间',
      dataIndex: 'expectedReturnTime',
      key: 'expectedReturnTime',
      width: 150,
      render: (time: string, record: BookBorrow) => {
        const isLate = isOverdue(time, record.status);
        return (
          <span style={{ color: isLate ? '#ff4d4f' : undefined }}>
            {dayjs(time).format('YYYY-MM-DD')}
            {isLate && <Tag color="red" style={{ marginLeft: 8 }}>逾期</Tag>}
          </span>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: BookBorrow) => {
        const displayStatus = isOverdue(record.expectedReturnTime, status) && status === '已借出' ? '逾期' : status;
        return <Tag color={getStatusColor(displayStatus)}>{displayStatus}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_: any, record: BookBorrow) => (
        <Space size="small">
          {record.status === '待审核' && (
            <>
              <Tooltip title="审核通过">
                <Button
                  type="link"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleApprove(record.id)}
                  style={{ color: '#52c41a' }}
                />
              </Tooltip>
              <Tooltip title="审核拒绝">
                <Button
                  type="link"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => handleReject(record.id)}
                  style={{ color: '#ff4d4f' }}
                />
              </Tooltip>
            </>
          )}
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条借阅记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="总借阅数"
              value={statistics.total}
              prefix={<ReadOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="待审核"
              value={statistics.pending}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="已借出"
              value={statistics.borrowed}
              prefix={<BookOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="已归还"
              value={statistics.returned}
              prefix={<CheckOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="逾期未还"
              value={statistics.overdue}
              prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card>
            <Statistic
              title="已拒绝"
              value={statistics.rejected}
              prefix={<CloseOutlined style={{ color: '#8c8c8c' }} />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card
        title={
          <Space>
            <ReadOutlined />
            <Title level={4} style={{ margin: 0 }}>借阅管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchBorrows}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="导出数据">
              <Button icon={<ExportOutlined />} />
            </Tooltip>
          </Space>
        }
      >
        {/* 提示信息 */}
        <Alert
          message="借阅管理说明"
          description="管理图书借阅申请，包括审核、编辑和删除借阅记录。逾期未还的图书会自动标记为逾期状态。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 搜索区域 */}
        <Card
          size="small"
          style={{
            marginBottom: 16,
            background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
            border: '1px solid #b7eb8f'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="搜索用户姓名"
                value={searchParams.userName}
                onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}
                prefix={<UserOutlined />}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="搜索图书名称"
                value={searchParams.bookName}
                onChange={(e) => setSearchParams(prev => ({ ...prev, bookName: e.target.value }))}
                prefix={<BookOutlined />}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="选择状态"
                value={searchParams.status}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="待审核">
                  <Space>
                    <ClockCircleOutlined />
                    待审核
                  </Space>
                </Option>
                <Option value="已借出">
                  <Space>
                    <BookOutlined />
                    已借出
                  </Space>
                </Option>
                <Option value="已归还">
                  <Space>
                    <CheckOutlined />
                    已归还
                  </Space>
                </Option>
                <Option value="已拒绝">
                  <Space>
                    <CloseOutlined />
                    已拒绝
                  </Space>
                </Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Space style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Space>
              {hasPermission(PERMISSIONS.BORROW_APPROVE) && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  size="large"
                >
                  新增借阅
                </Button>
              )}
            </Space>
          </Col>
          <Col xs={24} sm={12} md={16} style={{ textAlign: 'right' }}>
            <Text type="secondary">
              共找到 {pagination.total} 条借阅记录
            </Text>
          </Col>
        </Row>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={borrows}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1200 }}
      />

      {/* 编辑/新增模态框 */}
      <Modal
        title={editingBorrow ? '编辑借阅记录' : '新增借阅记录'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="userId"
            label="借阅用户"
            rules={[{ required: true, message: '请选择借阅用户' }]}
          >
            <Select placeholder="请选择借阅用户" showSearch optionFilterProp="children">
              {(users || []).map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="bookId"
            label="借阅图书"
            rules={[{ required: true, message: '请选择借阅图书' }]}
          >
            <Select placeholder="请选择借阅图书" showSearch optionFilterProp="children">
              {(books || []).map(book => (
                <Option key={book.id} value={book.id}>
                  {book.name} - {book.author}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="borrowTime"
            label="借阅时间"
            rules={[{ required: true, message: '请选择借阅时间' }]}
          >
            <DatePicker
              showTime
              placeholder="请选择借阅时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          <Form.Item
            name="expectedReturnTime"
            label="预期归还时间"
            rules={[{ required: true, message: '请选择预期归还时间' }]}
          >
            <DatePicker
              placeholder="请选择预期归还时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Form>
      </Modal>
      </Card>
    </div>
  );
};

export default BorrowManagement;
