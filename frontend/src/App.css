/* 全局样式 */
.App {
  text-align: center;
}

/* 主题色彩变量 */
:root {
  --primary-color: #1890ff;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #262626;
  --text-secondary: #8c8c8c;
  --border-color: #d9d9d9;
  --background-color: #f0f2f5;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  --card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 侧边栏Logo */
.logo {
  height: 40px;
  margin: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.logo:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
  transform: translateY(-1px);
}

/* 头部样式 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info .ant-avatar {
  background: var(--primary-gradient);
  border: 2px solid rgba(24, 144, 255, 0.2);
}

/* 面包屑导航 */
.breadcrumb-container {
  margin: 16px 0;
  padding: 0 4px;
}

.breadcrumb-container .ant-breadcrumb {
  font-size: 14px;
}

/* 内容区域 */
.content-wrapper {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.content-wrapper:hover {
  box-shadow: var(--card-shadow-hover);
}

/* 登录页面样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 48px 40px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 420px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-title {
  text-align: center;
  margin-bottom: 40px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 1px;
  position: relative;
}

.login-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 2px;
}

/* 登录表单样式增强 */
.login-form .ant-form-item {
  margin-bottom: 24px;
}

.login-form .ant-input,
.login-form .ant-input-password,
.login-form .ant-select-selector {
  height: 48px;
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
  font-size: 16px;
}

.login-form .ant-input:focus,
.login-form .ant-input-password:focus,
.login-form .ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.login-form .ant-input-prefix {
  color: var(--text-secondary);
  font-size: 16px;
}

.login-form .ant-btn {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-form .ant-btn-primary {
  background: var(--primary-gradient);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.login-form .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.login-form .ant-btn-default {
  border: 2px solid #f0f0f0;
  color: var(--text-color);
}

.login-form .ant-btn-default:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* 表格样式增强 */
.ant-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  color: var(--text-color);
  padding: 16px;
}

.ant-table-tbody > tr > td {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(24, 144, 255, 0.02);
}

/* 卡片样式增强 */
.ant-card {
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-2px);
}

.ant-card-head {
  border-bottom: 2px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #f8f8f8 100%);
}

.ant-card-head-title {
  font-weight: 600;
  color: var(--text-color);
}

/* 按钮样式增强 */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: var(--primary-gradient);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.ant-btn-danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border: none;
}

.ant-btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 模态框样式增强 */
.ant-modal {
  border-radius: 12px;
  overflow: hidden;
}

.ant-modal-header {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-bottom: 2px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: var(--text-color);
}

/* 消息提示样式增强 */
.ant-message {
  top: 80px;
}

.ant-message-notice {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    padding: 20px;
  }

  .ant-table {
    font-size: 13px;
  }
}

@media (max-width: 992px) {
  .header-content {
    padding: 0 16px;
  }

  .header-content .user-info {
    gap: 8px;
  }

  .header-content .user-info span {
    display: none;
  }

  .breadcrumb-container {
    margin: 12px 0;
  }

  .content-wrapper {
    padding: 16px;
    margin: 8px;
  }
}

@media (max-width: 768px) {
  .login-form {
    width: 90%;
    margin: 20px;
    padding: 32px 24px;
  }

  .login-title {
    font-size: 24px;
  }

  .logo {
    height: 36px;
    font-size: 14px;
  }

  .header-content {
    padding: 0 12px;
  }

  .header-content .user-info {
    gap: 4px;
  }

  .breadcrumb-container {
    margin: 8px 0;
    padding: 0 2px;
  }

  .content-wrapper {
    margin: 4px;
    padding: 12px;
    border-radius: 8px;
  }

  .ant-table {
    font-size: 12px;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }

  .ant-card {
    margin-bottom: 12px;
  }

  .ant-card-head {
    padding: 0 12px;
    min-height: 48px;
  }

  .ant-card-body {
    padding: 12px;
  }

  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .ant-modal-body {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .login-form {
    width: 95%;
    margin: 10px;
    padding: 24px 16px;
  }

  .login-title {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .logo {
    height: 32px;
    font-size: 12px;
    margin: 12px;
  }

  .header-content {
    padding: 0 8px;
  }

  .content-wrapper {
    margin: 2px;
    padding: 8px;
    border-radius: 6px;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 2px;
    font-size: 11px;
  }

  .ant-btn {
    height: 36px;
    font-size: 13px;
  }

  .ant-input,
  .ant-input-password,
  .ant-select-selector {
    height: 40px;
  }

  .ant-card-head {
    padding: 0 8px;
    min-height: 44px;
  }

  .ant-card-body {
    padding: 8px;
  }

  .ant-modal {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }

  .ant-statistic-title {
    font-size: 12px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }
}

/* 横屏适配 */
@media (max-height: 600px) and (orientation: landscape) {
  .login-container {
    padding: 20px 0;
  }

  .login-form {
    padding: 24px 32px;
  }

  .login-title {
    margin-bottom: 20px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1600px) {
  .content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 32px;
  }

  .ant-table {
    font-size: 14px;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 16px;
  }
}
