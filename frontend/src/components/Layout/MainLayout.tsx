import React, { useState } from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>crumb, Avatar, Dropdown, Space, Button } from 'antd';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  DashboardOutlined,
  BookOutlined,
  TeamOutlined,
  ReadOutlined,
  PayCircleOutlined,
  MessageOutlined,
  HeartOutlined,
  SettingOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { MenuItem } from '../../types';
import Dashboard from '../../pages/Dashboard';
import UserManagement from '../../pages/UserManagement';
import BookCategoryManagement from '../../pages/BookCategoryManagement';
import BookManagement from '../../pages/BookManagement';
import BorrowManagement from '../../pages/BorrowManagement';
import ReturnManagement from '../../pages/ReturnManagement';
import FineManagement from '../../pages/FineManagement';
import MessageManagement from '../../pages/MessageManagement';
import FavoriteManagement from '../../pages/FavoriteManagement';
import NewsManagement from '../../pages/NewsManagement';
import ConfigManagement from '../../pages/ConfigManagement';

const { Header, Sider, Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems: MenuItem[] = [
    {
      key: '/dashboard',
      label: '仪表板',
      icon: <DashboardOutlined />,
      path: '/dashboard',
    },
    {
      key: 'user-management',
      label: '用户管理',
      icon: <TeamOutlined />,
      children: [
        {
          key: '/users',
          label: '用户列表',
          path: '/users',
        },
      ],
    },
    {
      key: 'book-management',
      label: '图书管理',
      icon: <BookOutlined />,
      children: [
        {
          key: '/book-categories',
          label: '图书分类',
          path: '/book-categories',
        },
        {
          key: '/books',
          label: '图书信息',
          path: '/books',
        },
      ],
    },
    {
      key: 'borrow-management',
      label: '借阅管理',
      icon: <ReadOutlined />,
      children: [
        {
          key: '/borrows',
          label: '图书借阅',
          path: '/borrows',
        },
        {
          key: '/returns',
          label: '图书归还',
          path: '/returns',
        },
      ],
    },
    {
      key: '/fines',
      label: '缴纳罚金',
      icon: <PayCircleOutlined />,
      path: '/fines',
    },
    {
      key: '/messages',
      label: '留言板管理',
      icon: <MessageOutlined />,
      path: '/messages',
    },
    {
      key: '/favorites',
      label: '我的收藏',
      icon: <HeartOutlined />,
      path: '/favorites',
    },
    {
      key: 'system-management',
      label: '系统管理',
      icon: <SettingOutlined />,
      children: [
        {
          key: '/news',
          label: '公告信息',
          path: '/news',
        },
        {
          key: '/config',
          label: '轮播图管理',
          path: '/config',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) return item;
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  const getCurrentMenuItem = (): MenuItem | null => {
    return findMenuItem(menuItems, location.pathname);
  };

  const getBreadcrumbItems = () => {
    const currentItem = getCurrentMenuItem();
    if (!currentItem) return [];

    const items = [{ title: '首页' }];
    
    // 查找父级菜单
    for (const item of menuItems) {
      if (item.children) {
        const found = item.children.find(child => child.key === location.pathname);
        if (found) {
          items.push({ title: item.label });
          items.push({ title: found.label });
          return items;
        }
      } else if (item.key === location.pathname) {
        items.push({ title: item.label });
        return items;
      }
    }

    return items;
  };

  const userMenuItems = [
    {
      key: 'profile',
      label: '个人信息',
      icon: <UserOutlined />,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: logout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: 'linear-gradient(180deg, #001529 0%, #002140 100%)',
          boxShadow: '2px 0 8px rgba(0, 0, 0, 0.15)',
        }}
      >
        <div className="logo">
          <BookOutlined style={{ marginRight: collapsed ? 0 : 8, fontSize: 20 }} />
          {collapsed ? '' : '图书馆管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            background: 'transparent',
            border: 'none',
          }}
        />
      </Sider>
      <Layout>
        <Header
          style={{
            padding: 0,
            background: 'linear-gradient(135deg, #fff 0%, #f8f9fa 100%)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            borderBottom: '1px solid #f0f0f0',
            zIndex: 10,
            position: 'relative'
          }}
        >
          <div className="header-content">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
                color: '#666',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.1)';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
                e.currentTarget.style.color = '#666';
              }}
            />
            <div className="user-info">
              <Space size="large">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  style={{
                    fontSize: '16px',
                    color: '#666',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = '#1890ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = '#666';
                  }}
                />
                <Dropdown
                  menu={{ items: userMenuItems }}
                  placement="bottomRight"
                  trigger={['click']}
                >
                  <Space
                    style={{
                      cursor: 'pointer',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(24, 144, 255, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'transparent';
                    }}
                  >
                    <Avatar
                      icon={<UserOutlined />}
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        border: '2px solid rgba(24, 144, 255, 0.2)'
                      }}
                    />
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                      <span style={{ fontWeight: 600, color: '#262626' }}>
                        {user?.name || user?.username}
                      </span>
                      <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
                        {user?.role === '管理员' ? '系统管理员' : '普通用户'}
                      </span>
                    </div>
                  </Space>
                </Dropdown>
              </Space>
            </div>
          </div>
        </Header>
        <Content style={{
          margin: '16px',
          background: 'transparent',
          minHeight: 'calc(100vh - 112px)'
        }}>
          <div className="breadcrumb-container">
            <Breadcrumb
              items={getBreadcrumbItems()}
              style={{
                padding: '8px 16px',
                background: 'rgba(255, 255, 255, 0.8)',
                borderRadius: '8px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
            />
          </div>
          <div className="content-wrapper">
            <Routes>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/users" element={<UserManagement />} />
              <Route path="/book-categories" element={<BookCategoryManagement />} />
              <Route path="/books" element={<BookManagement />} />
              <Route path="/borrows" element={<BorrowManagement />} />
              <Route path="/returns" element={<ReturnManagement />} />
              <Route path="/fines" element={<FineManagement />} />
              <Route path="/messages" element={<MessageManagement />} />
              <Route path="/favorites" element={<FavoriteManagement />} />
              <Route path="/news" element={<NewsManagement />} />
              <Route path="/config" element={<ConfigManagement />} />
            </Routes>
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
