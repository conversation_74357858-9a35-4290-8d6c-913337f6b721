import React from 'react';
import { message, notification, Modal } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';

// 消息提示类型
export type MessageType = 'success' | 'error' | 'warning' | 'info' | 'loading';

// 通知提示类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

// 确认对话框类型
export type ConfirmType = 'confirm' | 'info' | 'success' | 'error' | 'warning';

// 消息提示配置
interface MessageConfig {
  content: string;
  duration?: number;
  onClose?: () => void;
  key?: string;
}

// 通知提示配置
interface NotificationConfig {
  message: string;
  description?: string;
  duration?: number;
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
  onClose?: () => void;
  key?: string;
}

// 确认对话框配置
interface ConfirmConfig {
  title: string;
  content?: string;
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
  okButtonProps?: any;
  cancelButtonProps?: any;
}

class ActionFeedback {
  // 消息提示
  static message = {
    success: (config: string | MessageConfig) => {
      const options = typeof config === 'string' ? { content: config } : config;
      return message.success({
        content: options.content,
        duration: options.duration || 3,
        onClose: options.onClose,
        key: options.key,
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      });
    },

    error: (config: string | MessageConfig) => {
      const options = typeof config === 'string' ? { content: config } : config;
      return message.error({
        content: options.content,
        duration: options.duration || 4,
        onClose: options.onClose,
        key: options.key,
        icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      });
    },

    warning: (config: string | MessageConfig) => {
      const options = typeof config === 'string' ? { content: config } : config;
      return message.warning({
        content: options.content,
        duration: options.duration || 3,
        onClose: options.onClose,
        key: options.key,
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
      });
    },

    info: (config: string | MessageConfig) => {
      const options = typeof config === 'string' ? { content: config } : config;
      return message.info({
        content: options.content,
        duration: options.duration || 3,
        onClose: options.onClose,
        key: options.key,
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      });
    },

    loading: (config: string | MessageConfig) => {
      const options = typeof config === 'string' ? { content: config } : config;
      return message.loading({
        content: options.content,
        duration: options.duration || 0,
        onClose: options.onClose,
        key: options.key,
      });
    },

    destroy: (key?: string) => {
      if (key) {
        message.destroy(key);
      } else {
        message.destroy();
      }
    }
  };

  // 通知提示
  static notification = {
    success: (config: NotificationConfig) => {
      return notification.success({
        message: config.message,
        description: config.description,
        duration: config.duration || 4.5,
        placement: config.placement || 'topRight',
        onClose: config.onClose,
        key: config.key,
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      });
    },

    error: (config: NotificationConfig) => {
      return notification.error({
        message: config.message,
        description: config.description,
        duration: config.duration || 6,
        placement: config.placement || 'topRight',
        onClose: config.onClose,
        key: config.key,
        icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      });
    },

    warning: (config: NotificationConfig) => {
      return notification.warning({
        message: config.message,
        description: config.description,
        duration: config.duration || 4.5,
        placement: config.placement || 'topRight',
        onClose: config.onClose,
        key: config.key,
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
      });
    },

    info: (config: NotificationConfig) => {
      return notification.info({
        message: config.message,
        description: config.description,
        duration: config.duration || 4.5,
        placement: config.placement || 'topRight',
        onClose: config.onClose,
        key: config.key,
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      });
    },

    destroy: (key?: string) => {
      if (key) {
        notification.destroy(key);
      } else {
        notification.destroy();
      }
    }
  };

  // 确认对话框
  static confirm = {
    show: (config: ConfirmConfig) => {
      return Modal.confirm({
        title: config.title,
        content: config.content,
        onOk: config.onOk,
        onCancel: config.onCancel,
        okText: config.okText || '确定',
        cancelText: config.cancelText || '取消',
        okButtonProps: config.okButtonProps,
        cancelButtonProps: config.cancelButtonProps,
        icon: <ExclamationCircleOutlined />,
        centered: true,
      });
    },

    success: (config: ConfirmConfig) => {
      return Modal.success({
        title: config.title,
        content: config.content,
        onOk: config.onOk,
        okText: config.okText || '确定',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
        centered: true,
      });
    },

    error: (config: ConfirmConfig) => {
      return Modal.error({
        title: config.title,
        content: config.content,
        onOk: config.onOk,
        okText: config.okText || '确定',
        icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
        centered: true,
      });
    },

    warning: (config: ConfirmConfig) => {
      return Modal.warning({
        title: config.title,
        content: config.content,
        onOk: config.onOk,
        okText: config.okText || '确定',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
        centered: true,
      });
    },

    info: (config: ConfirmConfig) => {
      return Modal.info({
        title: config.title,
        content: config.content,
        onOk: config.onOk,
        okText: config.okText || '确定',
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
        centered: true,
      });
    }
  };

  // 操作成功反馈
  static success = (message: string, description?: string) => {
    if (description) {
      ActionFeedback.notification.success({ message, description });
    } else {
      ActionFeedback.message.success(message);
    }
  };

  // 操作失败反馈
  static error = (message: string, description?: string) => {
    if (description) {
      ActionFeedback.notification.error({ message, description });
    } else {
      ActionFeedback.message.error(message);
    }
  };

  // 操作警告反馈
  static warning = (message: string, description?: string) => {
    if (description) {
      ActionFeedback.notification.warning({ message, description });
    } else {
      ActionFeedback.message.warning(message);
    }
  };

  // 操作信息反馈
  static info = (message: string, description?: string) => {
    if (description) {
      ActionFeedback.notification.info({ message, description });
    } else {
      ActionFeedback.message.info(message);
    }
  };

  // 删除确认
  static confirmDelete = (config: Omit<ConfirmConfig, 'title'> & { itemName?: string }) => {
    return ActionFeedback.confirm.show({
      title: `确定要删除${config.itemName || '此项'}吗？`,
      content: '删除后将无法恢复，请谨慎操作。',
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      ...config,
    });
  };
}

export default ActionFeedback;
