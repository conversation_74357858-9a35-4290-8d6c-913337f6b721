import React from 'react';
import { Spin, Space, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  spinning?: boolean;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  indicator?: React.ReactElement;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip = '加载中...',
  spinning = true,
  children,
  style,
  indicator,
}) => {
  const customIndicator = indicator || (
    <LoadingOutlined
      style={{
        fontSize: size === 'large' ? 32 : size === 'small' ? 16 : 24,
        color: '#1890ff'
      }}
      spin
    />
  ) as React.ReactElement;

  if (children) {
    return (
      <Spin 
        spinning={spinning} 
        indicator={customIndicator}
        tip={tip}
        size={size}
        style={style}
      >
        {children}
      </Spin>
    );
  }

  return (
    <div 
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '40px 20px',
        ...style
      }}
    >
      <Space direction="vertical" align="center">
        <Spin 
          indicator={customIndicator}
          size={size}
        />
        {tip && (
          <Text type="secondary" style={{ marginTop: 8 }}>
            {tip}
          </Text>
        )}
      </Space>
    </div>
  );
};

// 页面级加载组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = '页面加载中...' }) => (
  <div style={{
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(4px)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  }}>
    <LoadingSpinner size="large" tip={tip} />
  </div>
);

// 内容加载组件
export const ContentLoading: React.FC<{ tip?: string }> = ({ tip = '内容加载中...' }) => (
  <div style={{
    minHeight: '200px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  }}>
    <LoadingSpinner tip={tip} />
  </div>
);

// 按钮加载组件
export const ButtonLoading: React.FC<{ size?: 'small' | 'default' }> = ({ size = 'small' }) => (
  <LoadingOutlined 
    style={{ 
      fontSize: size === 'small' ? 14 : 16,
      marginRight: 8
    }} 
    spin 
  />
);

export default LoadingSpinner;
