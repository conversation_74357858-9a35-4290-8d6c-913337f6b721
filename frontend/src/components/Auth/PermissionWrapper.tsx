import React, { ReactNode } from 'react';
import { Alert, Empty, Button } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionWrapperProps {
  children: ReactNode;
  permission?: string | string[];
  role?: string | string[];
  fallback?: ReactNode;
  showFallback?: boolean;
  fallbackType?: 'empty' | 'alert' | 'custom';
  fallbackMessage?: string;
}

// 权限常量定义
export const PERMISSIONS = {
  // 用户管理权限
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',

  // 图书管理权限
  BOOK_VIEW: 'book:view',
  BOOK_CREATE: 'book:create',
  BOOK_EDIT: 'book:edit',
  BOOK_DELETE: 'book:delete',

  // 借阅管理权限
  BORROW_VIEW: 'borrow:view',
  BORROW_APPROVE: 'borrow:approve',
  BORROW_RETURN: 'borrow:return',

  // 系统管理权限
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_LOG: 'system:log',
  SYSTEM_STATS: 'system:stats',

  // 消息管理权限
  MESSAGE_VIEW_ALL: 'message:view:all',
  MESSAGE_REPLY: 'message:reply',

  // 罚金管理权限
  FINE_VIEW: 'fine:view',
  FINE_MANAGE: 'fine:manage',
} as const;

// 角色权限映射
const ROLE_PERMISSIONS = {
  '管理员': [
    // 管理员拥有所有权限
    ...Object.values(PERMISSIONS)
  ],
  '用户': [
    // 普通用户只有基本查看权限
    PERMISSIONS.BOOK_VIEW,
    PERMISSIONS.BORROW_VIEW,
  ]
};

const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  role,
  fallback = null,
  showFallback = true,
  fallbackType = 'empty',
  fallbackMessage,
}) => {
  const { user } = useAuth();

  // 如果没有用户信息，不显示
  if (!user) {
    if (!showFallback) return null;
    return fallback || (
      <Empty
        image={<LockOutlined style={{ fontSize: 48, color: '#bfbfbf' }} />}
        description="请先登录"
      />
    );
  }

  // 检查角色权限
  const hasRolePermission = () => {
    if (!role) return true;

    const requiredRoles = Array.isArray(role) ? role : [role];
    return requiredRoles.includes(user.role);
  };

  // 检查具体权限
  const hasSpecificPermission = () => {
    if (!permission) return true;

    const requiredPermissions = Array.isArray(permission) ? permission : [permission];
    const userPermissions = ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [];

    return requiredPermissions.every(perm => userPermissions.includes(perm));
  };

  // 权限检查
  if (!hasRolePermission() || !hasSpecificPermission()) {
    if (!showFallback) return null;

    if (fallback) return <>{fallback}</>;

    const message = fallbackMessage || '您没有权限访问此功能';

    switch (fallbackType) {
      case 'alert':
        return (
          <Alert
            message="权限不足"
            description={message}
            type="warning"
            icon={<LockOutlined />}
            showIcon
            style={{ margin: '16px 0' }}
          />
        );
      case 'empty':
        return (
          <Empty
            image={<LockOutlined style={{ fontSize: 48, color: '#bfbfbf' }} />}
            description={
              <div>
                <div style={{ marginBottom: 8 }}>{message}</div>
                <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                  当前角色：{user.role}
                </div>
              </div>
            }
          >
            <Button type="primary" icon={<UserOutlined />}>
              联系管理员
            </Button>
          </Empty>
        );
      default:
        return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

// 权限检查 Hook
export const usePermission = () => {
  const { user } = useAuth();

  const hasPermission = (permission: string | string[]) => {
    if (!user) return false;

    const requiredPermissions = Array.isArray(permission) ? permission : [permission];
    const userPermissions = ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [];

    return requiredPermissions.every(perm => userPermissions.includes(perm));
  };

  const hasRole = (role: string | string[]) => {
    if (!user) return false;

    const requiredRoles = Array.isArray(role) ? role : [role];
    return requiredRoles.includes(user.role);
  };

  const isAdmin = () => hasRole('管理员');
  const isUser = () => hasRole('用户');

  return {
    hasPermission,
    hasRole,
    isAdmin,
    isUser,
    user,
    permissions: user ? ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [] : []
  };
};

export default PermissionWrapper;
